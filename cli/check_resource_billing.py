import argparse
import json
import sys

import kr8s

sys.path.append("/code")

from app.core.qingcloud.billing import QAIBillingService
from app.models.notebooks import Notebook
from app.models.trains import Train
from app.apps.notebooks.kr8s_objects.notebook import NoteBook as NBCR


def get_resource_by_id(resource_id):
    if resource_id.startswith("nb-"):
        return Notebook.one_by_id(resource_id)


result = []
err_result = []

nbs = NBCR.list(namespace=kr8s.ALL)
for nb in nbs:
    notebook = get_resource_by_id(nb.metadata.name)
    if not notebook:
        print(f"not support resource {nb.metadata.name}")
        continue
    lease_info = QAIBillingService().get_lease_info(notebook.uuid, notebook.user_id)
    try:
        if notebook.status == "Running" and lease_info["contract"].get("price_info").get("replicas") != 1:
            result.append({
                "notebook": str(notebook),
                "status": notebook.status,
                "lease_info": lease_info
            })
        if notebook.status in ["Suspended", "Suspending"] and lease_info["status"] == "active" and lease_info["contract"].get(
                "price_info").get("replicas") != 0:
            result.append({
                "notebook_id": str(notebook),
                "status": notebook.status,
                "lease_info": lease_info
            })
    except Exception as e:
        print("Error check notebook")
        err_result.append({
            "notebook": str(notebook),
            "status": notebook.status,
            "lease_info": lease_info
        })

print(json.dumps(result))
print(json.dumps(err_result))
