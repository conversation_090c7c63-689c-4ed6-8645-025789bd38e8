import argparse
import sys

sys.path.append("/code")

from app.core.qingcloud.billing import QAIBillingService
from app.models.notebooks import Notebook
from app.models.trains import Train


def get_resource_by_id(resource_id):
    if resource_id.startswith("nb-"):
        return Notebook.one_by_id(resource_id)
    elif resource_id.startswith("tn-"):
        return Train.one_by_id(resource_id)
    else:
        print("Not support resource type, Only support notebook and train")
        exit(1)


def get_billing_info(resource_id, owner):
    print(QAIBillingService().get_lease_info(resource_id, owner))


def suspend_billing(resource_id, owner):
    print(QAIBillingService().suspend(resource_id, owner))


def restart_billing(resource_id, owner):
    print(QAIBillingService().restart(resource_id, owner))


def unlease_billing(resource_id, owner):
    print(QAIBillingService().unlease(resource_id, owner))


def main():
    # parser = argparse.ArgumentParser(prog="extract-openapi.py")
    # parser.add_argument(
    #     "--action", help="Action to billing", required=True, type=str,
    #     choices=["suspend", "restart", "unlease", "get"],
    # )
    # parser.add_argument(
    #     "--id", help="notebook or train id", default="openapi.json", required=True, type=str,
    # )
    # args = parser.parse_args()
    operator = sys.argv[1]
    resource_id = sys.argv[2]

    resource = get_resource_by_id(resource_id)
    if not resource:
        print(f"Resource {resource_id} not found")
        exit(1)

    if operator == "get":
        get_billing_info(resource_id, resource.user_id)
    elif operator == "suspend":
        suspend_billing(resource_id, resource.user_id)
    elif operator == "restart":
        restart_billing(resource_id, resource.user_id)
    elif operator == "unlease":
        unlease_billing(resource_id, resource.user_id)
    else:
        print("unknown action")


if __name__ == "__main__":
    main()
