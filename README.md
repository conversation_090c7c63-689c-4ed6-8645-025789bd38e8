# Features
---

- notebook
- jobs
- user

# 配置

| 配置名称                                                                                      | 类型        | 默认值                                                   | 说明                         |
|-------------------------------------------------------------------------------------------|-----------|-------------------------------------------------------|----------------------------|
| ---------- 基础配置 ----------                                                                | --        | --                                                    | --                         |
| api_v1_prefix                                                                             | str       | "/aicp"                                               |                            |
| api_kapi_prefix                                                                           | str       | "/kapis/aicp.kubesphere.io/v1"                        |                            |
| debug                                                                                     | bool      | False                                                 | 开启debug默认, 生产环境请勿开启        |
| project_name                                                                              | str       | "AICP"                                                |                            |
| version                                                                                   | str       | "0.0.1"                                               |                            |
| description                                                                               | str       | "aicp - AI Cloud Platform"                            |                            |
| exclude_url                                                                               | list[str] | ["/healthz", "/aicp-docs", "/aicp-openapi.json"]      | 白名单列表, 已正则形式匹配, 匹配则不校验用户权限 |
| billing_enable                                                                            | bool      | False                                                 | 计费功能开关, 默认关闭               |
| ---------- 数据库 ----------                                                                 |
| DB_USERNAME                                                                               | str       | 无                                                     |                            |
| DB_PASSWORD                                                                               | str       | 无                                                     |                            |
| DB_HOST                                                                                   | str       | "pg-readwrite.aicp-storage.svc"                       |                            |
| DB_PORT                                                                                   | int       | 5432                                                  |                            |
| DB_DATABASE                                                                               | str       | "aicp"                                                |                            |
| DB_POOL_SIZE                                                                              | int       | 5                                                     |                            |
| DB_MAX_OVERFLOW                                                                           | int       | 10                                                    |                            |
| DB_POOL_RECYCLE                                                                           | int       | 3600                                                  |                            |
| DB_POOL_PRE_PING                                                                          | bool      | True                                                  |                            |
| ---------- 数据库 ----------                                                                 |
| REDIS_HOST                                                                                | str       | 无                                                     |                            |
| REDIS_PORT                                                                                | int       | 6379                                                  |                            |
| REDIS_PASSWORD                                                                            | str       | 无                                                     |                            |
| ---------- 数据库 ----------                                                                 |
| ELASTICSEARCH_HOST                                                                        | str       | "aicp-es-es-http.aicp-storage.svc.cluster.local:9200" |                            |
| ELASTICSEARCH_USER                                                                        | str       | 无                                                     |                            |
| ELASTICSEARCH_PASSWORD                                                                    | str       | 无                                                     |                            |
| ELASTICSEARCH_LOG_INDEX                                                                   | str       | "filebeat-*"                                          |                            |
| ELASTICSEARCH_KUBE_RESOURCE_INDEX                                                         | str       | "kube_resource"                                       |                            |
| ---------- 数据库 ----------                                                                 |
| QINGCLOUD_GPFS_ENABLED                                                                    | bool      | False                                                 |                            |
| QINGCLOUD_GPFS_DEBUG                                                                      | bool      | False                                                 |                            |
| QINGCLOUD_GPFS_SERVER                                                                     | str       | None                                                  |                            |
| QINGCLOUD_GPSE_ZONE                                                                       | str       | None                                                  |                            |
| QINGCLOUD_GPSE_FILESYSTEM                                                                 | str       | None                                                  |                            |
| QINGCLOUD_GPSE_VOLUME_HANDLE                                                              | str       | None                                                  |                            |
| ---------- 数据库 ----------                                                                 |
| AI_CLOUD_PUSH_SERVER                                                                      | str       | "https://hpc-ai.qingcloud.com/push/ms"                |                            |
| ---------- 数据库 ----------                                                                 |
| NOTEBOOK_HOST                                                                             | str       | 无                                                     |                            |
| NOTEBOOK_INIT_CONTAINER_IMAGE = "dockerhub.aicp.local/aicp-common/notebook-init-container | latest"   | 无                                                     |                            |
| TENSORBOARD_ENABLE                                                                        | bool      | False                                                 |                            |
| TENSORBOARD_URL                                                                           | str       | 无                                                     |                            |
| TENSORBOARD_MOUNT_PATH                                                                    | str       | "/logs/tensorboard"                                   |                            |
| ---------- 数据库 ----------                                                                 |
| MINIO_ENABLE                                                                              | bool      | False                                                 |                            |
| MINIO_PROTOCOL                                                                            | str       | "http"                                                |                            |
| MINIO_HOST                                                                                | str       | "minio.aicp-storage.svc.cluster.local"                |                            |
| MINIO_PORT                                                                                | int       | 9000                                                  |                            |
| MINIO_SECURE                                                                              | bool      | False                                                 |                            |
| MINIO_POLICY_TMP_DIR                                                                      | str       | None                                                  |                            |
| MINIO_ACCESS_KEY                                                                          | str       | None                                                  |                            |
| MINIO_SECRET_KEY                                                                          | str       | None                                                  |                            |
| DEFAULT_VOLUMES_CAPACITY                                                                  | str       | "1Gi"                                                 |                            |
| ---------- 数据库 ----------                                                                 |
| LOCAL_STORAGE_ENABLE                                                                      | bool      | False                                                 |                            |
| LOCAL_STORAGE_CLASS                                                                       | str       | "local-hostpath"                                      |                            |
| ---------- 数据库 ----------                                                                 |
| DOCKER_REGISTRY                                                                           | str       | "j1-dockerhub.qingcloud.com"                          |                            |
| DOCKER_ADMIN_USER                                                                         | str       | "admin"                                               |                            |
| DOCKER_ADMIN_PASSWORD                                                                     | str       | "zhu88jie"                                            |                            |
| ---------- 数据库 ----------                                                                 |
| TRAIN_CODE_ENABLE                                                                         | bool      | False                                                 |                            |
| TRAIN_CODE_MOUNT_PATH                                                                     | str       | "/root/code"                                          |                            |
| kfam_host                                                                                 | str       | "profiles-kfam.kubeflow.svc.cluster.local"            |                            |
| kfam_port                                                                                 | int       | 8081                                                  |                            |

# Development

## 数据库

数据库版本管理使用`alembic`进行管理,

生成数据库version文件

```sh
alembic revision --autogenerate -m "added user table"
```

升级数据库版本

```sh
alembic upgrade head
```

降级回滚一个版本

```sh
alembic downgrade -1
```

## 服务更新

以下内容适用于`qingcloud` 环境, 其他环境根据实际情况进行修改

构建 `docker` 镜像

```shell
docker build -t dockerhub.qingcloud.com/aicp_system/aicp-api-server:latest-dev .
```

推送镜像到 `dockerhub.qingcloud.com`

```shell
docker push dockerhub.qingcloud.com/aicp_system/aicp-api-server:latest-dev
````

环境更新 `aicp-web-app` 服务, 使用下述命令. 或者在kse界面中点击更多操作-重新创建

```shell
kubectl rollout restart deployment -n aicp-system aicp-web-app
```

## 本地开发

开发过程中使用`kt-connect` 的 `connect` 和 `mesh` 功能, 可以将本地网络加入到 `k8s` 的集群网络中, 并将特定的流量代理到本地.
从而实现本地开发.

参考安装 [ktctl](https://alibaba.github.io/kt-connect/#/zh-cn/guide/quickstart)

### 启动 `kt-connect connect` 代理

```bash
ktctl connect --namespace aicp-system
```

`kt-connect connect` 命令的功能是将本地网络加入到 `k8s` 集群网络中, 但是并不会将流量代理到本地.
此时本地可以访问到 `k8s` 集群中的服务, 但是k8s无法访问到本地的服务.

### 启动 `kt-connect mesh`

```bash
ktctl.exe --kubeconfig <kube_config_path> -n aicp-system mesh aicp-web-app --expose 5000:5000 --mode manual \
  --versionMark <versionMark> --nodeSelector kubernetes.io/hostname=master001
```

`kt-connect mesh` 命令的功能是将 `k8s` 集群中的服务代理到本地.

- expose 8000:8080 表示将 `aicp-system` 服务的 `5000` 端口代理到本地的 `5000` 端口, 注意第一个5000使用的是`svc`
  的targetPort, 而不是`pod`的端口.
- --versionMark <versionMark> 表示标记为`<versionMark>`的流量将转发到versionMark的版本中, ktctl会在服务上添加对应版本的代理.

启动成功后能够看到如下的输出

> Now you can access your service by header 'VERSION: <versionMark>'

### 转发用户流量

由于环境中没办法通过添加header进行流量识别, 只能够通过用户进行流量转发.

在`destenationrule`的`subsets`字段中添加个人版本:

```shell
kubectl edit dr -n aicp-system  aicp-web-app
```

![img.png](doc/image/img.png)

在`virtualservice`的`spec.http`字段中添加个人版本, 其中:

- `labels.version` 表示匹配流量的标签, 这里的`version`对应上面的`versionMark`标签.
- `name` 对应`ds`中的`subsets.name`字段.

```shell
kubectl edit vs -n aicp-system  aicp-web-app
```

![img.png](doc/image/vs.png)

其中:

- 注意istio的流量比对是逐一进行比对的, 没有优先级的概念, 所以个人版本尽可能放在前面.
- `match.headers.aicp-userid.exact` 表示匹配请求头中的`aicp-userid`字段, 并且值为`<userid>`的流量.
- `match.uri.prefix` 表示匹配请求的uri前缀, 值为`/aicp`的流量.
- `match.route.destination`, 表示匹配的流量会被转发到`destination`中的`subset`中. 这里的`subset`, 对应`ds`
  中的`subsets.name`字段.

此时发往`aicp-system` 的流量并会在请求头中添加 `VERSION: nvthg` 标识的流量.
会被代理到本地的 `8000` 端口, 而且其他流量并不受到影响.

### 清除代理

本地`ctrl + c` 停止`kt`代理即可

清理`vs`, `ds`里面的自定义规则. 



