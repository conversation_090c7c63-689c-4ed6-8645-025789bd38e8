import asyncio
from logging.config import fileConfig

from alembic import context
from sqlalchemy import engine_from_config, pool
from sqlalchemy.ext.asyncio import AsyncEngine
from sqlmodel import SQLModel

config = context.config

if config.config_file_name is not None:
    fileConfig(config.config_file_name)

target_metadata = SQLModel.metadata

target_metadata.naming_convention = {
    "ix": "ix_%(column_0_label)s",
    "uq": "uq_%(table_name)s_%(column_0_name)s",
    "ck": "ck_%(table_name)s_%(constraint_name)s",
    "fk": "fk_%(table_name)s_%(column_0_name)"
          "s_%(referred_table_name)s",
    "pk": "pk_%(table_name)s"
}

from app import settings

from app.models.trains import Train, ExportRecord  # noqa: 'autogenerate' support
from app.models.notebooks import Notebook, NotebookServices  # noqa: 'autogenerate' support
from app.models.resource_group import ResourceGroup  # noqa: 'autogenerate' support
from app.models.resource_group import ResourceNode  # noqa: 'autogenerate' support
from app.models.resource_group import ResourceGroupShare # noqa: 'autogenerate' support
from app.models.resource_group import ResourceTemplate  # noqa: 'autogenerate' support
from app.models.gpu import NodeStaticInfo, GpuStaticInfo, GpuMaintainLog, IbDevInfo, GpuDashboardConfig, GpuFaultRecords, GpuErrorCodes  # noqa: 'autogenerate' support
from app.models.operation_record import OperationRecord  # noqa: 'autogenerate' support
from app.models.resource_pool import  ResourcePool # noqa: 'autogenerate' support
from app.models.public_key import PublicKey # noqa: 'autogenerate' support
from app.models.data_set import DataSet # noqa: 'autogenerate' support
from app.core.ufm.models import UFMPkeys # noqa: 'autogenerate' support
from app.models.user import UserInfo # noqa: 'autogenerate' support
from app.models.bm import BmInstance, BmImage # noqa: 'autogenerate' support
from app.models.net import Subnet # noqa: 'autogenerate' support
from app.models.finetuning import Finetuning


# exclude_tables = loads(os.getenv("DB_EXCLUDE_TABLES"))
exclude_tables = []


def filter_db_objects(
        object,  # noqa: indirect usage
        name,
        type_,
        reflected,
        compare_to
):
    if type_ == "table":
        # Don’t generate any DROP TABLE directives with autogenerate
        if reflected and compare_to is None:
            return False
        return name not in exclude_tables

    if type_ == "index" and name.startswith("idx") and name.endswith("geom"):
        return False

    return True


def run_migrations_offline():
    url = settings.DB_ASYNC_CONNECTION_STR
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
        include_object=filter_db_objects
    )

    with context.begin_transaction():
        context.run_migrations()


def do_run_migrations(connection):
    context.configure(connection=connection, target_metadata=target_metadata)

    with context.begin_transaction():
        context.configure(
            connection=connection,
            target_metadata=target_metadata,
            include_object=filter_db_objects
        )
        context.run_migrations()


async def run_migrations_online():
    config_section = config.get_section(config.config_ini_section)
    url = settings.DB_ASYNC_CONNECTION_STR
    config_section["sqlalchemy.url"] = url

    connectable = AsyncEngine(
        engine_from_config(
            config_section,
            prefix="sqlalchemy.",
            poolclass=pool.NullPool,
            future=True,
        )
    )

    async with connectable.connect() as connection:
        await connection.run_sync(do_run_migrations)

    await connectable.dispose()


if context.is_offline_mode():
    run_migrations_offline()
else:
    asyncio.run(run_migrations_online())
