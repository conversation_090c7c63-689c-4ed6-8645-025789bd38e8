kind: StatefulSet
apiVersion: apps/v1
metadata:
  name: redis
  namespace: pitrix
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      nodeName: master1
      containers:
        - name: redis
          image: redis:7.2
          ports:
            - containerPort: 6379
              protocol: TCP
          resources:
            requests:
              memory: "1Gi"
              cpu: "1"
            limits:
              memory: "8Gi"
              cpu: "4"
          imagePullPolicy: IfNotPresent
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirst
      securityContext: {}
      schedulerName: default-scheduler
      nodeSelector:
        aicp.group/role: master
      tolerations:
        - key: node-role.kubernetes.io/control-plane
          operator: Exists
          effect: NoSchedule

---
kind: Service
apiVersion: v1
metadata:
  name: redis
  namespace: pitrix
spec:
  ports:
    - protocol: TCP
      port: 6379
      targetPort: 6379
  selector:
    app: redis
  type: ClusterIP