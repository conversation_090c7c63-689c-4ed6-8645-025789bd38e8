apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: aicp-role
rules:
  - apiGroups:
      - authorization.k8s.io
    resources:
      - subjectaccessreviews
    verbs:
      - create
  - apiGroups:
      - kubeflow.org
    resources:
      - notebooks
      - notebooks/finalizers
      - poddefaults
      - mpijobs
      - mxjobs
      - paddlejobs
      - pytorchjobs
      - tfjobs
      - xgboostjobs
      - experiments
      - poddefaults
      - profiles
      - pvcviewers
      - scheduledworkflows
      - suggestions
      - trials
      - viewers
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - update
  - apiGroups:
      - ""
    resources:
      - persistentvolumeclaims
      - persistentvolumes
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
  - apiGroups:
      - ""
    resources:
      - persistentvolumeclaims
      - persistentvolumes
      - secrets
      - events
      - namespaces
      - nodes
      - pods
      - pods/log
      - pods/exec
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
  - apiGroups:
      - storage.k8s.io
    resources:
      - storageclasses
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch
  - apiGroups:
      - tensorboard.kubeflow.org
    resources:
      - tensorboards
    verbs:
      - get
      - list
      - create
      - delete
      - patch
      - watch