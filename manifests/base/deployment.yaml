apiVersion: apps/v1
kind: Deployment
metadata:
  name: aicp-web-app
  labels:
    app: aicp-web-app
    version: production
spec:
  selector:
    matchLabels:
      app: aicp-web-app
      version: production
  replicas: 1
  template:
    metadata:
      labels:
        app: aicp-web-app
        version: production
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      containers:
        - name: aicp-web-app
          image: dockerhub.qingcloud.com/aicp_system/aicp-api-server:latest
          imagePullPolicy: Always
          ports:
            - containerPort: 5000
          envFrom:
            - configMapRef:
                name: aicp-web-app-parameters
          env:
            - name: TZ
              value: Asia/Shanghai
          volumeMounts:
            - name: alembic-version-pvc
              mountPath: /code/migration/versions
      serviceAccountName: aicp-service-account
      imagePullSecrets:
        - name: aicp-docker-images-pull
      volumes:
        - name: aicp-web-app-volume
          persistentVolumeClaim:
            claimName: alembic-version-pvc
            readOnly: false
