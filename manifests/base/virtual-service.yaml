apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: aicp-web-app
spec:
  gateways:
    - aicp-gateway
  hosts:
    - "*"
  http:
    - headers:
        request:
          add:
            x-forwarded-prefix: /aicp
      match:
        - uri:
            regex: ^\/(aicp|kapis).*
      route:
        - destination:
            host: aicp-web-app
            subset: production
      name: aicp-web-app
