apiVersion: apps/v1
kind: Deployment
metadata:
  name: aicp-update-job
  labels:
    app: aicp-update-job
spec:
  selector:
    matchLabels:
      app: aicp-update-job
  replicas: 1
  template:
    metadata:
      labels:
        app: aicp-update-job
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      volumes:
        - name: ssh-volume
          hostPath:
            path: /root/.ssh
            type: Directory
        - name: kubectl-volume
          hostPath:
            path: /usr/local/bin/kubectl
            type: File
      containers:
        - name: aicp-update-job
          image: dockerhub.qingcloud.com/aicp_system/aicp-api-server:latest
          workingDir: "/code/"
          command:
            - "python"
            - "/code/app/jobs/update_status.py"
          imagePullPolicy: Always
          envFrom:
            - configMapRef:
                name: aicp-web-app-parameters
          env:
            - name: TZ
              value: Asia/Shanghai
          volumeMounts:
            - name: ssh-volume
              mountPath: /root/.ssh
            - name: kubectl-volume
              mountPath: /usr/local/bin/kubectl
              subPath: kubectl
      serviceAccountName: aicp-service-account
      imagePullSecrets:
        - name: aicp-docker-images-pull

