# write a cron job that runs every 1 minutes, with command python3 app/jobs/cron_jobs/by_minutes.py
apiVersion: batch/v1
kind: CronJob
metadata:
  name: aicp-cron-job-by-minutes
  namespace: aicp-system
spec:
  failedJobsHistoryLimit: 1 # 保留最近5个失败的任务
  successfulJobsHistoryLimit: 1 # 不保留成功的任务
  schedule: "*/1 * * * *"
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      parallelism: 1  # 最多同时只有一个任务在执行
      completions: 1  # 一个任务执行完成后，结束该任务
      ttlSecondsAfterFinished: 300  # 任务完成后，300秒后删除
      backoffLimit: 1  # 任务失败后，重试1次
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          containers:
            - name: cron-job
              image: dockerhub.qingcloud.com/aicp_system/aicp-api-server:v2.0.0
              command: [ "python3", "/code/app/jobs/cron_jobs/by_minutes.py" ]
              workingDir: /code
              imagePullPolicy: Always
              envFrom:
                - configMapRef:
                    name: aicp-web-app-parameters
              env:
                - name: TZ
                  value: Asia/Shanghai
          restartPolicy: Never
          serviceAccountName: aicp-service-account

---
# write a cron job that runs every 1 day, with command python3 app/jobs/cron_jobs/cleanup.py
apiVersion: batch/v1
kind: CronJob
metadata:
  name: aicp-cron-job-by-day-clean-up
  namespace: aicp-system
spec:
  failedJobsHistoryLimit: 1 # 保留最近5个失败的任务
  successfulJobsHistoryLimit: 1 # 不保留成功的任务
  schedule: "* 0 * * *"  # 每天0点执行
  concurrencyPolicy: Forbid
  jobTemplate:
    spec:
      parallelism: 1  # 最多同时只有一个任务在执行
      completions: 1  # 一个任务执行完成后，结束该任务
      ttlSecondsAfterFinished: 300  # 任务完成后，300秒后删除
      backoffLimit: 1  # 任务失败后，重试1次
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          containers:
            - name: cron-job-clean-up
              image: dockerhub.qingcloud.com/aicp_system/aicp-api-server:v2.0.0
              command: [ "python3", "/code/app/jobs/cron_jobs/cleanup.py" ]
              workingDir: /code
              imagePullPolicy: Always
              envFrom:
                - configMapRef:
                    name: aicp-web-app-parameters
              env:
                - name: TZ
                  value: Asia/Shanghai
          restartPolicy: Never
          serviceAccountName: aicp-service-account

