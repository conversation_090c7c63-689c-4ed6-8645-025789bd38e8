apiVersion: v1
kind: Secret
metadata:
  name: minio-admin
  namespace: aicp-system
stringData:
  accessKeyID: "aicp"
  secretAccessKey: "2a10auQpFvxSjg85JrTg4Iw8X"
  endpoint: ""

---
kind: StorageClass
apiVersion: storage.k8s.io/v1
metadata:
  name: csi-s3
provisioner: ru.yandex.s3.csi
parameters:
  # specify which mounter to use
  # can be set to rclone, s3fs, goofys or s3backer
  mounter: s3fs
  # to use an existing bucket, specify it here:
  # bucket: some-existing-bucket
  csi.storage.k8s.io/provisioner-secret-name: minio-admin
  csi.storage.k8s.io/provisioner-secret-namespace: aicp-system
  csi.storage.k8s.io/controller-publish-secret-name: minio-admin
  csi.storage.k8s.io/controller-publish-secret-namespace: aicp-system
  csi.storage.k8s.io/node-stage-secret-name: minio-admin
  csi.storage.k8s.io/node-stage-secret-namespace: aicp-system
  csi.storage.k8s.io/node-publish-secret-name: minio-admin
  csi.storage.k8s.io/node-publish-secret-namespace: aicp-system

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: alembic-version-pvc
  namespace: aicp-system
spec:
  accessModes:
  - ReadWriteOnce
  resources:
    requests:
      storage: 1Gi
  storageClassName: csi-s3


