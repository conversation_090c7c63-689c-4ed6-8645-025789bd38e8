project_name=AICP
version=0.1.0
description=The API for AICP app.
api_v1_prefix=/aicp
debug=false
exclude_url=["/healthz","/aicp-docs","/aicp-openapi.json"]

# Auth
disable_auth=false


# Database
#DB_CONNECTION_STR=postgresql+psycopg2://aicp:<EMAIL>:5432/aicp

qingcloud_access_key_id=LTMJGBXPHSEZRNVKKPHU
qingcloud_secret_access_key=7GvVuGAx2iB8NA9n8NtczH8BJnTkDGwGm9N6DYBo
qingcloud_zone=testing1a
qingcloud_host=************
qingcloud_port=7777
qingcloud_protocol=http
default_console_id=testing1
default_regin_id=testing1a

QINGCLOUD_GPFS_DEBUG=true
QINGCLOUD_GPFS_SERVER=http://**************:32566/
QINGCLOUD_GPSE_ZONE=testing1a
QINGCLOUD_GPSE_FILESYSTEM=gpfs_share
QINGCLOUD_GPSE_VOLUME_HANDLE=0;2;3320938546841704143;1F3D1FAC:6566D9C5;;{file_set};/gpfs_share/{file_set}

NOTEBOOK_HOST=http://notebooks.testing.com/notebook/{namespace}/{name}/lab

# default volumes
MINIO_PROTOCOL=http
MINIO_HOST=aicp-oss.testing.com
MINIO_PORT=80
MINIO_POLICY_TMP_DIR=/tmp
MINIO_ACCESS_KEY=minioadmin
MINIO_SECRET_KEY=minioadmin

