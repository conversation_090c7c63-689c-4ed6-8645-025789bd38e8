apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization


sortOptions:
  order: legacy
  legacySortOptions:
    orderFirst:
      - ClusterRole
      - Role
      - ServiceAccount
      - ClusterRoleBinding
      - RoleBinding
      - Deployment
      - Service

resources:
  - alembic_version_pv.yaml
  - authorizationpolicy.yaml
  - cluster-role.yaml
  - cluster-role-binding.yaml
  - deployment.yaml
  - destination-rule.yaml
  - gateway.yaml
  - role.yaml
  - role-binding.yaml
  - secret.yaml
  - service.yaml
  - service-account.yaml
  - update-job-deployment.yaml
  - cronjobs.yaml
  - virtual-service.yaml


namespace: aicp-system
commonLabels:
  app: aicp-web-app
  kustomize.component: aicp-web-app

configMapGenerator:
  - envs:
      - params.env
    name: aicp-web-app-parameters

generatorOptions:
  disableNameSuffixHash: true

configurations:
  - params.yaml

images:
  - name: dockerhub.qingcloud.com/aicp_system/aicp-api-server
    newTag: v2.3.0.2