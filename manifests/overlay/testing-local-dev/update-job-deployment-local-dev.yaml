apiVersion: apps/v1
kind: Deployment
metadata:
  name: aicp-update-job
  namespace: aicp-system
  labels:
    app: aicp-update-job
spec:
  selector:
    matchLabels:
      app: aicp-update-job
  replicas: 1
  template:
    metadata:
      labels:
        app: aicp-update-job
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      nodeName: master
      containers:
        - name: aicp-update-job
          image: dockerhub.qingcloud.com/aicp_system/aicp-api-server:local-dev
          workingDir: "/code/"
          command:
            - "python"
            - "/code/app/jobs/update_status.py"
          imagePullPolicy: IfNotPresent
          envFrom:
            - configMapRef:
                name: aicp-web-app-parameters-local-dev-2ff4t4c975
          volumeMounts:
            - mountPath: /code
              name: code
      hostAliases:
        - hostnames:
            - aicp-oss.testing.com
          ip: ************
      serviceAccountName: aicp-service-account
      volumes:
        - hostPath:
            path: /root/aicp-api-server-test/
          name: code
