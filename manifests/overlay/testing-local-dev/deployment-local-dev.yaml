apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: aicp-web-app
    kustomize.component: aicp-web-app
    version: local-dev
  name: aicp-web-app-local-dev
  namespace: aicp-system
spec:
  replicas: 1
  selector:
    matchLabels:
      kustomize.component: aicp-web-app
      app: aicp-web-app
      version: local-dev
  template:
    metadata:
      annotations:
        sidecar.istio.io/inject: "true"
      labels:
        kustomize.component: aicp-web-app
        app: aicp-web-app
        version: local-dev
    spec:
      containers:
        - command:
            - uvicorn
            - app.main:application
            - --host
            - 0.0.0.0
            - --port
            - "5000"
          envFrom:
            - configMapRef:
                name: aicp-web-app-parameters-local-dev
          image: dockerhub.qingcloud.com/aicp_system/aicp-api-server:latest
          imagePullPolicy: IfNotPresent
          name: aicp-web-app
          ports:
            - containerPort: 5000
          volumeMounts:
            - mountPath: /code
              name: code
      hostAliases:
        - hostnames:
            - aicp-oss.testing.com
          ip: ************
      nodeName: master
      serviceAccountName: aicp-service-account
      volumes:
        - hostPath:
            path: /root/aicp-api-server-test/
          name: code