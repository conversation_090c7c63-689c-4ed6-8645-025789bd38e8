apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: aicp-web-app
spec:
  http:
    - match: # 定义路由规则
        - queryParams:
            version: # 匹配请求的user-agent
              exact: local-dev
      route:
        - destination:
            host: aicp-web-app
            subset: local-dev
      name: aicp-web-app-local-dev-by-query
    - match: # 定义路由规则
        - headers:
            version: # 匹配请求的user-agent
              exact: local-dev
      route:
        - destination:
            host: aicp-web-app
            subset: local-dev
      name: aicp-web-app-local-dev
    - headers:
        request:
          add:
            x-forwarded-prefix: /aicp
      route:
        - destination:
            host: aicp-web-app
            subset: production
      name: aicp-web-app
