project_name=AICP
version=0.1.0
description=The API for AICP app.
api_v1_prefix=/aicp
exclude_url=["/healthz","/aicp-docs","/aicp-openapi"]
debug=true

# Auth
disable_auth=false
billing_enable=false

# kfam
kfam_host=profiles-kfam.kubeflow.svc.cluster.local
kfam_port=8081

# Database
DB_CONNECTION_STR=postgresql+psycopg2://aicp:<EMAIL>:5432/postgres
DB_USERNAME=aicp
DB_PASSWORD=Zhu%4088jie123
DB_DATABASE=postgres

ELASTICSEARCH_HOST=https://aicp-es-es-internal-http.aicp-storage.svc.cluster.local:9200
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASSWORD=HIr56OvS2a2k09Lr38D9Iq4L

REDIS_HOST=redis.kubesphere-system.svc
REDIS_PASSWORD=fDmWFCjRcJtRcxq

qingcloud_access_key_id=RNVYQQVFYQIRIPXMCIWJ
qingcloud_secret_access_key=ec0tHNu2Ze1juB7wflR3cFbbVsqyECY2WIcqTBUG
qingcloud_zone=jinan1a
qingcloud_host=api.qingcloud.com
qingcloud_port=443
qingcloud_protocol=https
default_console_id=qingcloud
default_regin_id=jinan1

QINGCLOUD_GPFS_ENABLED=true
QINGCLOUD_GPFS_DEBUG=false
QINGCLOUD_GPFS_SERVER=https://hpc-ai.qingcloud.com/
QINGCLOUD_GPSE_ZONE=jinan1a
QINGCLOUD_GPSE_FILESYSTEM=gpfs
QINGCLOUD_GPSE_VOLUME_HANDLE=0;2;5569208176590380235;0200A8C0:65DEE660;;{file_set};/share/{file_set}

NOTEBOOK_HOST=https://hpc-ai.qingcloud.com/notebook/{namespace}/{name}/lab
NOTEBOOK_INIT_CONTAINER_IMAGE=registry.cn-beijing.aliyuncs.com/danchey/notebook-init-container:dev
TENSORBOARD_URL=https://hpc-ai.qingcloud.com/tensorboard/{namespace}/{name}/

# MINIO volumes
MINIO_PROTOCOL=http
MINIO_HOST=ainas.qingcloud.com
MINIO_PORT=80
MINIO_POLICY_TMP_DIR=/tmp
MINIO_ACCESS_KEY=aicp
MINIO_SECRET_KEY=2a10auQpFvxSjg85JrTg4Iw8X
MINIO_SECURE=false
MINIO_SC_ENDPOINT=http://*************:30900

LOCAL_PATH_STORAGE_MOUNT_PATHS=["/mnt/image"]
LOCAL_STORAGE_CLASS=local-hostpath

SSH_HOST=0


