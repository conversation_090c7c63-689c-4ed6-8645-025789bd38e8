
# Database
DB_CONNECTION_STR=postgresql+psycopg2://aicp:<EMAIL>:5432/postgres
DB_ASYNC_CONNECTION_STR=postgresql+asyncpg://aicp:<EMAIL>:5432/postgres

ELASTICSEARCH_HOST=https://aicp-es-es-internal-http.aicp-storage.svc.cluster.local:9200
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASSWORD=8O6rMCaH76M28ci3IO5r9q9O

REDIS_HOST=redis.kubesphere-system.svc
REDIS_PASSWORD=MbePGQDLNekbUaQ

qingcloud_access_key_id=ODVPZZTRWZXBCKIRIICO
qingcloud_secret_access_key=6JQpjPz52xjpvdT1vUqR6ZhrKuyPzSypKA6c5uSQ
qingcloud_zone=jinan1a
qingcloud_host=***********
qingcloud_port=7777
qingcloud_protocol=http
default_console_id=trcloud
default_regin_id=gz01


NOTEBOOK_HOST=http://ai.cloud.turing-gai.com.com/notebook/{namespace}/{name}/lab
TENSORBOARD_URL=http://ai.cloud.turing-gai.com.com/tensorboard/{namespace}/{name}/

# MINIO volumes
MINIO_PROTOCOL=http
MINIO_HOST=ainas.cloud.turing-gai.com
MINIO_PORT=80
MINIO_POLICY_TMP_DIR=/tmp
MINIO_ACCESS_KEY=aicp
MINIO_SECRET_KEY=2a10auQpFvxSjg85JrTg4Iw8X

