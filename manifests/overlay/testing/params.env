billing_enable=true

# Database
DB_CONNECTION_STR=postgresql+psycopg2://aicp:Hpcadmin123@************:5432/postgres
DB_ASYNC_CONNECTION_STR=postgresql+asyncpg://aicp:Hpcadmin123@************:5432/postgres

ELASTICSEARCH_HOST=https://aicp-es-es-internal-http.aicp-storage.svc.cluster.local:9200
ELASTICSEARCH_USER=elastic
ELASTICSEARCH_PASSWORD=8O6rMCaH76M28ci3IO5r9q9O

REDIS_HOST=redis.kubesphere-system.svc
REDIS_PASSWORD=MbePGQDLNekbUaQ

qingcloud_access_key_id=LTMJGBXPHSEZRNVKKPHU
qingcloud_secret_access_key=7GvVuGAx2iB8NA9n8NtczH8BJnTkDGwGm9N6DYBo
qingcloud_zone=testing1a
qingcloud_host=************
qingcloud_port=7777
qingcloud_protocol=http

QINGCLOUD_GPFS_DEBUG=true
QINGCLOUD_GPFS_SERVER=http://**************:32566/
QINGCLOUD_GPSE_ZONE=testing1a
QINGCLOUD_GPSE_FILESYSTEM=gpfs_share
QINGCLOUD_GPSE_VOLUME_HANDLE=0;2;3320938546841704143;1F3D1FAC:6566D9C5;;{file_set};/gpfs_share/{file_set}

NOTEBOOK_HOST=http://ai.testing.com/notebook/{namespace}/{name}/lab
TENSORBOARD_URL=http://ai.testing.com/tensorboard/{namespace}/{name}/

# default volumes
MINIO_PROTOCOL=http
MINIO_HOST=ainas.testing.com
MINIO_PORT=80
MINIO_POLICY_TMP_DIR=/tmp
MINIO_ACCESS_KEY=aicp
MINIO_SECRET_KEY=2a10auQpFvxSjg85JrTg4Iw8X

