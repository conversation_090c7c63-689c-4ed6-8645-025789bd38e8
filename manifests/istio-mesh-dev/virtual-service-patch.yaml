apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: aicp-web-app
spec:
  host: aicp-web-app.aicp-system.svc.cluster.local
  trafficPolicy:
    tls:
      mode: ISTIO_MUTUAL
  subsets:
    - name: production
      labels:
        version: production
    - name: danchey-local-dev
      labels:
        version: danchey-local-dev

---
apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: aicp-web-app
spec:
  gateways:
    - aicp-gateway
  hosts:
    - "*"
  http:
    # 本地开发规则
    - match: # 定义路由规则
        - headers:
            aicp-userid: # 匹配请求的user-agent
              exact: usr-WJ2gdXUg
          uri:
            prefix: /aicp
      route:
        - destination:
            host: aicp-web-app.aicp-system.svc.cluster.local
            subset: danchey-local-dev
      name: aicp-web-app-danchey-local-dev
    # 线上环境规则
    - headers:
        request:
          add:
            x-forwarded-prefix: /aicp
      match:
        - uri:
            prefix: /aicp
      route:
        - destination:
            host: aicp-web-app
            subset: production
      name: aicp-web-app
