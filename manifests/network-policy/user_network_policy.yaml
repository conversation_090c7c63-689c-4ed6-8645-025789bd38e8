apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: user-network-policy
  namespace: usr-du7zvgq1
spec:
  podSelector: {}
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector: {}
    - namespaceSelector:
        matchLabels:
          kubernetes.io/metadata.name: istio-system
  - from:
      - podSelector: {}
      - namespaceSelector:
            matchLabels:
                kubernetes.io/metadata.name: kube-system
  egress:
  - to:
    - podSelector: {}
  - to:
    - ipBlock:
        cidr: 0.0.0.0/0

#所有用户的namespace需要应用此网络策略，允许namespace内部pod互相访问，并且允许pod访问node和外部流量(访问公网)，不允许其他namespace中的pod访问