FROM hub.kubesphere.com.cn/aicp/aicp-python:3.9.18-mc as builder

ARG TARGETARCH

WORKDIR /code

COPY ./requirements.txt /code/requirements.txt

RUN pip install --no-cache-dir --upgrade -r /code/requirements.txt -i https://mirrors.aliyun.com/pypi/simple

COPY . ./

EXPOSE 5000

ARG CACHEBUST=1

RUN python3 /code/app/extract_openapi --out /tmp/openapi.json


FROM scratch AS export-stage
COPY --from=builder /tmp/openapi.json .
