stages:
  - login
  - build
  - sonarqube-check
  - post-build
  - notify

variables:
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
  GIT_DEPTH: "0"

login-docker:
  stage: login
  only:
    - tags
  script:
    - echo "Logging in to hub.kubesphere.com.cn"
    - docker login -u $CI_KUBESPHERE_REGISTRY_USER -p $CI_KUBESPHERE_REGISTRY_PASSWORD hub.kubesphere.com.cn


build-aicp:
  stage: build
  only:
    - tags
  script:
    - echo "Building aicp-api-server image by tag $CI_COMMIT_TAG started"
    - make build-push-multi-arch TAG=$CI_COMMIT_TAG
  dependencies:
    - login-docker

build-aicp-dev:
  stage: build
  only:
    - dev
  script:
    - echo "Building aicp-api-server-dev image by branch dev"
    - make build-push-amd64-arch TAG=dev
  dependencies:
    - login-docker

build-aicp-binary:
  stage: build
  only:
    - code-compile
  script:
    - echo "Building aicp-api-server-dev image by branch dev"
    - make build-push-amd64-arch TAG=v2.24.0-binary
  dependencies:
    - login-docker

#remove build api doc
#build-api-doc:
#  stage: post-build
#  script:
#    - echo "Building aicp-api-doc image by tag $CI_COMMIT_TAG started"
#    - make build-api-docs GIT_BRANCH=$CI_COMMIT_REF_NAME

sonar-scanner:
  stage: post-build
  image:
    name: sonarsource/sonar-scanner-cli:latest
    entrypoint: [ "" ]
  variables:
    SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"  # Defines the location of the analysis task cache
    GIT_DEPTH: "0"  # Tells git to fetch all the branches of the project, required by the analysis task
  cache:
    key: "${CI_JOB_NAME}"
    paths:
      - .sonar/cache
  script:
    - sonar-scanner
  allow_failure: true
  only:
    - branches
    - merge_requests
  except:
    - dev
    - release
  when: always

sonar-send-email:
  stage: notify
  script:
    - echo "Sending email"
    - python3 /opt/sonarqube_api.py $sonarProjectId $CI_COMMIT_REF_NAME $GITLAB_USER_EMAIL
  only:
    - branches
  except:
    - dev
    - release
  when: on_success
  dependencies:
    - sonar-scanner
