# Makefile for building and pushing multi-architecture Docker images

# Default values
REPO ?= hub.kubesphere.com.cn/aicp/aicp-api-server
ARCHS ?= linux/amd64,linux/arm64

# Docker buildx builder name
BUILDER_NAME ?= multiarch-builder

# Determine the tag
GIT_TAG := $(shell git describe --tags --exact-match 2>/dev/null)
GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD)
GIT_SHA := $(shell git rev-parse --short HEAD)
TAG ?= $(if $(GIT_TAG),$(GIT_TAG),$(GIT_BRANCH)-$(GIT_SHA))

# api docs server
API_DOCS_SERVER ?= 60.216.39.135
API_DOCS_PORT ?= 54022
API_DOCS_PATH ?= /mnt/ftp/aicp-docs

# Cache image
CACHE_IMAGE ?= $(REPO):cache

CACHEBUST=1

# Default target
.PHONY: default
default: help

# Help target
.PHONY: help
help:
	@echo "Makefile targets:"
	@echo "  create-builder         - Create a new builder instance if it doesn't exist."
	@echo "  build                  - Build the Docker image."
	@echo "  build-push             - Build and push the Docker image."
	@echo "  build-multi-arch       - Build the Docker image for multiple architectures."
	@echo "  build-push-multi-arch  - Build and push the Docker image for multiple architectures."
	@echo "  clean                  - Clean up the builder instance."

# Create a new builder instance if it doesn't exist
.PHONY: create-builder
create-builder:
	@if ! docker buildx inspect $(BUILDER_NAME) > /dev/null 2>&1; then \
		docker buildx create --name $(BUILDER_NAME) --use; \
		docker buildx inspect $(BUILDER_NAME) --bootstrap; \
	fi

# Build the Docker image for multiple architectures
.PHONY: build
build:
	docker build --cache-from $(CACHE_IMAGE) -t $(REPO):$(TAG) .

# Build and push the Docker image for multiple architectures
.PHONY: build-push
build-push:
	docker build --cache-from $(CACHE_IMAGE) -t $(REPO):$(TAG) --push .


# Build the Docker image for multiple architectures
.PHONY: build-multi-arch
build-multi-arch: create-builder
	docker buildx build --cache-from $(CACHE_IMAGE) --platform $(ARCHS) -t $(REPO):$(TAG) .

# Build and push the Docker image for multiple architectures
.PHONY: build-push-multi-arch
build-push-multi-arch: create-builder
	docker buildx build --cache-from $(CACHE_IMAGE) --platform $(ARCHS) -t $(REPO):$(TAG) --push .

# Build and push the Docker image for amd64 architectures
.PHONY: build-push-amd64-arch
build-push-amd64-arch: create-builder
	docker buildx build --cache-from $(CACHE_IMAGE) --platform linux/amd64 -t $(REPO):$(TAG) --push .

# build and push personal docker image for kongyao (hub.kubesphere.com.cn/aicp/aicp-api-server-ky:danchey)
.PHONY: build-ky
build-ky:
	docker build --cache-from $(CACHE_IMAGE) -t hub.kubesphere.com.cn/aicp/aicp-api-server-ky:danchey --push .

.PHONY: build-api-docs
build-api-docs:
	DOCKER_BUILDKIT=1 docker build --progress=plain --cache-from $(CACHE_IMAGE) --build-arg CACHEBUST=$(CACHEBUST) --file Dockerfile.export_swagger --output out .
	scp -P $(API_DOCS_PORT)  out/openapi.json root@$(API_DOCS_SERVER):$(API_DOCS_PATH)/aicp-$(GIT_BRANCH).json

# Clean up the builder instance
.PHONY: clean
clean:
	docker buildx rm $(BUILDER_NAME)



