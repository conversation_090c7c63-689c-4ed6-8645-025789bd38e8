from app.core.exceptions import AICPBaseException


class TrainDeleteException(AICPBaseException):
    BASE_CODE = 1001
    BASE_MESSAGE = "删除训练作业失败"


class TrainCreateException(AICPBaseException):
    BASE_CODE = 1002
    BASE_MESSAGE = "创建训练作业失败"


class TrainNameAlreadyExistsException(AICPBaseException):
    BASE_CODE = 1003
    BASE_MESSAGE = "训练作业名称已存在"


class VolumeCreateException(AICPBaseException):
    BASE_CODE = 1004
    BASE_MESSAGE = "挂载数据集失败"


class CreateCodePathException(AICPBaseException):
    BASE_CODE = 1005
    BASE_MESSAGE = "创建代码上传路径失败"


class UploadPathException(AICPBaseException):
    BASE_CODE = 1006
    BASE_MESSAGE = "无法处理的文件路径路径"


class TensorboardHasNotPermissionException(AICPBaseException):
    BASE_CODE = 1007
    BASE_MESSAGE = "当前账号没有其它账号的Tensorboard权限"
