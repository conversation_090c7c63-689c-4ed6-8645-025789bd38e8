from typing import Dict, List, Optional

import kr8s
from kr8s.objects import APIObject


# noinspection PyClassHasNoInit
class TensorBoard(APIObject):
    """NoteBook is the definition of a notebooks.
    """

    version = "tensorboard.kubeflow.org/v1alpha1"
    endpoint = "tensorboards"
    kind = "Tensorboard"
    plural = "tensorboards"
    singular = "tensorboard"
    namespaced = True

    @classmethod
    def get_resource_definition(cls, namespace: str, pvcs: List[str]) -> Dict:
        """
        获取资源定义
        """
        template = {
            "apiVersion": cls.version,
            "kind": cls.kind,
            "metadata": {
                "name": "tensorboard",
                "namespace": namespace,
            },
            "spec": {
                "logspath": pvcs
            }
        }

        return template

    @classmethod
    def get_tensorboard(cls, namespace: str) -> Optional["TensorBoard"]:
        """
        获取资源定义
        """
        tb = kr8s.get(cls.endpoint, "tensorboard", namespace=namespace)
        if not tb:
            return None

        return tb[0]
