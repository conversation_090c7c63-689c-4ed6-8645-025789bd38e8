# from typing import Dict, List
#
# from fastapi import APIRouter, Depends, Query, Request
# from kr8s import NotFoundError
#
# from .common import parse_tensorboard
# from .exceptions import TensorboardHasNotPermissionException
# from .kr8s_objects.tensorboard import TensorBoard
# from app import logger
# from app.depends.authz import has_permission, interface_auth_check
# from app.core.response import GenericSingleResponse
# from ..notebooks.dependencies import get_notebooks_crud
# from ..trains.dependencies import get_train_crud
# from ...core.constant import UserFrom
# from ...core.kube.api.kfam import KfamClient
# from ...core.models import QingcloudUser
# from ...core.qingcloud.interface import describe_access_key_by_user_id, describe_user
# from ...cruds.notebooks import NotebookCRUD
# from ...cruds.trains import TrainCRUD
#
# router = APIRouter(prefix="/tensorboards", tags=["tensorboards"])
#
#
# @router.get("/namespaces/{namespace}/link", response_model=GenericSingleResponse[Dict], deprecated=True)
# def create_tensorboard(
#         request: Request,
#         namespace: str,
#         uids: List[str] = Query(..., description="训练作业ID列表, 或者容器实例的ID列表"),
#         notebook_session: NotebookCRUD = Depends(get_notebooks_crud),
#         train_session: TrainCRUD = Depends(get_train_crud)
# ):
#     """
#     创建训练作业
#     """
#     # init container 用于log_path数据的准备
#     # 挂载 pvc 到 init container
#     # ln -s /mnt/data /mnt/log_path
#     # 获取资源对应的namespace
#     if uids[0].split('-')[0] == "nb":
#         namespace = notebook_session.get_by_uuid(uids[0]).namespace
#     else:
#         namespace = train_session.get_by_uuid(uids[0]).namespace
#     # 鉴权
#     namespace = interface_auth_check(request, "NB" if uids[0].split('-')[0] == "nb" else "TN", namespace)
#     belong_user = request.user
#     try:
#         if belong_user.user_id.lower() != namespace:
#             belong_user_id = KfamClient(belong_user.user_id).get_shared_namespace_user_id(namespace)
#             belong_user = QingcloudUser(
#                 **{
#                     **describe_user(belong_user_id).get("user_set")[0],
#                     **describe_access_key_by_user_id(belong_user_id).get("access_key_set")[0]
#                 },
#                 user_from=UserFrom.CONSOLE)
#     except Exception as e:
#         raise TensorboardHasNotPermissionException()
#     umio = UserMinio(belong_user, namespace, )
#     pvcs = [f"pvc://{umio.pvc_name}/{logs_path}" for logs_path in uids]
#     tb: TensorBoard = TensorBoard.get_tensorboard(namespace)
#     if tb:
#         logger.info("tensorboard exists, update logspath")
#         pre_logspath = set(tb.raw["spec"]["logspath"])
#         now_logspath = set(pvcs)
#         if pre_logspath != now_logspath:
#             tb.patch({"spec": {"logspath": list(now_logspath)}})
#     else:
#         logger.info("create tensorboard")
#         tbd = TensorBoard.get_resource_definition(namespace, pvcs)
#         tb = TensorBoard(tbd)
#         tb.create()
#
#     data = parse_tensorboard(tb.raw)
#     return GenericSingleResponse[Dict](data=data)
#
#
# @router.get("/namespaces/{namespace}/status", response_model=GenericSingleResponse[Dict])
# def get_tensorboard_status(
#         request: Request,
#         namespace: str,
#         uids: List[str] = Query(..., description="训练作业ID列表, 或者容器实例的ID列表"),
#         notebook_session: NotebookCRUD = Depends(get_notebooks_crud),
#         train_session: TrainCRUD = Depends(get_train_crud)
# ):
#     """
#     获取tensorboard的状态
#     """
#     # 获取资源对应的namespace
#     if uids[0].split('-')[0] == "nb":
#         namespace = notebook_session.get_by_uuid(uids[0]).namespace
#     else:
#         namespace = train_session.get_by_uuid(uids[0]).namespace
#     # 鉴权
#     namespace = interface_auth_check(request, "NB" if uids[0].split('-')[0] == "nb" else "TN", namespace)
#     tb = TensorBoard.get_tensorboard(namespace)
#     if tb is None:
#         return NotFoundError("Tensorboard not found")
#     data = parse_tensorboard(tb.raw)
#     return GenericSingleResponse[Dict](data=data)
