import app.core.kube.api.utils
import app
from app import logger


def parse_tensorboard(tensorboard):
    """
    Process the Tensorboard object and format it as the UI expects it.
    """
    logger.debug(f"parsing tensorboard: {tensorboard}")
    if tensorboard.get("status", {}).get("readyReplicas", 0) == 1 and \
            tensorboard.get("status", {}).get("conditions", [{}])[-1].get("deploymentState") == "Available":
        phase = app.core.kube.api.utils.STATUS_PHASE.Running
        message = "The Tensorboard server is ready to connect"
    else:
        phase = app.core.kube.api.utils.STATUS_PHASE.Unavailable
        message = "The Tensorboard server is currently unavailble"

    parsed_tensorboard = {
        "name": tensorboard["metadata"]["name"],
        "namespace": tensorboard["metadata"]["namespace"],
        "logspath": tensorboard["spec"]["logspath"],
        "age": tensorboard["metadata"]["creationTimestamp"],
        "status": app.core.kube.api.utils.create_status(phase, message, ""),
        "url": app.settings.TENSORBOARD_URL.format(namespace=tensorboard["metadata"]["namespace"],
                                               name=tensorboard["metadata"]["name"])
    }

    return parsed_tensorboard
