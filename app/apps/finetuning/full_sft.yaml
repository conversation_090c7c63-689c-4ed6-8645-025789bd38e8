### model
model_name_or_path: {model_name_or_path}
trust_remote_code: true

### method
stage: sft
do_train: {do_train}
finetuning_type: full
deepspeed: {deepspeed} # choices: [ds_z0_config.json, ds_z2_config.json, ds_z3_config.json]

### dataset
dataset: {dataset}
template: {template}
cutoff_len: {cutoff_len}
max_samples:
overwrite_cache: true
preprocessing_num_workers: 16
dataloader_num_workers: 4

### output
output_dir: {output_dir}
logging_steps: 10
save_steps: {save_steps}
plot_loss: true
overwrite_output_dir: true
save_only_model: false
report_to: none  # choices: [none, wandb, tensorboard, swanlab, mlflow]

### train
per_device_train_batch_size: {per_device_train_batch_size}
gradient_accumulation_steps: {gradient_accumulation_steps}
learning_rate: {learning_rate}
num_train_epochs: {num_train_epochs}
lr_scheduler_type: {lr_scheduler_type}
warmup_ratio: 0.1
bf16: {bf16}
ddp_timeout: 180000000
resume_from_checkpoint: null

### eval
eval_dataset: {eval_dataset}
val_size: {val_size}
per_device_eval_batch_size: 1
eval_strategy: steps
eval_steps: {eval_steps}