import json
import os.path
from typing import List

from fastapi import APIRouter, BackgroundTasks
from fastapi import Depends, Request
import app
from app import logger
from app.apps.finetuning.dependencies import get_finetuning_crud
from app.apps.finetuning.exceptions import ModelNotFound, DataNotFound
from app.apps.trains.dependencies import get_train_crud
from app.core.response import BaseGenericResponse, GenericSingleResponse
from app.cruds.finetuning import FinetuningCrud
from app.cruds.trains import TrainCRUD
from app.models.finetuning import FinetuningParams, Finetuning, Dataset

router = APIRouter(prefix="/finetuning", tags=["finetuning"])


@router.post("/data/check", response_model=BaseGenericResponse)
def check_data_set(request: Request, req: Dataset):
    rep = BaseGenericResponse()
    datasets = []
    for data_path in req.dataset:
        full_data_path = os.path.join(app.settings.QINGCLOUD_GPSE_HOSTPATH_BACKEND_HOSTPATH, data_path.strip("/"))
        if not os.path.exists(full_data_path):
            raise DataNotFound(message="数据集{}不存在".format(data_path))
        if os.path.isdir(full_data_path):
            files = os.listdir(full_data_path)
            for file in files:
                if os.path.isfile(os.path.join(full_data_path, file)):
                    datasets.append(os.path.join(full_data_path, file))
        else:
            datasets = [full_data_path]
    if len(datasets) > 10:
        rep.ret_code = -1
        rep.message = f"数据集文件数量不能超过10"
        return rep
    if len(datasets) == 0:
        raise DataNotFound(message="数据集{}不存在".format(req.dataset))
    for data in datasets:
        if os.path.getsize(data) > 1024*1024*200:
            rep.ret_code = -1
            rep.message = f"单个数据集最大不能超过200M"
            return rep
        with open(data, "r") as f:
            try:
                content = json.loads(f.read())
                for item in content:
                    _ = item["instruction"]
                    _ = item["input"]
                    _ = item["output"]
            except Exception as e:
                logger.error(e)
                rep.ret_code = -1
                rep.message = f"{data}数据格式不正确"
                return rep
    return rep


@router.post("", response_model=GenericSingleResponse[Finetuning])
def run_finetuning(
    request: Request,
    finetuning_params: FinetuningParams,
    background_tasks: BackgroundTasks,
    crud: FinetuningCrud = Depends(get_finetuning_crud),
    train: TrainCRUD = Depends(get_train_crud)
):
    finetuning = crud.create_finetuning(finetuning_params, train, background_tasks)
    return GenericSingleResponse[Finetuning](data=finetuning)


@router.get("")
def get_finetuning(
        crud: FinetuningCrud = Depends(get_finetuning_crud)):
    pass



@router.post("/stop")
def stop_finetuning(
        crud: FinetuningCrud = Depends(get_finetuning_crud)):
    pass


@router.delete("")
def delete_finetuning(
        crud: FinetuningCrud = Depends(get_finetuning_crud)):
    pass