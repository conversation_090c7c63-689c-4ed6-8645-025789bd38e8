import json
from datetime import datetime
from typing import Dict, List, Optional, Sequence

import requests
from fastapi import APIRouter, BackgroundTasks, Depends, Query, Request, status as http_status

import app
from app import logger
from app.depends import is_admin
from app.apps.notebooks.dependencies import get_notebooks_crud
from app.apps.resource_group.dependencies import get_resource_crud
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.resource import ProductCenterResource
from app.core.response import GenericMultipleResponse, GenericSingleResponse
from app.core.ufm.crud import UFMInfoCrud
from app.core.ufm.dependencies import get_ufm_crud
from app.cruds.billing import AdminCRUD
from app.cruds.notebooks import NotebookCRUD
from app.cruds.resource_group import ResourceGroupCrud
from app.cruds.trains import TrainCRUD
from app.depends import is_admin
from app.models.notebooks import NoteBookUUIDPrefix
from app.models.resource_group import resource_group_prefix, resource_node_prefix
from app.models.trains import TrainUUIDPrefix
from .dependencies import get_admin_crud
from .exceptions import NotSupportActionTypeException, NotSupportResourceTypeException
from .utils import BillingAction, replace_to_utc_str
from ...core.models import STATUS_PHASE
from ...cruds.finetuning import FinetuningUUIDPrefix
from ...models.operation_record import OperationRecord

# api prefix: /aicp/user
router = APIRouter(prefix="/billing", tags=["billing"])

get_prefix = lambda x: x.split("-")[0] + "-"

inference_resource_prefix = "inf-"


@router.get(
    "/action",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK
)
def action(
        request: Request,
        background_tasks: BackgroundTasks,
        action_type: str = Query(...,
                                 description="操作类型, 支持describe, terminate, suspend, resume, 其中suspend, resume仅支持容器实例"),
        resource_ids: List[str] = Query(..., min_length=1, description="资源id列表, qai-<resource_id>"),
        user_id: str = Query(..., description="资源拥有者"),
        zone_id: str = Query(..., description="资源所在区域"),
        console_id: str = Query(..., description="资源所在控制台"),
        auth: bool = Depends(is_admin),
        ufm_db: UFMInfoCrud = Depends(get_ufm_crud),
        nb_db: NotebookCRUD = Depends(get_notebooks_crud),
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        session: AdminCRUD = Depends(get_admin_crud)
):
    """
    提供给计费侧的调用接口, 仅 billing 账号使用.
    see: https://cwiki.yunify.com/pages/viewpage.action?pageId=137055724
    """
    crud_map = {
        TrainUUIDPrefix: TrainCRUD(session.session, request.user),
        FinetuningUUIDPrefix: TrainCRUD(session.session, request.user),
        NoteBookUUIDPrefix: NotebookCRUD(session.session, request.user),
        resource_group_prefix: ResourceGroupCrud(session.session, request.user),
        resource_node_prefix: ResourceGroupCrud(session.session, request.user)
    }
    resource_ids = [QAIBillingService.get_real_resource_id(resource_id) for resource_id in resource_ids]
    resource_group = {}
    for resource_id in resource_ids:
        prefix = get_prefix(resource_id)
        if prefix == inference_resource_prefix:
            logger.info(f"send to model manage server {resource_id}")
            rep = requests.get(app.settings.MODEL_MANAGE_SERVER + "?" + request.url.query)
            logger.info("recv from  model manage server[%s]", rep.text)
            result = json.loads(rep.text)
            rep = GenericMultipleResponse[Dict](data=result["data"])
            rep.message = result["message"]
            rep.counts = result["counts"]
            return rep
        if prefix not in crud_map:
            raise NotSupportResourceTypeException(resource_id)
        if prefix not in resource_group:
            resource_group[prefix] = []
        resource_group[prefix].append(resource_id)
    logger.info(f"Billing Action: [{action_type}], resource_ids: [{resource_ids}]")

    if action_type == BillingAction.DESCRIBE:
        keys = ("resource_id", "status", "status_time", "name", "price_info")
        data = []
        for prefix, resource_ids in resource_group.items():
            crud = crud_map[prefix]
            resources: Sequence[tuple[str, str, datetime, str, str]] = crud.get_status_by_uuids(resource_ids)
            ans = []
            for uuid, status, updated_at, name, reason in resources:
                price_info = None
                if status == "Suspended" and reason == "欠费暂停":
                    status = "Paused"
                elif status == "Deleted":
                    status = "Terminated"

                # billing check, see: https://cwiki.yunify.com/pages/viewpage.action?pageId=224892770 , just notebook
                if prefix == NoteBookUUIDPrefix:
                    if status == "Running":
                        price_info = {"replicas": 1}
                    elif status in [STATUS_PHASE.Suspended, STATUS_PHASE.Suspending]:
                        price_info = {"replicas": 0}

                ans.append([uuid, status, updated_at, name, price_info])

            # transform to billing format
            res = [dict(zip(keys, x)) for x in ans]
            for x in res:
                x["status_time"] = replace_to_utc_str(x["status_time"])
            data.extend(res)
        list(map(lambda x: x.update({"resource_id": QAIBillingService.get_resource_id(str(x["resource_id"]))}), data))
        return GenericMultipleResponse[Dict](data=data, counts=len(data))

    elif action_type == BillingAction.CEASE:
        res = []
        for resource_id in resource_ids:
            prefix = get_prefix(resource_id)
            crud = crud_map[prefix]
            crud.terminated(resource_id, reason="delete by billing arrears.", force=True)
            res.append({"resource_id": QAIBillingService.get_resource_id(resource_id), "status": "success"})
            if prefix not in resource_group:
                resource_group[prefix] = []
            resource_group[prefix].append(resource_id)
        # 获取资源组到期ids
        rgs = resource_group.get("rgn-", [])
        if rgs:
            # ufm unbind pkey and guids
            hostnames = rg_db.get_hostnames_by_rgn_ids(rgs)
            if app.settings.UFM_ENABLE:
                # get UFMInfo
                background_tasks.add_task(delete_guids, ufm_db, request.user.user_id, hostnames)

        return GenericMultipleResponse[Dict](data=res)

    elif action_type == BillingAction.SUSPEND:
        res = []
        for resource_id in resource_ids:
            # only notebook support suspend
            prefix = get_prefix(resource_id)
            crud = crud_map[prefix]
            background_tasks.add_task(crud.suspended_by_billing, resource_id)
            res.append({"resource_id": QAIBillingService.get_resource_id(resource_id), "status": "success"})
        return GenericMultipleResponse[Dict](data=res)

    elif action_type == BillingAction.RESUME:
        res = []
        for resource_id in resource_ids:
            # only notebook support suspend
            prefix = get_prefix(resource_id)
            crud = crud_map[prefix]
            background_tasks.add_task(crud.resume_by_billing, resource_id)
            res.append({"resource_id": QAIBillingService.get_resource_id(resource_id), "status": "success"})
        return GenericMultipleResponse[Dict](data=res)

    else:
        raise NotSupportActionTypeException(action_type)


def delete_guids(ufm_db: UFMInfoCrud, user_id: str, hostnames: List[str]):
    guids = ufm_db.get_guids(hostnames)
    # get UFMInfo
    ufm_info = ufm_db.get_ufm_info_by_user(user_id)
    ufm_db.unbind_guids_pkey(guids, ufm_info.pkey)


@router.get("/price", response_model=GenericSingleResponse[Dict], status_code=http_status.HTTP_200_OK)
def get_price(
        request: Request,
        specs: str = Query(..., description="sku id"),
        replicas: int = Query(..., description="replicas"),
        charge_mode: str = Query(..., description="charge mode, elastic or "),
        duration: int = Query(...,
                              description="duration, charge mode is elastic, duration is 3600, otherwise is month"),

):
    """
    获取 SKU 价格
    """
    if not app.settings.billing_enable:
        return GenericSingleResponse[Dict](data={})
    production = ProductCenterResource(specs, replicas)
    price = production.get_price(request.user.user_id, charge_mode=charge_mode, duration=duration)
    if price is None:
        return GenericSingleResponse[Dict](data={"price": 0})
    return GenericSingleResponse[Dict](data={"price": price["price_set"][0]})


@router.get("/lease_info", response_model=GenericSingleResponse[Dict], status_code=http_status.HTTP_200_OK)
def get_lease_info(
        request: Request,
        resource_id: str = Query(..., description="资源id")):
    """
    获取单个订单信息
    """
    if not app.settings.billing_enable:
        return GenericSingleResponse[Dict](data={})

    # crud = get_crud(resource_id, admin_crud.session, request.user)
    # resource = crud.get_by_uuid_with_permission(resource_id)
    # if not resource:
    #     return GenericSingleResponse[Dict](data={}, message="资源不存在")
    # ids = get_models_pk_v([resource])
    lease_info = QAIBillingService().get_lease_info(resource_id, request.user.user_id)
    return GenericSingleResponse[Dict](data=lease_info)


@router.get("/charge_records", response_model=GenericSingleResponse[Dict], status_code=http_status.HTTP_200_OK)
def get_charge_records(
        request: Request,
        resource_id: str = Query(..., description="资源id")):
    """
    获取已经存在的订单修改配置需要的价格.
    """
    if not app.settings.billing_enable:
        return GenericSingleResponse[Dict](data={})

    charge_records = QAIBillingService().get_charge_records(resource_id, request.user.user_id)
    if charge_records:
        return GenericSingleResponse[Dict](data=charge_records[0])
    return GenericSingleResponse[Dict](data={}, message="未查询到订单")


@router.get("/lease_infos", response_model=GenericMultipleResponse[Dict], status_code=http_status.HTTP_200_OK)
def get_lease_infos(
        request: Request,
        resource_ids: Optional[List[str]] = Query([], description="资源id列表"),
        user_id: str = Query(..., description="资源拥有者", deprecated=True),
        owner: str = Query(..., description="资源拥有者"),
):
    """
    获取多个订单信息
    """
    if not app.settings.billing_enable:
        return GenericMultipleResponse[Dict](data=[])

    if not resource_ids:
        return GenericMultipleResponse[Dict](data=[])

    lease_info = QAIBillingService().get_lease_infos(resource_ids, user_id or owner)
    return GenericMultipleResponse[Dict](data=lease_info)
