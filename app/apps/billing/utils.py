import asyncio
from datetime import datetime, timezone
from enum import Enum
from zoneinfo import ZoneInfo

from app.apps.billing.exceptions import NotSupportResourceTypeException
from app.cruds.notebooks import NotebookCRUD
from app.models.notebooks import NoteBookUUIDPrefix
from app.cruds.resource_group import ResourceGroupCrud
from app.models.resource_group import resource_group_prefix
from app.cruds.trains import TrainCRUD
from app.models.trains import TrainUUIDPrefix


class BillingAction(str, Enum):
    DESCRIBE = "describe"
    CEASE = "cease"

    # 支持暂停后恢复
    RESUME = "resume"
    SUSPEND = "suspend"

    # not support now
    TERMINATE = "terminate"
    RECOVER = "recover"


DEFAULT_TZINFO = ZoneInfo("Asia/Shanghai")


def replace_to_utc_str(dt: datetime, tzinfo: ZoneInfo = DEFAULT_TZINFO, format: str = "%Y-%m-%dT%H:%M:%SZ") -> str:
    """
    转换为utc时间字符串
    :param dt:
    :param format:
    :return:
    """
    return dt.replace(tzinfo=tzinfo).astimezone(timezone.utc).strftime(format)


async def k8s_stream_thread(websocket, container_stream):
    while container_stream.is_open():
        if container_stream.peek_stdout():
            stdout = container_stream.read_stdout()
            await websocket.send_text(stdout)
        if container_stream.peek_stderr():
            stderr = container_stream.read_stderr()
            await websocket.send_text(stderr)
    else:
        await websocket.close()


def between_callback(*args, **kwargs):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    loop.run_until_complete(k8s_stream_thread(*args, **kwargs))
    loop.close()


get_prefix = lambda x: x.split("-")[0] + "-"


def get_crud(resource_id, session, user):
    crud_map = {
        TrainUUIDPrefix: TrainCRUD,
        NoteBookUUIDPrefix: NotebookCRUD,
        resource_group_prefix: ResourceGroupCrud,
    }

    prefix = get_prefix(resource_id)
    if prefix not in crud_map:
        raise NotSupportResourceTypeException(resource_id)

    return crud_map[prefix](session, user)
