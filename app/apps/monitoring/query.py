from typing import List, Optional

from fastapi import Query

from app.core.prometheus.query_params import MonitoringQueryParams

DEFAULT_METRICS = []


class PodMonitoringQueryParams:
    """
    MonitoringQueryParams like object
    """

    def __init__(
            self,
            uuid: str = Query(None, description="作业或者容器实例或者资源组的uuid"),
            metrics_filter: List[str] = Query(
                None,
                description="""
                过滤指标, 
                对于容器 'pod_cpu_usage', 'pod_memory_usage', 'pod_memory_usage_wo_cache', 'pod_net_bytes_transmitted', 'pod_net_bytes_received', 'pod_gpu_util', 'pod_gpu_mem_usage', 'pod_tensor_core_util'
                对于节点 "node_cpu_usage", "node_memory_usage_wo_cache", "node_gpu_used_count", "node_gpu_util", "node_gpu_mem_usage", "node_gpu_mem_total", "node_cpu_requests", "node_memory_requests", "node_gpu_util_mean"
                查询实例pod 所在的节点信息 'node_info'
                """
            ),
            start_at: Optional[int] = Query(default=None, description="开始时间, timestamp格式"),
            end_at: Optional[int] = Query(default=None, description="结束时间, timestamp格式"),
            step: str = Query(default="5m", description="采样时间间隔"),
    ):
        self.start_at = start_at
        self.end_at = end_at
        self.uuid = uuid
        self.metrics_filter = metrics_filter
        self.step = step
        self.namespace = ""
        self.resource_filer = []
        self.gpu_vendor = None


class NotebookDiskMonitoringQueryParams:
    """
    MonitoringQueryParams like object
    """

    def __init__(
            self,
            uuid: str = Query(..., description="作业或者容器实例或者资源组的uuid")
    ):
        self.uuid = uuid
        self.namespace = ""
        self.resource_filer = []
        self.start_at = None
        self.end_at = None


class NotebookDiskSumMonitoringQueryParams:
    """
    MonitoringQueryParams like object
    """

    def __init__(
            self,
    ):
        self.namespace = ""
        self.resource_filer = []
        self.start_at = None
        self.end_at = None
