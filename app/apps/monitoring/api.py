from typing import Dict, Union
from fastapi import APIRouter, Depends, status as http_status, Request, Query

from app.depends import has_permission
from app.core.response import GenericMultipleResponse
from app.cruds.monitoring import MonitoringCRUD
from .dependencies import get_monitoring_crud
from .exceptions import NotSupportResourceTypeException
from app.core.prometheus.monitoring_operator import MonitoringOperator, notebook_system_disk_metrics, \
    notebook_system_disk_metrics_sum, notebook_zfs_metrics, notebook_zfs_metrics_sum, resource_node_metrics
from .query import NotebookDiskMonitoringQueryParams, NotebookDiskSumMonitoringQueryParams, PodMonitoringQueryParams
from app.cruds.notebooks import NotebookCRUD
from ..notebooks.dependencies import has_notebook_resource_permission_namespace
from app.models.notebooks import NoteBookU<PERSON>DPrefix, Notebook
from app.cruds.resource_group import ResourceGroupCrud
from app.models.trains import Train, TrainUUIDPrefix
import app
from ... import logger
from ...core.exceptions import ResourceNotFoundException
from ...core.opensearch.client import es_client
from ...cruds.finetuning import FinetuningUUIDPrefix
from ...models.resource_group import ResourceNode
from ...depends.authz import PermissionLevel, interface_auth_check
from ...depends.nb_group import notebook_namespace_permission, notebook_has_permission

# api prefix: /aicp/user
router = APIRouter(prefix="/monitoring", tags=["monitoring"])


@router.get(
    "/namespaces/{namespace}/pods",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK
)
def get_pod_metric(
        request: Request,
        namespace: str,
        query: PodMonitoringQueryParams = Depends(PodMonitoringQueryParams),
        crud: MonitoringCRUD = Depends(get_monitoring_crud)
):
    """
    获取作业的pod, 仅限于未终止的作业
    namespace: 命名空间
    endpoint: notebook / training
    """
    model_map = {
        TrainUUIDPrefix: Train,
        FinetuningUUIDPrefix: Train,
        NoteBookUUIDPrefix: Notebook
    }
    # 鉴权
    namespace = interface_auth_check(request, "NB" if query.uuid.split('-')[0] == "nb" else "TN", namespace)

    resource_prefix = query.uuid.split("-")[0] + "-"
    if resource_prefix not in model_map:
        raise NotSupportResourceTypeException(query.uuid)

    resources = model_map[resource_prefix].all_by_ids_in_namespace([query.uuid], namespace, session_=crud.session)
    if not resources:
        raise ResourceNotFoundException(query.uuid)
    resource: Union[Train, Notebook] = resources[0]

    pods = es_client.get_pods_by_lables_app([query.uuid])
    if not pods:
        raise ResourceNotFoundException(query.uuid)

    # if shareGpu monitor, we should get node gpu metrics
    if resource_prefix == NoteBookUUIDPrefix and resource.replica_specs.custom_gpu_list \
            and query.metrics_filter[0] in ["pod_gpu_util", "pod_gpu_mem_usage"]:
        metric_filter = query.metrics_filter[0].replace("pod", "node")
        resource_node: ResourceNode = ResourceNode.one_by_id(resource.replica_specs.specs, session_=crud.session)
        query.resource_filer = [resource_node.hostname]
        query.gpu_vendor = resource_node.get_gpu_vendor()
        res = MonitoringOperator().get_named_metrics([metric_filter], query)
        if res:
            res[0]["metric_name"] = query.metrics_filter[0]
            res[0]["result"] = list(
                filter(lambda x: x["metric"]["device"] in resource.replica_specs.custom_gpu_list, res[0]["result"])
            )

        return GenericMultipleResponse[Dict](data=res)

    query.resource_filer = [x["metadata"]["name"] for x in pods]
    query.gpu_vendor = resource.get_gpu_vendor()
    query.namespace = resource.namespace

    res = MonitoringOperator().get_named_metrics(query.metrics_filter, query)

    return GenericMultipleResponse[Dict](data=res)


@router.get(
    "/namespaces/{namespace}/resource_group",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK
)
def get_resource_group_metric(
        request: Request,
        namespace: str = Depends(has_permission),
        query: PodMonitoringQueryParams = Depends(PodMonitoringQueryParams),
        rgn_ids: list = Query([], description="节点id"),
        crud: MonitoringCRUD = Depends(get_monitoring_crud)
):
    """
    获取资源组节点信息
    namespace: 命名空间
    endpoint: notebook / training
    """
    crud = ResourceGroupCrud(crud.session, request.user)
    resource_group_node = crud.get_rgn_by_ids(rgn_ids)
    resource_group_node_names = [node.hostname for node in resource_group_node if resource_group_node]
    if not resource_group_node:
        return GenericMultipleResponse[Dict](data=[])

    query.resource_filer = resource_group_node_names
    query.gpu_vendor = resource_group_node[0].get_gpu_vendor()

    if not query.metrics_filter:
        query.metrics_filter = resource_node_metrics

    res = MonitoringOperator().get_named_metrics(query.metrics_filter, query)

    return GenericMultipleResponse[Dict](data=res)


@router.get(
    "/namespaces/{namespace}/notebook/disk",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_notebook_resource_permission_namespace("uuid"))]
)
def get_notebook_disk_meter(
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ)),
        query: NotebookDiskMonitoringQueryParams = Depends(NotebookDiskMonitoringQueryParams),
):
    """
    获取作业的pod, 仅限于未终止的作业
    namespace: 命名空间
    endpoint: notebook / training
    """

    results = []
    mo = MonitoringOperator()
    # 查询 local zfs pvc 使用
    query.resource_filer = [f"{app.settings.LOCAL_STORAGE_CLASS}-{query.uuid}"]
    query.metrics_filter = notebook_zfs_metrics
    query.namespace = namespace
    notebook_zfs_metrics_res = mo.get_named_metrics(query.metrics_filter, query)
    results.extend(notebook_zfs_metrics_res)

    # 查询Pod的 system disk 使用
    query.metrics_filter = notebook_system_disk_metrics
    # query.resource_filer = es_client.get_pods_name_by_lables_app([query.uuid + "-0"])
    query.resource_filer = [query.uuid]
    query.namespace = namespace
    notebook_system_metrics_res = mo.get_named_metrics(query.metrics_filter, query)
    results.extend(notebook_system_metrics_res)

    return GenericMultipleResponse[Dict](data=results)


@router.get(
    "/namespaces/{namespace}/notebook/disk/sum",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
def get_notebook_disk_meter_sum(
        request: Request,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ)),
        query: NotebookDiskSumMonitoringQueryParams = Depends(NotebookDiskSumMonitoringQueryParams),
        crud: MonitoringCRUD = Depends(get_monitoring_crud)
):
    """
    获取作业的pod, 仅限于未终止的作业
    namespace: 命名空间
    endpoint: notebook / training
    """
    notebook_crud = NotebookCRUD(crud.session, request.user)
    notebooks = notebook_crud.get_by_namespace(namespace)
    if not notebooks:
        return GenericMultipleResponse[Dict](data=[])

    results = []
    mo = MonitoringOperator()
    # 查询 local zfs pvc 使用
    query.resource_filer = [f"{app.settings.LOCAL_STORAGE_CLASS}-{notebook.uuid}" for notebook in notebooks]
    query.metrics_filter = notebook_zfs_metrics_sum
    query.namespace = namespace
    notebook_zfs_metrics_res = mo.get_named_metrics(query.metrics_filter, query)
    results.extend(notebook_zfs_metrics_res)

    # 查询Pod的 system disk 使用
    query.metrics_filter = notebook_system_disk_metrics_sum
    # query.resource_filer = es_client.get_pods_name_by_lables_app([f"{notebook.uuid}" for notebook in notebooks])
    query.namespace = namespace
    notebook_system_metrics_res = mo.get_named_metrics(query.metrics_filter, query)

    # patch notebook Running os disk total
    try:
        limit, available, usage = None, None, None
        for notebook_system_metrics in notebook_system_metrics_res:
            result = notebook_system_metrics["result"]
            if notebook_system_metrics["metric_name"] == "pod_system_disk_limit_bytes_sum_by_namespace":
                limit = result
            elif notebook_system_metrics["metric_name"] == "pod_system_disk_available_bytes_sum_by_namespace":
                available = result
            elif notebook_system_metrics["metric_name"] == "pod_system_disk_usage_bytes_sum_by_namespace":
                usage = result
        if limit and available and usage:
            limit[0]["value"][1] = sum(
                [notebook.replica_specs.custom_system_disk_size for notebook in notebooks]) * 1024 ** 3
            available[0]["value"][1] = max(int(limit[0]["value"][1]) - int(usage[0]["value"][1]), 0)
    except Exception as e:
        # if not has running notebook, maybe will raise some error
        logger.error("patch notebook Running os disk total error: %s", e)

    results.extend(notebook_system_metrics_res)

    return GenericMultipleResponse[Dict](data=results)
