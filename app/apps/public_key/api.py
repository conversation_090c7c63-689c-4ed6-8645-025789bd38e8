from datetime import datetime
from typing import Dict

from fastapi import APIRouter, Depends, Query, Request

from app import logger
from app.common.response import RESOURCE_NOT_FOUND, ERROR_DELETE_RESOURCE
from app.cruds.public_key import PublicKeysCrud
from app.apps.public_key.dependencies import get_key_crud
from app.models.public_key import PublicKeyDetailRep, PublicKeyCreate, PublicKey
from app.core.response import BaseGenericResponse, GenericMultipleResponse, GenericSingleResponse

from app.core.middlewares.auth.qingcloud_auth import QingcloudUser


router = APIRouter(prefix="/keys", tags=["keys"])


@router.get("/public_key",
            summary="获取公钥列表",
            response_model=GenericMultipleResponse[PublicKeyDetailRep])
def get_public_keys(
        request: Request,
        rg_db: PublicKeysCrud = Depends(get_key_crud),
        offset: int = 0,
        limit: int = 20,
        reverse: bool = False,
        order_by: str = 'create_time',
        search_word: str = None
):
    """
    获取公钥列表
    """
    user: QingcloudUser = request.user
    resource_groups = rg_db.get_pk_by_user(user.user_id,
                                           order_by=order_by, reverse=reverse,
                                           search_word=search_word
                                           )
    if not resource_groups:
        logger.info("当前用户[%s]未添加任何公钥", user)
        return GenericMultipleResponse[PublicKeyDetailRep](data=[], counts=0)
    public_keys_list = []
    total_count = 0
    counts = len(resource_groups)
    sorted_list = sorted(resource_groups,
                         key=lambda x: x.create_time if x.create_time is not None else datetime.min, reverse=True)
    resource_groups = sorted_list[offset:offset + limit]
    for item in resource_groups:
        total_count += 1
        public_key = PublicKeyDetailRep(**dict(item), total=total_count)
        public_keys_list.append(public_key)

    return GenericMultipleResponse[PublicKeyDetailRep](data=public_keys_list,
                                                       counts=counts)


@router.post("/public_key",
             summary="添加公钥",
             response_model=GenericSingleResponse[PublicKeyDetailRep])
def create_public_key(
        request: Request,
        public_key_create: PublicKeyCreate,
        pk_db: PublicKeysCrud = Depends(get_key_crud)
):
    """
    添加公钥
    """
    user = request.user

    resource_groups = pk_db.get_pk_by_user(user.user_id)
    rep = GenericSingleResponse[Dict](data={})

    if resource_groups:
        for pk_data in resource_groups:
            if public_key_create.key == pk_data.key:
                logger.info("当前公钥已经存在")
                rep.ret_code = -1
                rep.message = "当前公钥已存在,请重新输入"
                return rep
    public_key = pk_db.create_pulic_key(public_key_create, user)
    return GenericSingleResponse[PublicKey](data=public_key)


@router.delete("/public_key",
               summary="删除公钥",
               response_model=BaseGenericResponse)
def delete_public_key(
        request: Request,
        pk_id: str = Query(description="多个参数之间用逗号分割"),
        pk_db: PublicKeysCrud = Depends(get_key_crud)
):
    """
    删除公钥
    """
    rep = BaseGenericResponse()
    user = request.user
    rg_ids = pk_id.split(",")
    for rg_id in rg_ids:
        if not pk_db.delete_rg(rg_id, user):
            rep.ret_code = RESOURCE_NOT_FOUND
            rep.message = ERROR_DELETE_RESOURCE
            return rep
    return rep