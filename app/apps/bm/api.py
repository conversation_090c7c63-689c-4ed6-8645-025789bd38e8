import copy
from typing import List

from fastapi import APIRouter, Request, Depends, BackgroundTasks, Query, HTTPException

from app import logger, settings
from app.apps.bm.dependencies import get_bm_crud
from app.common.response import RESOURCE_NOT_FOUND, PRODUCT_NOT_FOUND, RESOURCE_NOT_ENOUGH, NOT_ENOUGH_NODE, \
    BM_NODE_NOT_FOUND, NOT_ALLWORD_TO_DO
from app.core.constant import AIPODS_TYPE, BM_NODE_TAG_KEY, BM_TAG_LOCK, STATUS_PENDING
from app.core.kube.api import list_nodes
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.interface import product_center_query_request
from app.core.qingcloud.resource import ProductCenterResource
from app.core.response import GenericSingleResponse, GenericMultipleResponse
from app.core.rlock import Rlock
from app.core.utils import get_product_resource_info, get_product_aipods_type, get_product_gpu_model, gpu_manager
from app.cruds.bm import BMCrud
from app.models.bm import CreateBmInstanceRequest, BmInstance, UpdateBmInstanceRequest, \
    CreateBmImageRequest, BmImage, BmStatus

router = APIRouter(prefix="/bm", tags=["bm"])



@router.post("/instance", response_model=GenericMultipleResponse[BmInstance])
def create_bm_instance(
        request: Request,
        req: CreateBmInstanceRequest,
        background_tasks: BackgroundTasks,
        db: BMCrud = Depends(get_bm_crud)
):
    """
    创建bm instance
    """
    if settings.billing_enable:
        # 创建新订单
        billing = QAIBillingService()
        price_info = ProductCenterResource(req.sku_id, 1).get_billing_price_info()
        logger.info(price_info)
        billing.check_resource_balance("bm-tmp", request.user.user_id,
                                       price_info, duration=req.duration, count=req.count)

    rep = GenericMultipleResponse[BmInstance](data=[], counts=req.count)
    result = product_center_query_request(search_word=req.sku_id)
    if not result["skus"]:
        rep.ret_code = RESOURCE_NOT_FOUND
        rep.message = PRODUCT_NOT_FOUND
        return rep
    product = result["skus"][0]
    if product["status"] != 'sale':
        rep.ret_code = RESOURCE_NOT_FOUND
        rep.message = PRODUCT_NOT_FOUND
        return rep
    filters = product["filters"]
    resource_conf = get_product_resource_info(filters)
    aipods_type = get_product_aipods_type(filters)
    gpu_model = get_product_gpu_model(filters)
    gpu_model_resource_key = ""
    if gpu_model:
        gpu = gpu_manager.get_gpu_matcher(gpu_model)
        gpu_model_key = gpu.label
        gpu_model_resource_key = gpu.resource
        logger.debug("节点配置[%s]", resource_conf)
        label_selector = f"{AIPODS_TYPE}={aipods_type},{gpu_model_key}={gpu_model},!{BM_NODE_TAG_KEY}"
    else:
        label_selector = f"{AIPODS_TYPE}={aipods_type},!{BM_NODE_TAG_KEY}"
    bm_node_ids = []
    for i in range(req.count):
        bm_node_id = db.generate_id(exclude_ids=bm_node_ids)
        bm_node_ids.append(bm_node_id)
        bm_instance = BmInstance(status=STATUS_PENDING,
                                 name=req.name,
                                 during=req.duration,
                                 auto_renewal=req.auto_renew,
                                 sku_id=req.sku_id,
                                 user_id=request.user.user_id,
                                 root_user_id=request.user.root_user_id,
                                 bm_node_id=bm_node_id,
                                 billing_order_id=bm_node_id,
                                 next_charge_mode=req.next_charge_mode,
                                 bm_type=aipods_type,
                                 username = req.username,
                                 password = req.password,
                                 **resource_conf)
        db.session.add(bm_instance)
        rep.data.append(copy.deepcopy(bm_instance))
    db.session.commit()
    if aipods_type == "bm1":
        return rep
    with Rlock(BM_TAG_LOCK):
        logger.info("label_selector[%s]", label_selector)
        rsp = list_nodes(label_selector=label_selector)
        nodes = rsp.items
        logger.info("[%s] node will be select", len(rsp.items))
        if len(nodes) < req.count:
            rep.ret_code = RESOURCE_NOT_ENOUGH
            rep.message = NOT_ENOUGH_NODE
            return rep
    if settings.billing_enable:
        for bm in rep.data:
            # 创建新订单
            billing = QAIBillingService()
            price_info = ProductCenterResource(req.sku_id, 1).get_billing_price_info()
            logger.info(price_info)
            bill_rep = billing.lease(bm.bm_node_id, request.user.user_id, price_info, duration=req.duration,
                                     charge_mode=req.charge_mode, auto_renew=req.auto_renewal,
                                     check_resource_balance=True, count=1)
            if bill_rep['ret_code'] != 0:
                logger.critical("创建订单失败%s", bm.bm_node_id)
    background_tasks.add_task(db.create_bm_instance, rep.data)
    return rep

@router.put("/instance", response_model=GenericSingleResponse[BmInstance])
def update_bm_instance(
        req: UpdateBmInstanceRequest,
        db: BMCrud = Depends(get_bm_crud)
):
    bm_instance = db.update_bm_instance(req)
    return GenericSingleResponse[BmInstance](data=bm_instance)


@router.post("/instance/release", response_model=GenericSingleResponse[BmInstance])
def release_bm_instance(
        bm_node_id: str,
        db: BMCrud = Depends(get_bm_crud)
):
    ''''
    更新bm1的状态并开始计费
    '''
    bm_instances = db.release_bm_instance(bm_node_id)
    return GenericSingleResponse[BmInstance](data=bm_instances)


@router.get("/instance", response_model=GenericMultipleResponse[BmInstance])
def get_bm_instance(
        request: Request,
        offset: int = 0,
        limit: int = 10,
        status: List[str] = Query(default=[]),
        order_by: str = "created_at",
        reverse: int = 1,
        db: BMCrud = Depends(get_bm_crud)
):
    try:
        valid_items = [BmStatus(item) for item in status]  # 转换为 Enum 成员
    except ValueError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid value in list: {e}"
        )
    bm_instances, count = db.get_bm_instance(offset=offset, limit=limit,
                                             status=status,order_by=order_by,reverse=reverse)
    return GenericMultipleResponse[BmInstance](data=bm_instances, counts=count)


@router.delete("/instance", response_model=GenericMultipleResponse[BmInstance])
def delete_bm_instance(
        request: Request,
        background_tasks: BackgroundTasks,
        bm_node_ids: list = Query(default=[]),
        db: BMCrud = Depends(get_bm_crud)
):
    rep = GenericMultipleResponse[BmInstance](data=[], counts=len(bm_node_ids))
    bm_instances = db.get_bm_instance_by_ids(bm_node_ids)
    if not bm_instances:
        rep.data = []
        rep.ret_code = RESOURCE_NOT_ENOUGH
        rep.message = BM_NODE_NOT_FOUND
        return rep
    for bm_instance in bm_instances:
        if bm_instance.status not in ["running", "stopped","failed"]:
            logger.error("instance status not allowed to terminated")
            rep.ret_code = RESOURCE_NOT_ENOUGH
            rep.message = NOT_ALLWORD_TO_DO % bm_instance.status
            return rep
    background_tasks.add_task(db.delete_bm_instances, bm_instances)
    return GenericMultipleResponse[BmInstance](data=bm_instances, count=len(bm_node_ids))


@router.get("/image", response_model=GenericMultipleResponse[BmInstance])
def get_bm_image(
        request: Request,
        offset: int = 0,
        limit: int = 10,
        order_by: str = "create_at",
        reverse: int = 1,
        db: BMCrud = Depends(get_bm_crud)
):
    bm_instances, count = db.get_bm_instance(offset, limit, order_by, reverse)
    return GenericMultipleResponse[BmInstance](data=bm_instances, counts=count)



@router.delete("/image", response_model=GenericMultipleResponse[BmImage])
def delete_bm_image(
        bm_image_ids: list = Query(default=[]),
        db: BMCrud = Depends(get_bm_crud)
):
    bm_instances, count = db.delete_bm_image(bm_image_ids)
    return GenericMultipleResponse[BmInstance](data=bm_instances, count=count)


@router.post("/image", response_model=GenericMultipleResponse[BmImage])
def create_bm_image(
        req: CreateBmImageRequest,
        db: BMCrud = Depends(get_bm_crud)
):
    bm_image = db.create_bm_image(req)
    return GenericSingleResponse[BmImage](data=bm_image)


@router.put("/image", response_model=GenericMultipleResponse[BmImage])
def update_bm_image(
        db: BMCrud = Depends(get_bm_crud)
):
    bm_instances, count = db.update_bm_image()
    return GenericMultipleResponse[BmImage](data=bm_instances, count=count)


