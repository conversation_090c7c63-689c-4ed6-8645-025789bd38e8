from datetime import datetime
import re

from kubernetes.client import ApiException
from kubernetes.stream import stream

from app.core.kube.api import v1_core
from kubernetes import client

from app import logger
from app.core.utils import generate_random_string
from app.core.kube.api.events import create_event


def filtered_ib_util(data, instance, dev_name):
    ib_util_data = list(filter(
        lambda item: item['metric']['instance'] == instance and item['metric'][
            'device'] == dev_name, data))
    return ib_util_data


def filtered_dcgm_util(data, gpu_node_id, gpu_dev_uuid):
    filtered_dcgm_gpu_util = list(filter(
        lambda item: item['metric']['Hostname'] == gpu_node_id and item['metric'][
            'UUID'] == gpu_dev_uuid, data))
    return filtered_dcgm_gpu_util


def filtered_schedule_util(data, gpu_node_id, gpu_dev_uuid, metric='nodeid', metric2='deviceuuid'):
    filtered_schedule_vgpu_util = []
    try:
        filtered_schedule_vgpu_util = list(filter(
            lambda item: item['metric'][metric] == gpu_node_id and item['metric'][
                metric2] == gpu_dev_uuid, data))
    except Exception as error:
        logger.error("filtered_schedule_util -> error: %s" % error)
    return filtered_schedule_vgpu_util


def filtered_prometheus_column(data, gpu_node_id, column):
    filtered_column = list(filter(
        lambda item: column in item['metric'] and item['metric'][column] == gpu_node_id, data))
    return filtered_column


def k8s_list_namespaced_pod(namespace, pattern: str = "nvidia-dcgm-exporter-*"):
    gpu_list = []
    ret = v1_core.list_namespaced_pod(namespace)

    for i in ret.items:
        pod_name = i.metadata.name
        if re.match(pattern, pod_name):
            for condition in i.status.conditions:
                if condition.type == "Ready" and condition.status == "True":
                    gpu_list.append(pod_name)
                    break
    return gpu_list


def k8s_get_node_name(pod, namespace):
    node_name = ""
    try:
        resp = v1_core.read_namespaced_pod(name=pod, namespace=namespace)
        node_name = resp.spec.node_name
    except ApiException as e:
        if e.status != 404:
            print("Unknown error: %s" % e)
            exit(1)
    return node_name


def k8s_remote_shell(pod, namespace, shell_command: str = "nvidia-smi -L | grep UUID | wc -l"):
    stdout = 0
    exec_command = ['/bin/sh']
    resp = stream(v1_core.connect_get_namespaced_pod_exec, pod, namespace, command=exec_command, stderr=True,
                  stdin=True, stdout=True, tty=False, _preload_content=False)
    commands = [
        shell_command,
    ]

    while resp.is_open():
        resp.update(timeout=3)
        if resp.peek_stdout():
            stdout = resp.read_stdout().split('\n')[0]
            logger.info("%s, pod -> %s, stdout -> %s" % (shell_command, pod, stdout))

        if resp.peek_stderr():
            logger.error("STDERR: %s" % resp.read_stderr())
        if commands:
            c = commands.pop(0)
            resp.write_stdin(c + "\n")
        else:
            break

    # resp.write_stdin("date\n")
    # sdate = resp.readline_stdout(timeout=3)
    # print("Server date command returns: %s" % sdate)

    resp.close()
    return stdout


def get_k8s_node_scheduling(node_name):
    node_status = ""
    node_scheduling = ""
    try:
        # node status
        node_resp = v1_core.read_node_status(node_name)
        node_status = node_resp.status.conditions[-1].type

        # node Scheduling
        node_info_resp = v1_core.read_node(node_name)
        node_scheduling = node_info_resp.spec.unschedulable
        logger.info("node_name-> %s, node status-> %s, Scheduling status-> %s" % (
            node_name, node_status, node_scheduling))
    except ApiException as e:
        if e.status != 404:
            print("Unknown error: %s" % e)
            exit(1)
    return node_scheduling


def set_k8s_node_scheduling(node_name, unschedulable, status):
    try:
        # 获取特定节点的信息
        node = v1_core.read_node(node_name)

        if node.spec.unschedulable != unschedulable:
            # 更新节点的 unschedulable 属性为 True（cordon） or False
            node.spec.unschedulable = unschedulable

            if status == "set":
                taint = client.V1Taint(key="aicp.group/maintain", value="true", effect="NoSchedule")
                # 设置节点的污点
                if node.spec.taints is None:
                    node.spec.taints = []
                node.spec.taints.append(taint)
            elif status == "remove":
                # 移除节点的污点
                if node.spec.taints:
                    node.spec.taints = [taint for taint in node.spec.taints if taint.key != "aicp.group/maintain"]

            v1_core.patch_node(node_name, node)
    except ApiException as e:
        if e.status != 404:
            print("Unknown error: %s" % e)
            exit(1)


def send_k8s_event(node, node_uid, reason, message):
    # 获取当前时间并转换为ISO格式
    current_time = datetime.now().astimezone().isoformat()
    metadata_name = node + generate_random_string()

    event = client.CoreV1Event(
        api_version='v1',
        kind='Event',
        metadata=client.V1ObjectMeta(name=metadata_name),
        involved_object=client.V1ObjectReference(
            api_version='v1',
            kind='Node',
            name=node,
            uid=node
        ),
        reporting_component="",
        reporting_instance="",
        reason=reason,
        message=message,
        type='Warning',
        first_timestamp=current_time,
        last_timestamp=current_time,
        source=client.V1EventSource(component='aicp')
    )
    create_event('default', event)
def replace_with_regex(data, replacements):
    # 创建正则表达式模式：匹配$1/$2/$3
    pattern = re.compile(r"(\$\d)")
    return [
        {
            key: pattern.sub(
                lambda m: replacements.get(m.group(0), m.group(0)),  # 找不到则保留原值
                value
            ) if isinstance(value, str) else value
            for key, value in item.items()
        }
        for item in data
    ]


def transform_label_metrics(data):
    result = {}

    for item in data:
        metric_name = item['metric_name']
        values = []

        for res in item['result']:
            # 处理 metric 字典
            metric_data = res['metric']

            # 跳过空 metric
            if not metric_data:
                continue

            # 处理多标签情况：将多个标签值用逗号连接
            label_values = [str(v) for v in metric_data.values()]
            combined_value = ",".join(label_values)

            values.append(combined_value)

        # 将值列表转为竖线分隔的字符串
        if values:
            result[metric_name] = "|".join(values)

    return result
def convert_to_array(data_list):
    result = []
    for item in data_list:
        new_item = {}
        for key, value in item.items():
            # 使用 split('|') 将字符串分割为数组
            new_item[key] = value.split('|')
        result.append(new_item)
    return result