import time
from typing import Dict

from fastapi import APIRouter, Depends, Request, Query

from app.cruds.gpu import GpuCrud
from app.apps.gpu.dependencies import get_gpu_crud
from app.models.gpu import GpuMaintainLog, GpuErrorCodes, GpuDashboardConfig
from .common import GPU_Monitor_Metrics_Labels, AICP_Overview_Node_Status, Template_Handlers, AICP_Overview_GPU_Cards, \
    AICP_Overview_Work_Nodes, AICP_Overview_Manager_Node, GPU_Pool_Metrics_Labels

from .utils import filtered_dcgm_util, filtered_ib_util, filtered_prometheus_column, filtered_schedule_util, \
    transform_label_metrics, replace_with_regex
from ...common.response import GPU_RESOURCE_NOT_FOUND, ERROR_GPU_NODE_NOT_FOUND
from ...core.constant import HUAWEI, NVIDIA, HEXAFLAKE, HYGON

from ...core.prometheus.client import PrometheusClient
from ...core.prometheus.monitoring_operator import MonitoringOperator
from ...core.prometheus.query_params import MonitoringQueryParams
from ...core.response import GenericMultipleResponse, GenericSingleResponse, BaseGenericResponse

from app import logger
from ...core.utils import GPUManager

# api prefix: /kapis/list_gpu
router = APIRouter(prefix="/gpu", tags=["gpu"])


@router.get("/list_gpu_dev_info", response_model=GenericMultipleResponse[Dict])
def list_gpu_dev_info(
        search_word: str = Query(description="gpu_node_id搜索", default=None),
        gpu_product: str = Query(description="显卡类型", default=NVIDIA),
        compute_group: str = Query(description="资源池", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 资源组
    """
    gpu_details = rg_db.get_gpu_status(search_word, "gpu", gpu_product)

    # DCGM_FI_DEV_GPU_UTIL
    dcgm_fi_dev_gpu_util = "DCGM_FI_DEV_GPU_UTIL"
    dev_gpu_util_data = PrometheusClient().expression_execute(dcgm_fi_dev_gpu_util)

    # DCGM_FI_DEV_MEM_COPY_UTIL
    dcgm_fi_dev_fb_used_percent = "DCGM_FI_DEV_FB_USED_PERCENT"
    fi_dev_fb_used_percent = PrometheusClient().expression_execute(dcgm_fi_dev_fb_used_percent)

    # DCGM_FI_DEV_FB_TOTAL
    dcgm_fi_dev_fb_total = "DCGM_FI_DEV_FB_TOTAL"
    fi_dev_fb_total = PrometheusClient().expression_execute(dcgm_fi_dev_fb_total)

    # DCGM_FI_DEV_FB_USED
    dcgm_fi_dev_fb_used = "DCGM_FI_DEV_FB_USED"
    fi_dev_fb_used = PrometheusClient().expression_execute(dcgm_fi_dev_fb_used)

    data = []
    for gpu_detail in gpu_details:
        gpu_node_id = gpu_detail[0]
        gpu_node_status = gpu_detail[1]

        #  Get node static info
        node_static_info = rg_db.get_node_static_info(gpu_node_id, compute_group=compute_group)
        if node_static_info is not None:
            gpu_node_ip = getattr(node_static_info, "ip")
            gpu_node_compute_group = getattr(node_static_info, "compute_group")

            pool_name = ""
            if gpu_node_compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(gpu_node_compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            available_gpu_fb = len(list(filter(
                lambda item: item['metric']['Hostname'] == gpu_node_id, fi_dev_fb_total)))

            gpu_static_info = rg_db.get_gpu_static_info(gpu_node_id)
            for static_info in gpu_static_info:
                # init
                dev_gpu_name = ""
                dev_gpu_util = 0
                dev_gpu_mem_copy_util = 0
                dev_gpu_mem_total = 0.0
                dev_gpu_mem_used = 0.0
                exported_container = ""
                exported_namespace = ""
                exported_pod = ""
                dev_gpu_status = "0"  # Normal

                gpu_uuid = getattr(static_info, "gpu_uuid")
                if dev_gpu_util_data:
                    metric_util_data = filtered_dcgm_util(dev_gpu_util_data, gpu_node_id, gpu_uuid)

                    if len(metric_util_data) != 0:
                        metric_data = metric_util_data[0]
                        dev_gpu_name = metric_data['metric']['DCGM_FI_DEV_NAME']
                        # DEV_GPU_UTIL
                        dev_gpu_util = int(metric_data['value'][1])
                        # Detect exported job
                        exported_data = metric_data['metric']
                        if exported_data.get('container') is not None:
                            if exported_data['namespace'] not in "gpu-operator":
                                exported_container = exported_data['container']
                                exported_namespace = exported_data['namespace']
                                exported_pod = exported_data['pod']

                        if exported_container == "" and exported_data.get('exported_container') is not None:
                            exported_container = exported_data['exported_container']
                            exported_namespace = exported_data['exported_namespace']
                            exported_pod = exported_data['exported_pod']
                    else:
                        #  获取静态表的 GPU 设备数匹配 Prometheus 采集的设备总数进行对比
                        dev_gpu_status = "2"  # Abnormal

                if fi_dev_fb_used_percent:
                    # fi_dev_fb_used_percent
                    mem_util = filtered_dcgm_util(fi_dev_fb_used_percent, gpu_node_id, gpu_uuid)
                    if len(mem_util) != 0:
                        dev_gpu_mem_copy_util = "{:.0f}".format(float(mem_util[0]['value'][1]) * 100)

                if fi_dev_fb_total:
                    dev_fb_total = filtered_dcgm_util(fi_dev_fb_total, gpu_node_id, gpu_uuid)
                    if len(dev_fb_total) != 0:
                        dev_gpu_mem_total = "{:.2f}".format(float(dev_fb_total[0]['value'][1]) / 1024)

                if fi_dev_fb_used:
                    gpu_mem_used = filtered_dcgm_util(fi_dev_fb_used, gpu_node_id, gpu_uuid)
                    if len(gpu_mem_used) != 0:
                        dev_gpu_mem_used = "{:.2f}".format(float(gpu_mem_used[0]['value'][1]) / 1024)

                gpu_dev_detail = {"gpu_node_id": gpu_node_id,
                                  "gpu_node_status": gpu_node_status,
                                  "gpu_node_ip": gpu_node_ip,
                                  "gpu_node_compute_group": gpu_node_compute_group,
                                  "gpu_node_compute_group_name": pool_name,
                                  "dev_gpu_available": available_gpu_fb,
                                  "dev_gpu_uuid": gpu_uuid,
                                  "dev_gpu_name": dev_gpu_name,
                                  "dev_gpu_status": dev_gpu_status,
                                  "dev_gpu_util": dev_gpu_util,
                                  "dev_gpu_mem_copy_util": dev_gpu_mem_copy_util,
                                  "dev_gpu_mem_used": dev_gpu_mem_used,
                                  "dev_gpu_mem_total": dev_gpu_mem_total,
                                  "exported_container": exported_container,
                                  "exported_namespace": exported_namespace,
                                  "exported_pod": exported_pod
                                  }
                data.append(gpu_dev_detail)
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.post("/create_gpu_node_maintain_log")
def create_gpu_node_maintain_log(
        gpu_node_id: str,
        description: str,
        gpu_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    创建 GPU 维护日志
    """

    # check gpu_node_id exist
    count_gpu_num = gpu_db.get_gpu_node(gpu_node_id)
    if count_gpu_num != 1:
        rep = BaseGenericResponse()
        rep.ret_code = GPU_RESOURCE_NOT_FOUND
        rep.message = ERROR_GPU_NODE_NOT_FOUND
        return rep

    return GenericSingleResponse[GpuMaintainLog](data=gpu_db.create_gpu_maintain_log(gpu_node_id, description))


@router.get("/list_gpu_node_maintain_log", response_model=GenericMultipleResponse[GpuMaintainLog])
def list_gpu_node_maintain_log(
        gpu_node_id: str,
        search_word: str = Query(description="搜索", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 维护日志列表
    """
    data = []
    gpu_node_maintain_log = rg_db.get_gpu_node_maintain_log(gpu_node_id, search_word)
    for maintain_log in gpu_node_maintain_log:
        data.append(maintain_log)
    return GenericMultipleResponse[GpuMaintainLog](data=data, counts=len(data))


@router.get("/list_gpu_error_codes", response_model=GenericMultipleResponse[GpuErrorCodes])
def list_gpu_error_codes(
        search_word: str = Query(description="搜索", default=None),
        gpu_product: str = Query(description="显卡类型", default=NVIDIA),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 错误码列表
    """
    data = []
    gpu_error_codes = rg_db.get_gpu_error_codes(search_word, gpu_product)
    for gpu_error_code in gpu_error_codes:
        data.append(gpu_error_code)
    return GenericMultipleResponse[GpuErrorCodes](data=data, counts=len(data))


@router.get("/get_gpu_error_code", response_model=GenericSingleResponse[GpuErrorCodes])
def get_gpu_error_code(
        code_id: str,
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 错误码列表
    """
    gpu_error_code = rg_db.get_gpu_error_code_by_code_id(code_id)

    return GenericSingleResponse[GpuErrorCodes](data=gpu_error_code)


@router.post("/update_gpu_error_code")
def update_gpu_error_code(
        code_id: str,
        gpu_err_strategy: str,
        gpu_err_priority: str,
        gpu_err_desc: str,
        gpu_suggestions: str,
        gpu_db: GpuCrud = Depends(get_gpu_crud)
):
    """
     修改 GPU 查询故障记录
    """
    gpu_db.update_gpu_error_code(code_id, gpu_err_strategy, gpu_err_priority, gpu_err_desc, gpu_suggestions)
    return GenericSingleResponse[Dict](data=[])


@router.get("/list_gpu_fault_record", response_model=GenericMultipleResponse[Dict])
def list_gpu_fault_record(
        gpu_node_id: str = Query(description="按 GPU 节点查询", default=None),
        fault_status: str = Query(description="按处理状态查询, 0-未处理，1-已处理", default=None),
        search_word: str = Query(description="搜索", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询故障记录
    """
    data = []
    fault_records = rg_db.get_gpu_fault_record_by_node_id(gpu_node_id, fault_status, search_word)
    for fault_record in fault_records:
        gpu_node_id = getattr(fault_record, "gpu_node_id")
        gpu_xid = getattr(fault_record, "gpu_xid")
        gpu_err_id = ""
        fault_priority = "info"
        gpu_err_desc = "暂无"
        gpu_suggestions = "暂无"
        gpu_err_strategy = "暂不做处理"

        #  获取资源组池的信息
        gpu_node_ip = "-"
        gpu_node_compute_group = ""
        pool_name = ""
        static_info = rg_db.get_node_static_info(gpu_node_id)
        if static_info is not None:
            gpu_node_ip = getattr(static_info, "ip")
            gpu_node_compute_group = getattr(static_info, "compute_group")

            if gpu_node_compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(gpu_node_compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

        error_code = rg_db.get_gpu_error_code_by_xid(gpu_xid)
        if error_code is not None:
            gpu_err_id = getattr(error_code, "gpu_err_id")
            fault_priority = getattr(error_code, "gpu_err_priority")
            gpu_err_desc = getattr(error_code, "gpu_err_desc")
            gpu_suggestions = getattr(error_code, "gpu_suggestions")
            gpu_err_strategy = getattr(error_code, "gpu_err_strategy")

        fault_record_data = {
            "gpu_node_id": gpu_node_id,
            "gpu_node_ip": gpu_node_ip,
            "gpu_node_compute_group": gpu_node_compute_group,
            "gpu_node_compute_group_name": pool_name,
            "records_id": getattr(fault_record, "records_id"),
            "dev_gpu_uuid": getattr(fault_record, "gpu_uuid"),
            "dev_gpu_name": getattr(fault_record, "gpu_model_name"),
            "fault_status": getattr(fault_record, "fault_status"),
            "fault_priority": fault_priority,
            "fault_treatment": getattr(fault_record, "fault_treatment"),
            "gpu_err_id": gpu_err_id,
            "gpu_xid": gpu_xid,
            "gpu_err_desc": gpu_err_desc,
            "gpu_suggestions": gpu_suggestions,
            "gpu_err_strategy": gpu_err_strategy,
            "created_at": getattr(fault_record, "created_at"),
            "updated_at": getattr(fault_record, "updated_at"),
        }
        data.append(fault_record_data)

    fault_treated_records = rg_db.get_gpu_fault_record_by_node_id()
    fault_untreated = sum(1 for item in fault_treated_records if getattr(item, "fault_status") in ['0'])
    fault_treated = sum(1 for item in fault_treated_records if getattr(item, "fault_status") in ['1'])

    fault_data = [
        {"fault_records": data, "counts": len(data), "fault_untreated": fault_untreated,
         "fault_treated": fault_treated}]
    return GenericMultipleResponse[Dict](data=fault_data, counts=len(fault_data))


@router.post("/update_gpu_fault_record")
def update_gpu_fault_record(
        gpu_fault_id: str,
        description: str,
        gpu_db: GpuCrud = Depends(get_gpu_crud)
):
    """
     修改 GPU 查询故障记录
    """
    gpu_db.update_gpu_fault_record(gpu_fault_id, description)
    return GenericSingleResponse[Dict](data=[])


@router.get("/get_total_node_status", response_model=GenericSingleResponse[Dict])
def get_total_node_status(
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询节点管理的状态
    """
    # Nvidia 网卡 DCGM_FI_DEV_GPU_UTIL
    fi_dev_fb_total = PrometheusClient().expression_execute("DCGM_FI_DEV_GPU_UTIL")
    # NPU 网卡数 单位：个
    machine_npu_nums_prom = PrometheusClient().expression_execute("npu_chip_info_utilization")
    # Hx 网卡
    hx_smi_devicespec_deviceid = PrometheusClient().expression_execute("hx_smi_devicespec_deviceid")
    # DCU 网卡数
    dcu_utilizationrate = PrometheusClient().expression_execute("dcu_utilizationrate")
    # vGPU 网卡 & vNPU 网卡 由于都由 hami 提供数据源，那么按条件查询
    node_gpu_overview = PrometheusClient().expression_execute("nodeGPUOverview {devicememorylimit != '0'}")
    overview = {"vNPU": 0, "vDCU": 0, "vGPU": 0}

    for item in node_gpu_overview:
        devicetype = item['metric']['devicetype']
        if devicetype.startswith("Ascend"):
            overview["vNPU"] += 1
        elif devicetype.startswith("DCU"):
            overview["vDCU"] += 1
        else:
            overview["vGPU"] += 1

    data = []
    num_gpu, num_npu, num_hfk, num_dcu = 0, 0, 0, 0
    num_vgpu, num_vnpu, num_vdcu = 0, 0, 0

    for node_status in rg_db.get_node_status():
        data.append(node_status[1])
        if node_status[2] == NVIDIA and node_status[4] == "gpu":
            num_gpu = num_gpu + len(rg_db.get_gpu_static_info(node_status[0]))
        if node_status[2] == HUAWEI and node_status[4] == "gpu":
            num_npu = num_npu + len(rg_db.get_gpu_static_info(node_status[0]))
        if node_status[2] == HEXAFLAKE and node_status[4] == "gpu":
            num_hfk = num_hfk + len(rg_db.get_gpu_static_info(node_status[0]))
        if node_status[2] == HYGON and node_status[4] == "gpu":
            num_dcu = num_dcu + len(rg_db.get_gpu_static_info(node_status[0]))
        if node_status[2] == NVIDIA and node_status[4] == "vgpu":
            num_vgpu = num_vgpu + len(rg_db.get_gpu_static_info(node_status[0]))
        if node_status[2] == HUAWEI and node_status[4] == "vgpu":
            num_vnpu = num_vnpu + len(rg_db.get_gpu_static_info(node_status[0]))
        if node_status[2] == HYGON and node_status[4] == "vgpu":
            num_vdcu = num_vdcu + len(rg_db.get_gpu_static_info(node_status[0]))

    resp_node_status = {
        "Ready": data.count('Ready'),  # Ready(就绪)
        "NotReady": data.count('NotReady'),  # NotReady (未就绪)
        "NoSchedule": data.count('NoSchedule'),  # NoSchedule 禁止调度
        "Unavailable": sum(1 for item in data if item not in ['Ready', 'NotReady', 'NoSchedule']),  # Unavailable 其他异常
        "num_gpu": num_gpu,
        "num_unavailable_gpu": num_gpu - len(fi_dev_fb_total) + num_vgpu,
        "num_npu": num_npu,
        "num_unavailable_npu": num_npu - len(machine_npu_nums_prom) + num_vnpu,
        "num_hfk": num_hfk,
        "num_unavailable_hfk": num_hfk - len(hx_smi_devicespec_deviceid),
        "num_dcu": num_dcu,
        "num_unavailable_dcu": num_dcu - (len(dcu_utilizationrate) - overview['vDCU']),
        "num_vgpu": num_vgpu,
        "num_unavailable_vgpu": num_vgpu - overview['vGPU'],
        "num_vnpu": num_vnpu,
        "num_unavailable_vnpu": num_vnpu - overview['vNPU'],
        "num_vdcu": num_vdcu,
        "num_unavailable_vdcu": num_vdcu - overview['vDCU']
    }

    return GenericSingleResponse[Dict](data=resp_node_status)


@router.get("/list_node_static_info", response_model=GenericMultipleResponse[Dict])
def list_node_static_info(
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询节点列表
    """
    data = []
    resourceNodeStatus = rg_db.list_node_status()
    for nodeStatus in resourceNodeStatus:
        node_id = nodeStatus[0]
        get_node_static_info = rg_db.get_node_static_info(node_id)

        gpu_dev_detail = {"node_id": node_id,
                          "node_status": nodeStatus[1],
                          "node_cpu_model": "",
                          "node_cpu": "",
                          "node_memory": "",
                          "node_gpu_model": "",
                          "node_gpu_memory": "",
                          "node_gpu": "",
                          "node_ib_bw_compute": "",
                          "node_ib_count_compute": "",
                          "node_ib_bw_storage": "",
                          "node_ib_count_storage": "",
                          "node_ib_bw_manager": "",
                          "node_ib_count_manager": ""
                          }
        if get_node_static_info is not None:
            pool_name = ""
            compute_group = getattr(get_node_static_info, "compute_group")
            if compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            gpu_dev_detail["node_ip"] = getattr(get_node_static_info, "ip")
            gpu_dev_detail["node_role"] = getattr(get_node_static_info, "role")
            gpu_dev_detail["node_compute_group"] = compute_group
            gpu_dev_detail["node_compute_group_name"] = pool_name
            gpu_dev_detail["node_cpu_model"] = getattr(get_node_static_info, "cpu_model")
            gpu_dev_detail["node_cpu"] = getattr(get_node_static_info, "cpu")
            gpu_dev_detail["node_memory"] = getattr(get_node_static_info, "memory")
            gpu_dev_detail["node_gpu_model"] = getattr(get_node_static_info, "gpu_model")
            gpu_dev_detail["node_gpu_memory"] = getattr(get_node_static_info, "gpu_memory")
            gpu_dev_detail["node_gpu"] = getattr(get_node_static_info, "gpu")
            gpu_dev_detail["node_ib_bw_compute"] = getattr(get_node_static_info, "ib_bw_compute")
            gpu_dev_detail["node_ib_count_compute"] = getattr(get_node_static_info, "ib_count_compute")
            gpu_dev_detail["node_ib_bw_storage"] = getattr(get_node_static_info, "ib_bw_storage")
            gpu_dev_detail["node_ib_count_storage"] = getattr(get_node_static_info, "ib_count_storage")
            gpu_dev_detail["node_ib_bw_manager"] = getattr(get_node_static_info, "ib_bw_manager")
            gpu_dev_detail["node_ib_count_manager"] = getattr(get_node_static_info, "ib_count_manager")

        data.append(gpu_dev_detail)

    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/get_node_static_info")
def get_node_static_info(
        node_id: str,
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 节点的基本信息与配置信息
    """
    get_node_static_info = rg_db.get_node_static_info(node_id)
    if get_node_static_info is None:
        rep = BaseGenericResponse()
        rep.ret_code = GPU_RESOURCE_NOT_FOUND
        rep.message = ERROR_GPU_NODE_NOT_FOUND
        return rep
    compute_group = getattr(get_node_static_info, "compute_group")
    pool_name = ""
    if compute_group is not None:
        get_resource_pool = rg_db.get_resource_pool(compute_group)
        if get_resource_pool is not None:
            pool_name = getattr(get_resource_pool, "pool_name")

    gpu_dev_detail = {
        "node_id": node_id,
        "node_cluster": getattr(get_node_static_info, "cluster"),
        "node_compute_group": compute_group,
        "node_compute_group_name": pool_name,
        "node_ipmi_ip": getattr(get_node_static_info, "ipmi"),
        "node_product_company": getattr(get_node_static_info, "product_company"),
        "node_hardware_model": getattr(get_node_static_info, "hardware_model"),
        "node_serial_number": getattr(get_node_static_info, "serial_number"),
        "node_zone": getattr(get_node_static_info, "zone"),
        "node_location_number": getattr(get_node_static_info, "location_number"),
        "node_cpu_model": getattr(get_node_static_info, "cpu_model"),
        "node_cpu": getattr(get_node_static_info, "cpu"),
        "node_memory": getattr(get_node_static_info, "memory"),
        "node_gpu_model": getattr(get_node_static_info, "gpu_model"),
        "node_gpu_memory": getattr(get_node_static_info, "gpu_memory"),
        "node_gpu": getattr(get_node_static_info, "gpu"),
        "nvlink": getattr(get_node_static_info, "nvlink"),
        "node_ib_bw_compute": getattr(get_node_static_info, "ib_bw_compute"),
        "node_ib_count_compute": getattr(get_node_static_info, "ib_count_compute"),
        "node_ib_bw_storage": getattr(get_node_static_info, "ib_bw_storage"),
        "node_ib_count_storage": getattr(get_node_static_info, "ib_count_storage"),
        "node_ib_bw_manager": getattr(get_node_static_info, "ib_bw_manager"),
        "node_ib_count_manager": getattr(get_node_static_info, "ib_count_manager")
    }

    return GenericSingleResponse[Dict](data=gpu_dev_detail)


@router.get("/get_compute_group_by_node_ids")
def get_compute_group_by_node_ids(
        node_ids: list[str] = Query(..., min_length=1, description="node_id"),
        gpu_product: str = Query(description="GPU 厂商", default=NVIDIA),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 节点的基本信息与配置信息
    """

    # Nvidia
    dcgm_fi_dev_gpu_util = "DCGM_FI_DEV_GPU_UTIL"
    dev_gpu_util_data = PrometheusClient().expression_execute(dcgm_fi_dev_gpu_util)

    dcgm_fi_dev_xid_errors = "DCGM_FI_DEV_XID_ERRORS"
    dcgm_fi_dev_xid_errors_data = PrometheusClient().expression_execute(dcgm_fi_dev_xid_errors)

    # vGPU HAMI
    vgpu_container = PrometheusClient().expression_execute("kube_pod_container_resource_requests",
                                                           "{pod =~'^(nb|tn|inf).*$', resource =~'qingcloud_nvidia_com_vgpu'}")

    # IB
    node_infiniband_physical_state_id = "node_infiniband_physical_state_id"
    expression = " == 5"
    infiniband_data = PrometheusClient().expression_execute(node_infiniband_physical_state_id, expression)

    # NPU
    npu_chip_info_error_code_data = PrometheusClient().expression_execute("npu_chip_info_utilization")

    # DCU
    dcu_utilizationrate_data = PrometheusClient().expression_execute("dcu_utilizationrate")

    # 带有容器信息的NPU利用率，只支持整卡 单位：%
    c_npu_utilization = PrometheusClient().expression_execute("container_npu_utilization")

    # 查询磁盘总容量
    node_filesystem_size_bytes = PrometheusClient().expression_execute(
        "node_filesystem_size_bytes{job='node-exporter',mountpoint=~'(/|/aicp/dockerRootDir|/zfspv-pool)'}")

    # 查询可用磁盘容量
    node_filesystem_used_bytes_data = PrometheusClient().expression_execute(
        "node_filesystem_size_bytes{job='node-exporter',mountpoint=~'(/|/aicp/dockerRootDir|/zfspv-pool)'}"
        " - node_filesystem_avail_bytes{job='node-exporter',mountpoint=~'(/|/aicp/dockerRootDir|/zfspv-pool)'}")

    # 查询可用磁盘容量百分比
    node_filesystem_used_percent_data = PrometheusClient().expression_execute(
        "(node_filesystem_size_bytes{job='node-exporter',mountpoint=~'(/|/aicp/dockerRootDir|/zfspv-pool)'}"
        " - node_filesystem_avail_bytes{job='node-exporter',mountpoint=~'(/|/aicp/dockerRootDir|/zfspv-pool)'}) / "
        "node_filesystem_size_bytes{job='node-exporter',mountpoint=~'(/|/aicp/dockerRootDir|/zfspv-pool)'} * 100")
    # 计算池需求，需要统计 vgpu 占用的 gpu 卡的占用及空闲
    resource_pool_used_by_gpu_data = PrometheusClient().expression_execute(
        "node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'(" + "|".join(node_ids) + ")', device_gpu_status=~'^used_by_.*$'}")
    resource_pool_idle_by_gpu_data = PrometheusClient().expression_execute(
        "node:aicp_gpu_device_overview:gpu_memory_utilization{node=~'(" + "|".join(node_ids) + ")', device_gpu_status=~'idle.*$'}")

    data = []
    for node_id in node_ids:
        node_static_info = rg_db.get_node_static_info(node_id)
        gpu_matcher = GPUManager().get_gpu_matcher(str(node_static_info.gpu_model).strip())
        if gpu_matcher is not None:
            gpu_product = gpu_matcher.vendor.lower()
        if node_static_info is not None:
            gpu_used_array = []
            gpu_available = []
            ib_available = []
            resource_pool_used_by_gpu = []
            resource_pool_idle_by_gpu = []

            gpu_static_info = rg_db.get_gpu_static_info(node_id)
            for static_info in gpu_static_info:
                exported_container = ""
                if gpu_product == NVIDIA and dev_gpu_util_data:
                    metric_util_data = filtered_dcgm_util(dev_gpu_util_data, node_id,
                                                          getattr(static_info, "gpu_uuid"))
                    if len(metric_util_data) != 0:
                        metric_data = metric_util_data[0]

                        exported_data = metric_data['metric']
                        if exported_data.get('container') is not None:
                            if exported_data['namespace'] not in "gpu-operator":
                                exported_container = exported_data['container']

                        if exported_container == "" and exported_data.get('exported_container') is not None:
                            exported_container = exported_data['exported_container']

                        if exported_container != "":
                            gpu_used_array.append(exported_container)
                if gpu_product == HUAWEI and c_npu_utilization:

                    cnu = filtered_schedule_util(c_npu_utilization, node_id,
                                                 getattr(static_info, "gpu_uuid"), 'node', 'vdie_id')
                    if len(cnu) != 0:
                        gpu_used_array.append(cnu)
            if gpu_product == NVIDIA and dcgm_fi_dev_xid_errors_data:
                gpu_available = filtered_prometheus_column(dcgm_fi_dev_xid_errors_data, node_id, 'Hostname')
            if gpu_product == HUAWEI and npu_chip_info_error_code_data:
                gpu_available = filtered_prometheus_column(npu_chip_info_error_code_data, node_id, 'node')
            if gpu_product == HYGON and dcu_utilizationrate_data:
                gpu_available = filtered_prometheus_column(dcu_utilizationrate_data, node_id, 'node')
            if infiniband_data:
                ib_available = filtered_prometheus_column(infiniband_data, node_id, 'instance')

            compute_group = getattr(node_static_info, "compute_group")
            pool_name = ""
            if compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            if vgpu_container:
                vgpu_usage = filtered_prometheus_column(vgpu_container, node_id, 'node')
                if len(vgpu_usage) != 0:
                    gpu_used_array = vgpu_usage
            if resource_pool_used_by_gpu_data:
                resource_pool_used_by_gpu = filtered_prometheus_column(resource_pool_used_by_gpu_data, node_id, 'node')
            if resource_pool_idle_by_gpu_data:
                resource_pool_idle_by_gpu = filtered_prometheus_column(resource_pool_idle_by_gpu_data, node_id, 'node')


            static_info = {
                "gpu_node_id": node_id,
                "gpu_node_compute_group": compute_group,
                "gpu_node_compute_group_name": pool_name,
                "gpu_total": len(gpu_static_info),    # 在 数据库中，实际注册 gpu 的数量及 uuid （例如8个卡，uuid不变的）
                "gpu_used": len(gpu_used_array),      # 在 Prometheus 中，pod 占用了多少卡 （已使用）
                "gpu_available": len(gpu_available),  # 在 Prometheus 里能查到的 gpu 卡的数量（可用的） uuid 变了，或少了
                "ib_available": len(ib_available),
                "resource_pool_used_by_gpu": len(resource_pool_used_by_gpu),
                "resource_pool_idle_by_gpu": len(resource_pool_idle_by_gpu),
                "ib_total": int(getattr(node_static_info, "ib_count_compute")) + int(
                    getattr(node_static_info, "ib_count_storage") + getattr(node_static_info, "ib_count_manager")),
            }

            # 新增需求 QAI-2413 节点管理，节点监控，增加磁盘的利用率显示，包含系统盘和多个数据盘
            processing_tasks = [
                {
                    'data_source': node_filesystem_size_bytes,
                    'key_prefix': 'node_filesystem',
                    'mapping': {
                        'root_size_bytes': '/',
                        'docker_root_dir_size_bytes': '/aicp/dockerRootDir',
                        'zfspv_pool_size_bytes': '/zfspv-pool'
                    }
                },
                {
                    'data_source': node_filesystem_used_bytes_data,
                    'key_prefix': 'node_filesystem_used',
                    'mapping': {
                        'root_size_bytes': '/',
                        'docker_root_dir_size_bytes': '/aicp/dockerRootDir',
                        'zfspv_pool_size_bytes': '/zfspv-pool'
                    }
                },
                {
                    'data_source': node_filesystem_used_percent_data,
                    'key_prefix': 'node_filesystem_used',
                    'mapping': {
                        'root_size_percent': '/',
                        'docker_root_dir_size_percent': '/aicp/dockerRootDir',
                        'zfspv_pool_size_percent': '/zfspv-pool'
                    }
                }
            ]

            # 统一处理所有任务
            for task in processing_tasks:
                data_source = task['data_source']
                key_prefix = task['key_prefix']

                for key_suffix, mountpoint in task['mapping'].items():
                    full_key = f"{key_prefix}_{key_suffix}"
                    item = filtered_schedule_util(data_source, node_id, mountpoint, 'instance', 'mountpoint')
                    static_info[full_key] = item[0]['value'][1] if item and item[0] else "0"
            data.append(static_info)

    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.post("/gpu_hooks")
async def gpu_hooks(
        request: Request,
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    alert_data = await request.json()
    for firing in alert_data["alerts"]:
        if firing["status"] == "firing":
            logger.info(firing)
            rg_db.create_gpu_fault_records(firing)

    return GenericMultipleResponse[Dict](data=[])


@router.get("/list_ib_dev_info", response_model=GenericMultipleResponse[Dict])
def list_ib_dev_info(
        search_word: str = Query(description="gpu_node_id搜索", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU IB 资源组
    """

    # rate(node_infiniband_link_downed_total[5m]) 统计近 5 分钟错误率
    link_downed_rate = "rate(node_infiniband_link_downed_total[5m])"
    link_downed_data = PrometheusClient().expression_execute(link_downed_rate)

    # node_infiniband_physical_state_id IB 设备的状态
    ib_physical_state = "node_infiniband_physical_state_id"
    ib_physical_state_data = PrometheusClient().expression_execute(ib_physical_state)

    # node_infiniband_info IB 信息
    node_infiniband_info = "node_infiniband_info"
    node_infiniband_info_data = PrometheusClient().expression_execute(node_infiniband_info)

    data = []
    gpu_details = rg_db.get_gpu_status(search_word, None, None, 1)
    for gpu_detail in gpu_details:
        gpu_node_id = gpu_detail[0]
        gpu_node_status = gpu_detail[1]

        #  Get node static info 找到 nvlink 启动的机器，以便准确的判断出哪些 GPU 存在 IB 网络
        node_static_info = rg_db.get_node_static_info(gpu_node_id, 0)
        if node_static_info is not None:
            gpu_node_ip = getattr(node_static_info, "ip")
            compute_group = getattr(node_static_info, "compute_group")

            pool_name = ""
            if compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            ib_count_compute = getattr(node_static_info, "ib_count_compute")
            ib_count_storage = getattr(node_static_info, "ib_count_storage")
            ib_count_manager = getattr(node_static_info, "ib_count_manager")
            ib_bw_compute = getattr(node_static_info, "ib_bw_compute")
            ib_bw_storage = getattr(node_static_info, "ib_bw_storage")
            ib_bw_manager = getattr(node_static_info, "ib_bw_manager")

            ib_device = ""
            ib_board_id = ""
            ib_firmware_version = ""
            ib_hca_type = ""
            link_downed_rate = 0
            ib_physical_state = 0

            ib_dev_info = rg_db.get_ib_dev_info(gpu_node_id)
            for ib_dev in ib_dev_info:
                dev_name = getattr(ib_dev, "dev_name")
                metric_infiniband_info_data = filtered_ib_util(node_infiniband_info_data, gpu_node_id, dev_name)
                if len(metric_infiniband_info_data) != 0:
                    metric_data = metric_infiniband_info_data[0]['metric']
                    ib_device = metric_data['device']
                    ib_board_id = metric_data['board_id']
                    ib_firmware_version = metric_data['firmware_version']
                    ib_hca_type = metric_data['hca_type']

                metric_link_downed_data = filtered_ib_util(link_downed_data, gpu_node_id, dev_name)
                if len(metric_link_downed_data) != 0:
                    link_downed_rate = metric_link_downed_data[0]['value'][1]

                metric_ib_physical_state_data = filtered_ib_util(ib_physical_state_data, gpu_node_id, dev_name)
                if len(metric_ib_physical_state_data) != 0:
                    ib_physical_state = metric_ib_physical_state_data[0]['value'][1]

                gpu_dev_detail = {"gpu_node_id": gpu_node_id,
                                  "gpu_node_status": gpu_node_status,
                                  "gpu_node_ip": gpu_node_ip,
                                  "gpu_node_compute_group": compute_group,
                                  "gpu_node_compute_group_name": pool_name,
                                  "ib_device": ib_device,
                                  "ib_board_id": ib_board_id,
                                  "ib_firmware_version": ib_firmware_version,
                                  "ib_hca_type": ib_hca_type,
                                  "ib_status": int(ib_physical_state),
                                  "link_downed_rate": int(link_downed_rate),
                                  "ib_count_compute": ib_count_compute,
                                  "ib_count_storage": ib_count_storage,
                                  "ib_count_manager": ib_count_manager,
                                  "ib_bw_compute": ib_bw_compute,
                                  "ib_bw_storage": ib_bw_storage,
                                  "ib_bw_manager": ib_bw_manager
                                  }
                data.append(gpu_dev_detail)
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/list_vgpu_dev_info", response_model=GenericMultipleResponse[Dict])
def list_vgpu_dev_info(
        gpu_product: str = Query(description="GPU 厂商", default=NVIDIA),
        compute_group: str = Query(description="资源池", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 vGPU 资源组
    """
    gpu_details = rg_db.get_gpu_status(None, "vgpu", gpu_product)

    device_core_limit_prom = PrometheusClient().expression_execute("GPUDeviceCoreLimit")
    node_gpu_memory_percentage_prom = PrometheusClient().expression_execute("nodeGPUMemoryPercentage")
    node_gpu_overview_prom = PrometheusClient().expression_execute("nodeGPUOverview{devicememorylimit != '0'}")

    vgpu_core_percentage_prom = PrometheusClient().expression_execute("vGPUCorePercentage")
    vgpu_pods_device_allocated_prom = PrometheusClient().expression_execute("vGPUPodsDeviceAllocated")
    vgpu_memory_percentage_prom = PrometheusClient().expression_execute("vGPUMemoryPercentage")

    # 整卡显存用量
    vgpu_device_memory_usage_in_bytes_prom = PrometheusClient().expression_execute("vGPU_device_memory_usage_in_bytes")
    # 容器显存用量
    device_memory_desc_of_container_prom = PrometheusClient().expression_execute("Device_memory_desc_of_container")

    data = []
    for gpu_detail in gpu_details:
        gpu_node_id = gpu_detail[0]
        gpu_node_status = gpu_detail[1]

        #  Get node static info
        node_static_info = rg_db.get_node_static_info(gpu_node_id, compute_group=compute_group)
        if node_static_info is not None:
            gpu_node_ip = getattr(node_static_info, "ip")
            gpu_node_compute_group = getattr(node_static_info, "compute_group")

            pool_name = ""
            if gpu_node_compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(gpu_node_compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            available_gpu_fb = len(list(filter(
                lambda item: item['metric']['nodeid'] == gpu_node_id, node_gpu_overview_prom)))

            gpu_static_info = rg_db.get_gpu_static_info(gpu_node_id)
            for static_info in gpu_static_info:
                # init
                dev_gpu_name = ""

                gpu_device_core_limit = 0
                gpu_device_core_allocated = 0
                gpu_device_memory_limit = 0
                node_gpu_memory_percentage = 0
                # gpu_device_memory_allocated = node_gpu_overview
                node_gpu_overview = "0.00"
                gpu_device_shared_num = 0
                gpu_device_memory_usage = "0.00"

                core = []
                device = []
                memory = []
                vgpu_core_list = []

                # nvidia 默认
                gpu_uuid = getattr(static_info, "gpu_uuid")

                if gpu_product == HUAWEI:
                    gpu_uuid = "Ascend-" + str(getattr(static_info, "index"))

                if vgpu_device_memory_usage_in_bytes_prom:
                    total_vgpu_usage_in_bytes = filtered_prometheus_column(vgpu_device_memory_usage_in_bytes_prom,
                                                                           gpu_uuid, 'deviceuuid')
                    if len(total_vgpu_usage_in_bytes) != 0:
                        gpu_device_memory_usage = "{:.2f}".format(float(total_vgpu_usage_in_bytes[0]['value'][1])
                                                                  / 1024 / 1024 / 1024)

                if device_core_limit_prom:
                    core_limit = filtered_schedule_util(device_core_limit_prom, gpu_node_id, gpu_uuid)
                    if len(core_limit) != 0:
                        gpu_device_core_limit = int(core_limit[0]['value'][1])

                if node_gpu_memory_percentage_prom:
                    node_gpu_memory_percentage_data = filtered_schedule_util(node_gpu_memory_percentage_prom,
                                                                             gpu_node_id,
                                                                             gpu_uuid)
                    if len(node_gpu_memory_percentage_data) != 0:
                        node_gpu_memory_percentage = "{:.2f}".format(
                            float(node_gpu_memory_percentage_data[0]['value'][1]))

                if node_gpu_overview_prom:
                    node_gpu_overview_data = filtered_schedule_util(node_gpu_overview_prom, gpu_node_id, gpu_uuid)
                    if len(node_gpu_overview_data) != 0:
                        node_gpu_overview = int(node_gpu_overview_data[0]['value'][1]) / 1024 / 1024
                        gpu_device_core_allocated = node_gpu_overview_data[0]['metric']['devicecores']
                        # 有 bug 已反馈给相关人，暂时注释
                        # gpu_device_shared_num = int(node_gpu_overview_data[0]['metric']['sharedcontainers'])
                        gpu_device_memory_limit = int(node_gpu_overview_data[0]['metric']['devicememorylimit'])
                        if gpu_product == NVIDIA:
                            gpu_device_memory_limit = "{:.2f}".format(gpu_device_memory_limit / 1024)
                            node_gpu_overview = "{:.2f}".format(node_gpu_overview / 1024)

                        dev_gpu_name = node_gpu_overview_data[0]['metric']['devicetype']
                        if gpu_product == HUAWEI:
                            dev_gpu_name = dev_gpu_name + "-" + str(getattr(static_info, "index")) + "-" + str(
                                getattr(static_info, "gpu_model"))

                if vgpu_core_percentage_prom and vgpu_pods_device_allocated_prom and vgpu_memory_percentage_prom:
                    vgpu_core_percentage_data = filtered_schedule_util(vgpu_core_percentage_prom, gpu_node_id, gpu_uuid,
                                                                       "nodename")
                    vgpu_pods_device_allocated_data = filtered_schedule_util(vgpu_pods_device_allocated_prom,
                                                                             gpu_node_id, gpu_uuid, "nodename")
                    vgpu_memory_percentage_data = filtered_schedule_util(vgpu_memory_percentage_prom, gpu_node_id,
                                                                         gpu_uuid, "nodename")
                    if len(vgpu_core_percentage_data) != 0 and len(vgpu_pods_device_allocated_data) != 0 and len(
                            vgpu_memory_percentage_data) != 0:
                        for vgpu_core_percentage in vgpu_core_percentage_data:

                            device_memory_used = "0.00"
                            pod_name = vgpu_core_percentage['metric']['podname']
                            if device_memory_desc_of_container_prom:
                                desc_of_container = filtered_schedule_util(device_memory_desc_of_container_prom,
                                                                           pod_name,
                                                                           gpu_uuid, 'podname')
                                if len(desc_of_container) != 0:
                                    device_memory_used = "{:.2f}".format(
                                        float(desc_of_container[0]['value'][1]) / 1024 / 1024 / 1024)

                            core_percentage = {
                                "vgpu_core_percentage": "{:.2f}".format(float(vgpu_core_percentage['value'][1])),
                                "vgpu_memory_used": device_memory_used,
                                "pod_namespace": vgpu_core_percentage['metric']['podnamespace'],
                                "pod_name": pod_name,
                            }
                            core.append(core_percentage)

                        for vgpu_pods_device_allocated in vgpu_pods_device_allocated_data:
                            allocated = int(vgpu_pods_device_allocated['value'][1]) / 1024 / 1024
                            if gpu_product == NVIDIA:
                                allocated = allocated / 1024
                            pods_device_allocated = {
                                "vgpu_pods_device_allocated": "{:.2f}".format(allocated),
                                "pod_namespace": vgpu_pods_device_allocated['metric']['podnamespace'],
                                "pod_name": vgpu_pods_device_allocated['metric']['podname']
                            }
                            device.append(pods_device_allocated)

                        for vgpu_memory_percentage in vgpu_memory_percentage_data:
                            memory_percentage = {
                                "vgpu_memory_percentage": "{:.2f}".format(float(vgpu_memory_percentage['value'][1])),
                                "pod_namespace": vgpu_memory_percentage['metric']['podnamespace'],
                                "pod_name": vgpu_memory_percentage['metric']['podname']
                            }
                            memory.append(memory_percentage)

                        merged_dict = {}
                        for d in core + device + memory:
                            key = (d['pod_namespace'], d['pod_name'])  # 使用 'podnamespace' 和 'podname' 作为键
                            if key in merged_dict:
                                merged_dict[key].update(d)  # 如果键已存在，则更新该键对应的值
                            else:
                                merged_dict[key] = d.copy()  # 如果键不存在，则添加新的键值对
                        vgpu_core_list = list(merged_dict.values())
                #  获取静态表的 GPU 设备数匹配 Prometheus 采集的设备总数进行对比
                if available_gpu_fb == len(gpu_static_info):
                    dev_gpu_status = "0"  # Normal
                else:
                    dev_gpu_status = "2"  # Abnormal

                gpu_dev_detail = {"gpu_node_id": gpu_node_id,
                                  "gpu_node_status": gpu_node_status,
                                  "gpu_node_ip": gpu_node_ip,
                                  "gpu_node_compute_group": gpu_node_compute_group,
                                  "gpu_node_compute_group_name": pool_name,
                                  "gpu_node_memory_percentage": node_gpu_memory_percentage,
                                  "dev_gpu_uuid": gpu_uuid,
                                  "dev_gpu_name": dev_gpu_name,
                                  "dev_gpu_status": dev_gpu_status,
                                  "gpu_device_core_limit": gpu_device_core_limit,
                                  "gpu_device_core_allocated": gpu_device_core_allocated,
                                  "gpu_device_memory_limit": gpu_device_memory_limit,
                                  "gpu_device_memory_allocated": node_gpu_overview,
                                  "gpu_device_memory_usage": gpu_device_memory_usage,
                                  # 用 vgpu pod 数代替 shared_num
                                  "gpu_device_shared_num": len(vgpu_core_list),
                                  "vgpu_core_list": vgpu_core_list
                                  }
                data.append(gpu_dev_detail)
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/list_npu_dev_info", response_model=GenericMultipleResponse[Dict])
def list_npu_dev_info(
        search_word: str = Query(description="npu_node_id 搜索", default=None),
        compute_group: str = Query(description="资源池", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 NPU 资源组
    """
    # 网卡数 单位：个
    machine_npu_nums_prom = PrometheusClient().expression_execute("machine_npu_nums")

    # 网口 link 状态，  1 up  2 donw
    link = PrometheusClient().expression_execute("npu_chip_info_link_status")

    # 网络健康状态 1：健康，可以连通 0：不健康，无法连通
    network = PrometheusClient().expression_execute("npu_chip_info_network_status")

    # 昇腾AI处理器错误码 具体参考
    # https://support.huawei.com/enterprise/zh/doc/EDOC1100368811?idPath=23710424|251366513|22892968|252309113|254184887
    error = PrometheusClient().expression_execute("npu_chip_info_error_code")

    # 昇腾AI处理器健康状态 1：健康 0：不健康
    health = PrometheusClient().expression_execute("npu_chip_info_health_status")

    # 昇腾AI处理器DDR内存已使用量 单位：MB
    ddr_used_memory = PrometheusClient().expression_execute("npu_chip_info_used_memory")

    # 昇腾AI处理器DDR内存总量 单位：MB
    ddr_used_total_memory = PrometheusClient().expression_execute("npu_chip_info_total_memory")

    # 昇腾AI处理器HBM内存已使用量（仅支持训练产品）单位：%
    hbm_used_memory = PrometheusClient().expression_execute("npu_chip_info_hbm_used_memory")

    # 昇腾AI处理器HBM总内存（仅支持训练产品）单位：MB
    hbm_total_memory = PrometheusClient().expression_execute("npu_chip_info_hbm_total_memory")

    # 昇腾AI处理器AI Core利用率 单位：%
    utilization = PrometheusClient().expression_execute("npu_chip_info_utilization")

    # 带有容器信息的NPU内存总大小，只支持整卡 单位：MB
    c_npu_total_memory = PrometheusClient().expression_execute("container_npu_total_memory")

    # 带有容器信息的NPU已使用内存，只支持整卡 单位：MB
    c_npu_used_memory = PrometheusClient().expression_execute("container_npu_used_memory")

    # 带有容器信息的NPU利用率，只支持整卡 单位：%
    c_npu_utilization = PrometheusClient().expression_execute("container_npu_utilization")

    data = []
    gpu_details = rg_db.get_gpu_status(search_word, "gpu", HUAWEI)
    for node in gpu_details:
        gpu_node_id = node[0]
        npu_node_ip = ""
        npu_node_compute_group = ""
        pool_name = ""

        node_static_info = rg_db.get_node_static_info(gpu_node_id, compute_group=compute_group)
        if node_static_info is not None:
            npu_node_ip = getattr(node_static_info, "ip")
            npu_node_compute_group = getattr(node_static_info, "compute_group")

            if npu_node_compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(npu_node_compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

        gpu_static_info = rg_db.get_gpu_static_info(gpu_node_id)
        for static_info in gpu_static_info:
            uuid = getattr(static_info, "gpu_uuid")

            merged_dict = {}
            container_npu_total_memory = "0"
            container_npu_used_memory = "0"
            container_npu_utilization = "0"
            npu_node_status = "0"
            exported_namespace = ""
            pod_name = ""
            model_name = ""
            pcie_bus_info = ""
            nums = 0

            health_status = filtered_schedule_util(health, gpu_node_id, uuid, 'node', 'vdie_id')
            if len(health_status) != 0:
                model_name = health_status[0]['metric']['model_name']
                pcie_bus_info = health_status[0]['metric']['pcie_bus_info']
                merged_dict["npu_chip_info_health_status"] = health_status[0]['value'][1]

            npu_chip_info_link_status = -1
            npu_chip_info_network_status = -1
            npu_chip_info_error_code = 0
            npu_chip_info_used_memory = 0
            npu_chip_info_total_memory = 0
            npu_chip_info_hbm_used_memory = 0
            npu_chip_info_hbm_total_memory = 0
            npu_chip_info_utilization = 0

            if machine_npu_nums_prom:
                nums = filtered_prometheus_column(machine_npu_nums_prom, gpu_node_id, 'node')[0]['value'][1]
                if link:
                    npu_chip_info_link_status = \
                        filtered_schedule_util(link, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                if network:
                    npu_chip_info_network_status = \
                        filtered_schedule_util(network, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                if error:
                    npu_chip_info_error_code = \
                        filtered_schedule_util(error, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                npu_chip_info_used_memory = \
                    filtered_schedule_util(ddr_used_memory, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                npu_chip_info_total_memory = \
                    filtered_schedule_util(ddr_used_total_memory, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                npu_chip_info_hbm_used_memory = \
                    filtered_schedule_util(hbm_used_memory, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                npu_chip_info_hbm_total_memory = \
                    filtered_schedule_util(hbm_total_memory, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]
                npu_chip_info_utilization = \
                    filtered_schedule_util(utilization, gpu_node_id, uuid, 'node', 'vdie_id')[0]['value'][1]

            merged_dict["npu_chip_info_link_status"] = npu_chip_info_link_status
            merged_dict["npu_chip_info_network_status"] = npu_chip_info_network_status
            merged_dict["npu_chip_info_error_code"] = npu_chip_info_error_code
            merged_dict["npu_chip_info_used_memory"] = npu_chip_info_used_memory
            merged_dict["npu_chip_info_total_memory"] = npu_chip_info_total_memory
            merged_dict["npu_chip_info_hbm_used_memory"] = npu_chip_info_hbm_used_memory
            merged_dict["npu_chip_info_hbm_total_memory"] = npu_chip_info_hbm_total_memory
            merged_dict["npu_chip_info_utilization"] = npu_chip_info_utilization
            merged_dict["machine_npu_nums"] = nums

            #  假数据，需要适配改造。获取静态表的 NPU 设备数匹配 Prometheus 采集的设备总数进行对比
            if int(nums) != len(gpu_static_info):
                npu_node_status = "2"  # 异常

            merged_dict["npu_status"] = npu_node_status
            merged_dict["npu_node_status"] = node[1]
            merged_dict["npu_node_id"] = gpu_node_id
            merged_dict["npu_node_ip"] = npu_node_ip
            merged_dict["npu_node_compute_group"] = npu_node_compute_group
            merged_dict["npu_node_compute_group_name"] = pool_name

            merged_dict["npu_uuid"] = uuid

            cntm = filtered_schedule_util(c_npu_total_memory, gpu_node_id, uuid, 'node', 'vdie_id')
            if len(cntm) != 0:
                container_npu_total_memory = cntm[0]['value'][1]

            cnum = filtered_schedule_util(c_npu_used_memory, gpu_node_id, uuid, 'node', 'vdie_id')
            if len(cnum) != 0:
                container_npu_used_memory = cnum[0]['value'][1]

            cnu = filtered_schedule_util(c_npu_utilization, gpu_node_id, uuid, 'node', 'vdie_id')
            if len(cnu) != 0:
                container_npu_utilization = cnu[0]['value'][1]
                exported_namespace = cnu[0]['metric']['exported_namespace']
                pod_name = cnu[0]['metric']['pod_name']

            merged_dict["container_npu_total_memory"] = container_npu_total_memory
            merged_dict["container_npu_used_memory"] = container_npu_used_memory
            merged_dict["container_npu_utilization"] = container_npu_utilization
            merged_dict["exported_namespace"] = exported_namespace
            merged_dict["pod_name"] = pod_name
            merged_dict["model_name"] = model_name
            merged_dict["pcie_bus_info"] = pcie_bus_info
            data.append(merged_dict)

    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/list_gpu_dashboard_config", response_model=GenericMultipleResponse[GpuDashboardConfig])
def list_gpu_dashboard_config(
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 监控配置列表
    """
    data = []
    dashboards = rg_db.list_gpu_dashboard_config()
    for dashboard in dashboards:
        data.append(dashboard)
    return GenericMultipleResponse[GpuDashboardConfig](data=data, counts=len(data))


@router.post("/update_gpu_dashboard_config")
def update_gpu_dashboard_config(
        dashboard_id: str = Query(description="Grafana 唯一标识", default=None),
        dashboard_name: str = Query(description="Grafana 菜单名称", default=None),
        grafana_address: str = Query(description="Grafana 地址:端口", default=None),
        enable_dashboard: str = Query(description="是否开启 Grafana 看板", default=None),
        enable_table_view: str = Query(description="是否开启 tabview 页签看板", default=None),
        uid: str = Query(description="Grafana dashboard uid", default=None),
        template: str = Query(description="Grafana dashboard 名称", default=None),
        grafana_params: str = Query(description="Grafana url 参数", default=None),
        gpu_db: GpuCrud = Depends(get_gpu_crud)
):
    """
     修改 GPU 查询故障记录
    """
    gpu_db.update_gpu_dashboard_config(dashboard_id, dashboard_name, enable_dashboard, enable_table_view,
                                       grafana_address, uid, template, grafana_params)
    return GenericSingleResponse[Dict](data=[])


@router.get("/list_hfk_dev_info", response_model=GenericMultipleResponse[Dict])
def list_gpu_dev_info(
        search_word: str = Query(description="gpu_node_id搜索", default=None),
        gpu_product: str = Query(description="显卡类型", default=HEXAFLAKE),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 资源组
    """
    gpu_details = rg_db.get_gpu_status(search_word, "gpu", gpu_product)

    hx_smi_gpu_info = PrometheusClient().expression_execute("hx_smi_gpu_info")
    dev_gpu_util_data = PrometheusClient().expression_execute("hx_smi_devicespec_utilization_gpu")
    fi_dev_fb_total = PrometheusClient().expression_execute("hx_smi_devicespec_memoryusage_total")
    fi_dev_fb_used = PrometheusClient().expression_execute("hx_smi_devicespec_memoryusage_used")

    data = []
    for gpu_detail in gpu_details:
        gpu_node_id = gpu_detail[0]
        gpu_node_status = gpu_detail[1]

        #  Get node static info
        node_static_info = rg_db.get_node_static_info(gpu_node_id)
        if node_static_info is not None:
            gpu_node_ip = getattr(node_static_info, "ip")
            gpu_node_compute_group = getattr(node_static_info, "compute_group")

            pool_name = ""
            if gpu_node_compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(gpu_node_compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            available_gpu_fb = len(list(filter(
                lambda item: item['metric']['node_name'] == gpu_node_id, hx_smi_gpu_info)))

            gpu_static_info = rg_db.get_gpu_static_info(gpu_node_id)
            for static_info in gpu_static_info:
                # init
                dev_gpu_name = ""
                dev_gpu_util = 0
                dev_gpu_mem_copy_util = 0
                dev_gpu_mem_total = 0.0
                dev_gpu_mem_used = 0.0
                exported_container = ""
                exported_namespace = ""
                exported_pod = ""
                dev_gpu_status = "0"  # Normal

                gpu_uuid = getattr(static_info, "gpu_uuid")
                gpu_index = str(getattr(static_info, "index")) + "-" + str(getattr(static_info, "index"))
                if dev_gpu_util_data:
                    metric_util_data = filtered_schedule_util(dev_gpu_util_data, gpu_node_id, gpu_index,
                                                              'node_name',
                                                              'uuid')

                    if len(metric_util_data) != 0:
                        metric_data = metric_util_data[0]
                        dev_gpu_name = hx_smi_gpu_info[0]['metric']['pcicard_partnumber']
                        # DEV_GPU_UTIL
                        dev_gpu_util = int(metric_data['value'][1])
                        # Detect exported job
                        exported_data = metric_data['metric']
                        if exported_data.get('container') is not None:
                            if exported_data['namespace'] not in "hexaflake-gpu-exporter":
                                exported_container = exported_data['container']
                                exported_namespace = exported_data['namespace']
                                exported_pod = exported_data['pod']

                        if exported_container == "" and exported_data.get('exported_container') is not None:
                            exported_container = exported_data['exported_container']
                            exported_namespace = exported_data['exported_namespace']
                            exported_pod = exported_data['exported_pod']
                    else:
                        #  获取静态表的 GPU 设备数匹配 Prometheus 采集的设备总数进行对比
                        dev_gpu_status = "2"  # Abnormal

                if fi_dev_fb_total:
                    dev_fb_total = filtered_schedule_util(fi_dev_fb_total, gpu_node_id, gpu_index, 'node_name', 'uuid')
                    if len(dev_fb_total) != 0:
                        dev_gpu_mem_total = "{:.2f}".format(float(dev_fb_total[0]['value'][1]) / 1024)

                if fi_dev_fb_used:
                    gpu_mem_used = filtered_schedule_util(fi_dev_fb_used, gpu_node_id, gpu_index, 'node_name', 'uuid')
                    if len(gpu_mem_used) != 0:
                        dev_gpu_mem_used = "{:.2f}".format(float(gpu_mem_used[0]['value'][1]) / 1024)

                gpu_dev_detail = {"gpu_node_id": gpu_node_id,
                                  "gpu_node_status": gpu_node_status,
                                  "gpu_node_ip": gpu_node_ip,
                                  "gpu_node_compute_group": gpu_node_compute_group,
                                  "gpu_node_compute_group_name": pool_name,
                                  "dev_gpu_available": available_gpu_fb,
                                  "dev_gpu_uuid": gpu_uuid,
                                  "dev_gpu_name": dev_gpu_name,
                                  "dev_gpu_status": dev_gpu_status,
                                  "dev_gpu_util": dev_gpu_util,
                                  "dev_gpu_mem_used": dev_gpu_mem_used,
                                  "dev_gpu_mem_total": dev_gpu_mem_total,
                                  "exported_container": exported_container,
                                  "exported_namespace": exported_namespace,
                                  "exported_pod": exported_pod
                                  }
                data.append(gpu_dev_detail)
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/list_gpu_overview", response_model=GenericMultipleResponse[Dict])
def list_gpu_overview(
        search_word: str = Query(description="gpu_node_id搜索", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    num_gpu, num_npu, num_hfk, num_dcu, num_cpu = 0, 0, 0, 0, 0
    num_vgpu, num_vnpu, num_vdcu = 0, 0, 0

    num_gpu_card, num_npu_card, num_hfk_card, num_dcu_card, num_cpu_card = 0, 0, 0, 0, 0
    num_vgpu_card, num_vnpu_card, num_vdcu_card = 0, 0, 0

    already_used_gpu_card, already_used_npu_card, already_used_hfx_card, already_used_dcu_card = 0, 0, 0, 0
    already_used_vnpu_card, already_used_vgpu_card, already_used_vdcu_card = 0, 0, 0

    # nvidia 总 GPU 数
    gpu_util = PrometheusClient().expression_execute("DCGM_FI_DEV_GPU_UTIL")
    # 海飞科总 GPU 数
    hf_gpu_util = PrometheusClient().expression_execute("hx_smi_devicespec_utilization_gpu")
    # 华为总 NPU 数
    npu_chip_info_utilization = PrometheusClient().expression_execute("npu_chip_info_utilization")
    # 海光总 DCU 数
    dcu_utilizationrate = PrometheusClient().expression_execute("dcu_utilizationrate")

    # 运行中的 pod 包括容器，训练，推理
    # GPU 不包括 vGPU，目前只适配了 nvidia 和 npu，其他的按需求适配
    # qingcloud_adapter_com_adapter 是 vnpu 的 resource
    # qingcloud_nvidia_com_vgpu 是 nvidia vgpu 的 resource
    # hygon_com_dcu 是 DCU 的 resource
    gpu_express = "huawei_com|nvidia_com_gpu|qingcloud_adapter_com_adapter|hygon_com_dcu"
    vgpu_express = "|qingcloud_nvidia_com_vgpu"
    # 容器
    gpu_expression = ("sum(kube_pod_container_resource_requests{pod=~'^nb.*$', node!='', resource=~'^(" +
                      gpu_express + ").*$" + vgpu_express + "'}) by (pod, resource, namespace)")
    nb_prom = PrometheusClient().expression_execute(gpu_expression)
    # 训练
    gpu_expression = ("sum(kube_pod_container_resource_requests{pod=~'^tn.*$', node!='', resource=~'^(" +
                      gpu_express + ").*$" + vgpu_express + "'}) by (pod, resource, namespace)")
    tn_prom = PrometheusClient().expression_execute(gpu_expression)
    # 推理
    gpu_expression = ("sum(kube_pod_container_resource_requests{pod=~'^inf.*$', node!='', resource=~'^(" +
                      gpu_express + ").*$" + vgpu_express + "'}) by (pod, resource, namespace)")
    inf_prom = PrometheusClient().expression_execute(gpu_expression)

    for node_status in rg_db.get_node_status():
        if node_status[2] == NVIDIA and node_status[4] == "gpu":
            # 统计卡
            num_gpu = num_gpu + 1
            num_gpu_card = num_gpu_card + rg_db.get_gpu_static_info(node_status[0], "true")

            # 统计占用的卡
            dev_gpu_util_data = PrometheusClient().expression_execute("DCGM_FI_DEV_GPU_UTIL",
                                                                      "{Hostname='" + node_status[0] + "', "
                                                                                                       "exported_container != 'gpu-operator', "
                                                                                                       "exported_container != ''}")
            if dev_gpu_util_data:
                already_used_gpu_card = already_used_gpu_card + len(dev_gpu_util_data)
        if node_status[2] == HUAWEI and node_status[4] == "gpu":
            num_npu = num_npu + 1
            num_npu_card = num_npu_card + rg_db.get_gpu_static_info(node_status[0], "true")

            npu_gpu_utilization = PrometheusClient().expression_execute("container_npu_utilization")
            cnu = filtered_prometheus_column(npu_gpu_utilization, node_status[0], 'node')
            if cnu:
                already_used_npu_card = already_used_npu_card + len(cnu)
        if node_status[2] == HEXAFLAKE and node_status[4] == "gpu":
            num_hfk = num_hfk + 1
            num_hfk_card = num_hfk_card + rg_db.get_gpu_static_info(node_status[0], "true")
        if node_status[2] == HYGON and node_status[4] == "gpu":
            num_dcu = num_dcu + 1
            num_dcu_card = num_dcu_card + rg_db.get_gpu_static_info(node_status[0], "true")

            # 统计占用的卡
            dcu = filtered_prometheus_column(dcu_utilizationrate, node_status[0], 'node')
            if dcu:
                already_used_dcu_card = already_used_dcu_card + len(dcu)
        if node_status[2] == NVIDIA and node_status[4] == "vgpu":
            num_vgpu = num_vgpu + 1
            num_vgpu_card = num_vgpu_card + rg_db.get_gpu_static_info(node_status[0], "true")

            # vGPU 的统计规则，如果查到这个节点的 vGPU 数量存在, 代表这这张卡已被占用, len 统计被占用的卡数
            vGPUCorePercentage = "sum(vGPUCorePercentage) by (nodename, deviceuuid)"
            vgpu = filtered_prometheus_column(PrometheusClient().expression_execute(vGPUCorePercentage),
                                              node_status[0], 'nodename')
            if vgpu:
                already_used_vgpu_card = already_used_vgpu_card + len(vgpu)
        if node_status[2] == HUAWEI and node_status[4] == "vgpu":
            num_vnpu = num_vnpu + 1
            num_vnpu_card = num_vnpu_card + rg_db.get_gpu_static_info(node_status[0], "true")

            # 统计 vnpu 占用的卡
            gpu_expression = "sum(vGPUCorePercentage) by (nodename, deviceuuid)"
            cvnpu = filtered_prometheus_column(PrometheusClient().expression_execute(gpu_expression),
                                               node_status[0], 'nodename')
            if cvnpu:
                already_used_vnpu_card = already_used_vnpu_card + len(cvnpu)
        if node_status[2] == HYGON and node_status[4] == "vgpu":
            num_vdcu = num_vdcu + 1
            num_vdcu_card = num_vdcu_card + rg_db.get_gpu_static_info(node_status[0], "true")

            # vDCU 的统计规则，如果查到这个节点的 vDCU 数量存在, 代表这这张卡已被占用, len 统计被占用的卡数
            vdc_expression = "sum(dcu_utilizationrate) by (node, device_id)"
            vdcu_utilizationrate = PrometheusClient().expression_execute(vdc_expression)
            vdcu = filtered_prometheus_column(vdcu_utilizationrate, node_status[0], 'node')
            if vdcu:
                already_used_vdcu_card = already_used_vdcu_card + len(vdcu)
        if node_status[4] == "cpu":
            num_cpu = num_cpu + 1

    total_node = num_gpu + num_npu + num_hfk + num_dcu + num_vgpu + num_vnpu + num_vdcu + num_cpu
    total_gpu_card = num_gpu_card + num_npu_card + num_hfk_card + num_dcu_card + num_vgpu_card + num_vnpu_card + num_vdcu_card
    # 这个 already_used_gpu_card ，already_used_npu_card 包括 vGPU、 vNPU、vDCU
    total_available_gpu_cards = (total_gpu_card - already_used_gpu_card - already_used_hfx_card - already_used_dcu_card
                                 - already_used_npu_card - already_used_vnpu_card - already_used_vdcu_card)
    total_unavailable_gpu = total_gpu_card - len(gpu_util) - len(hf_gpu_util) - len(
        npu_chip_info_utilization) - len(dcu_utilizationrate)

    gpu_nodes = []
    gpu_nodes.append({"Nvidia": num_gpu + num_vgpu}) if num_gpu > 0 else ""
    gpu_nodes.append({"Hexaflake": num_hfk}) if num_hfk > 0 else ""
    gpu_nodes.append({"NPU": num_npu + num_vnpu}) if num_npu > 0 else ""
    gpu_nodes.append({"DCU": num_dcu + num_vdcu}) if num_dcu > 0 else ""
    gpu_nodes.append({"Master": num_cpu}) if num_cpu > 0 else ""

    gpu_cards = []
    gpu_cards.append({"Nvidia": num_gpu_card}) if num_gpu_card > 0 else ""
    gpu_cards.append({"Hexaflake": num_hfk_card}) if num_hfk_card > 0 else ""
    gpu_cards.append({"NPU": num_npu_card}) if num_npu_card > 0 else ""
    gpu_cards.append({"DCU": num_dcu_card}) if num_dcu_card > 0 else ""
    gpu_cards.append({"vGPU": num_vgpu_card}) if num_vgpu_card > 0 else ""
    gpu_cards.append({"vNPU": num_vnpu_card}) if num_vnpu_card > 0 else ""
    gpu_cards.append({"vDCU": num_vdcu_card}) if num_vdcu_card > 0 else ""

    available_gpu_cards = []
    available_gpu_cards.append({"Nvidia": num_gpu_card - already_used_gpu_card}) if num_gpu_card > 0 else ""
    # to do 等 hfk 能获取容器信息后，再加 num_hfk_card - len(hf_gpu_util)，现在先一直保持有多少卡就是多少个可用
    available_gpu_cards.append({"Hexaflake": num_hfk_card}) if num_hfk_card > 0 else ""
    available_gpu_cards.append({"NPU": num_npu_card - already_used_npu_card}) if num_npu_card > 0 else ""
    available_gpu_cards.append({"DCU": num_dcu_card - already_used_dcu_card}) if num_dcu_card > 0 else ""
    available_gpu_cards.append({"vNPU": num_vnpu_card - already_used_vnpu_card}) if num_vnpu_card > 0 else ""
    available_gpu_cards.append({"vDCU": num_vdcu_card - already_used_vdcu_card}) if num_vdcu_card > 0 else ""
    available_gpu_cards.append({"vGPU": num_vgpu_card - already_used_vgpu_card}) if num_vgpu_card > 0 else ""

    unavailable_gpu_cards = []
    unavailable_gpu_cards.append({"Nvidia": num_gpu_card + num_vgpu_card - len(gpu_util)}) if num_gpu > 0 else ""
    unavailable_gpu_cards.append({"Hexaflake": num_hfk_card - len(hf_gpu_util)}) if num_hfk > 0 else ""
    unavailable_gpu_cards.append(
        {"NPU": num_npu_card + num_vnpu_card - len(npu_chip_info_utilization)}) if num_npu_card > 0 else ""
    unavailable_gpu_cards.append(
        {"DCU": num_dcu_card + num_vdcu_card - len(dcu_utilizationrate)}) if num_dcu_card > 0 else ""

    # pod info
    nb_cards, tn_cards, inf_cards = 0, 0, 0
    if nb_prom:
        # 该数组定义的目的是，特殊处理因 vnpu 的网卡数不对的问题，强制改成 1 ，现在是 aicore
        nb_prom_arr = []
        for item in nb_prom:
            if item['metric']["resource"] == 'qingcloud_adapter_com_adapter':
                item['value'][1] = '1'
            nb_prom_arr.append(item)
        nb_prom = nb_prom_arr
        nb_cards = sum(int(item["value"][1]) for item in nb_prom)
    if tn_prom:
        tn_prom_arr = []
        for item in tn_prom:
            if item['metric']["resource"] == 'qingcloud_adapter_com_adapter':
                item['value'][1] = '1'
            tn_prom_arr.append(item)
        tn_prom = tn_prom_arr
        tn_cards = sum(int(item["value"][1]) for item in tn_prom)
    if inf_prom:
        inf_prom_arr = []
        for item in inf_prom:
            if item['metric']["resource"] == 'qingcloud_adapter_com_adapter':
                item['value'][1] = '1'
            inf_prom_arr.append(item)
        inf_prom = inf_prom_arr
        inf_cards = sum(int(item["value"][1]) for item in inf_prom)

    # namespace info
    user_daily_info = rg_db.list_user_info("today")
    user_all_info = rg_db.list_user_info("all", "true")

    data = {
        "cluster_info": {
            "total_gpu_node": total_node,
            "gpu_nodes": gpu_nodes,
            "total_gpu_card": total_gpu_card,
            "gpu_cards": gpu_cards,
            "total_available_gpu_cards": total_available_gpu_cards,
            "available_gpu_cards": available_gpu_cards,
            "remaining_gpu_cards": total_gpu_card - total_available_gpu_cards,
            "total_unavailable_gpu_cards": total_unavailable_gpu,
            "unavailable_gpu_cards": unavailable_gpu_cards
        }
    }, {
        "pod_info": {
            "nb_pods": len(nb_prom),
            "nb_cards": nb_cards,
            "nb_list": nb_prom,
            "tn_pods": len(tn_prom),
            "tn_cards": tn_cards,
            "tn_list": tn_prom,
            "inf_pods": len(inf_prom),
            "inf_cards": inf_cards,
            "inf_list": inf_prom
        }
    }, {
        "namespace_info": {
            "total_namespace": user_all_info,
            "daily_namespace": len(user_daily_info),
            "daily_info": user_daily_info
        }
    }

    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/list_dcu_dev_info", response_model=GenericMultipleResponse[Dict])
def list_dcu_dev_info(
        search_word: str = Query(description="gpu_node_id搜索", default=None),
        gpu_product: str = Query(description="显卡类型", default=HYGON),
        gpu_type: str = Query(description="虚拟化类型", default="gpu"),
        compute_group: str = Query(description="资源池", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU 资源组
    """
    # DCU 利用率
    dcu_utilizationrate = PrometheusClient().expression_execute("dcu_utilizationrate")
    # DCU 显存占用
    dcu_usedmemory_bytes = PrometheusClient().expression_execute("dcu_usedmemory_bytes")
    # DCU 显存总量
    dcu_memorycap_bytes = PrometheusClient().expression_execute("dcu_memorycap_bytes")
    # PCIe 显存带宽
    dcu_pciebw_mb = PrometheusClient().expression_execute("dcu_pciebw_mb")
    # DCU 不可恢复错误数量
    dcu_ce_count = PrometheusClient().expression_execute("sum(dcu_ce_count) by (device_id)")
    # DCU 可恢复错误数量
    dcu_ue_count = PrometheusClient().expression_execute("sum(dcu_ue_count) by (device_id)")
    # # 虚拟计算加速卡利用率
    # vdcu_utilizationrate = PrometheusClient().expression_execute("vdcu_utilizationrate")
    # # 虚拟计算加速卡已使用显存量
    # vdcu_usedmemory_bytes = PrometheusClient().expression_execute("vdcu_usedmemory_bytes")

    # 暂时不显示这些指标，这些指标跟算力切分有关，但跟监控无关
    # # DCU 计算单元数量
    # dcu_compute_unit_count = PrometheusClient().expression_execute("dcu_compute_unit_count")
    # # DCU 可用于切分虚拟计算卡的显存余量
    # dcu_memory_remaining = PrometheusClient().expression_execute("dcu_memory_remaining")
    # # DCU 可用于切分虚拟计算卡的计算单元余量
    # dcu_compute_unit_remaining_count = PrometheusClient().expression_execute("dcu_compute_unit_remaining_count")

    data = []
    gpu_details = rg_db.get_gpu_status(search_word, gpu_type, gpu_product)

    for gpu_detail in gpu_details:
        gpu_node_id = gpu_detail[0]
        gpu_node_status = gpu_detail[1]

        #  Get node static info
        node_static_info = rg_db.get_node_static_info(gpu_node_id, compute_group=compute_group)
        if node_static_info is not None:
            gpu_node_ip = getattr(node_static_info, "ip")
            gpu_node_compute_group = getattr(node_static_info, "compute_group")

            pool_name = ""
            if gpu_node_compute_group is not None:
                get_resource_pool = rg_db.get_resource_pool(gpu_node_compute_group)
                if get_resource_pool is not None:
                    pool_name = getattr(get_resource_pool, "pool_name")

            available_gpu_fb = len(list(filter(
                lambda item: item['metric']['node'] == gpu_node_id, dcu_utilizationrate)))

            gpu_static_info = rg_db.get_gpu_static_info(gpu_node_id)
            for static_info in gpu_static_info:
                # init
                dev_gpu_name = ""
                dev_gpu_util = 0
                dev_gpu_pciebw_mb = 0.000
                dev_gpu_mem_total = 0.0
                dev_gpu_mem_used = 0.0
                dev_gpu_ce_count, dev_gpu_ue_count = 0, 0
                exported_container = ""
                exported_namespace = ""
                exported_pod = ""
                dev_gpu_status = "2"  # Abnormal

                gpu_uuid = getattr(static_info, "gpu_uuid")
                if dcu_utilizationrate:
                    dcu_containers_data = filtered_schedule_util(dcu_utilizationrate, gpu_node_id, gpu_uuid, 'node',
                                                                 'device_id')

                    if len(dcu_containers_data) != 0:
                        dev_gpu_util = dcu_containers_data[0]['value'][1]
                        dev_gpu_status = "0"  # Normal

                        metric_data = dcu_containers_data[0]['metric']
                        dev_gpu_name = metric_data['name']

                        if metric_data.get('exported_container') is not None:
                            exported_container = metric_data['exported_container']
                            exported_namespace = metric_data['dcu_pod_namespace']
                            exported_pod = metric_data['dcu_pod_name']

                # if vdcu_utilizationrate:
                #     vdcu_containers_data = filtered_schedule_util(vdcu_utilizationrate, gpu_node_id, gpu_uuid, 'node',
                #                                                   'device_id')
                #     vdcu_usedmemory_bytes = filtered_schedule_util(vdcu_usedmemory_bytes, gpu_node_id, gpu_uuid, 'node',
                #                                                    'device_id')
                #
                #     if len(vdcu_containers_data) != 0 and len(vdcu_usedmemory_bytes) != 0:
                #         dev_gpu_util = vdcu_containers_data[0]['value'][1]
                #
                #         metric_data = vdcu_containers_data[0]['metric']
                #         dev_gpu_name = metric_data['name']
                #
                #         exported_container = metric_data['exported_container']
                #         exported_namespace = metric_data['dcu_pod_namespace']
                #         exported_pod = metric_data['dcu_pod_name']

                if dcu_memorycap_bytes:
                    dev_fb_total = filtered_schedule_util(dcu_memorycap_bytes, gpu_node_id, gpu_uuid, 'node',
                                                          'device_id')
                    if len(dev_fb_total) != 0:
                        dev_gpu_mem_total = "{:.2f}".format(float(dev_fb_total[0]['value'][1]) / 1024 / 1024 / 1024)

                if dcu_usedmemory_bytes:
                    gpu_mem_used = filtered_schedule_util(dcu_usedmemory_bytes, gpu_node_id, gpu_uuid, 'node',
                                                          'device_id')
                    if len(gpu_mem_used) != 0:
                        dev_gpu_mem_used = "{:.2f}".format(float(gpu_mem_used[0]['value'][1]) / 1024 / 1024 / 1024)

                if dcu_pciebw_mb:
                    dev_gpu_pciebw_mb = filtered_schedule_util(dcu_pciebw_mb, gpu_node_id, gpu_uuid, 'node',
                                                               'device_id')
                    if len(dev_gpu_pciebw_mb) != 0:
                        dev_gpu_pciebw_mb = dev_gpu_pciebw_mb[0]['value'][1]

                if dcu_ce_count and dcu_ue_count:
                    dcu_ce_count = filtered_schedule_util(dcu_ce_count, gpu_node_id, gpu_uuid, 'node',
                                                          'device_id')
                    dcu_ue_count = filtered_schedule_util(dcu_ue_count, gpu_node_id, gpu_uuid, 'node',
                                                          'device_id')
                    if len(dcu_ce_count) != 0 and len(dcu_ue_count) != 0:
                        dev_gpu_ce_count = dcu_ce_count
                        dev_gpu_ue_count = dcu_ue_count

                gpu_dev_detail = {"gpu_node_id": gpu_node_id,
                                  "gpu_node_status": gpu_node_status,
                                  "gpu_node_ip": gpu_node_ip,
                                  "gpu_node_compute_group": gpu_node_compute_group,
                                  "gpu_node_compute_group_name": pool_name,
                                  "dev_gpu_available": available_gpu_fb,
                                  "dev_gpu_uuid": gpu_uuid,
                                  "dev_gpu_name": dev_gpu_name,
                                  "dev_gpu_status": dev_gpu_status,
                                  "dev_gpu_util": dev_gpu_util,
                                  "dev_gpu_pciebw_mb": dev_gpu_pciebw_mb,
                                  "dev_gpu_mem_used": dev_gpu_mem_used,
                                  "dev_gpu_mem_total": dev_gpu_mem_total,
                                  "dev_gpu_ce_count": dev_gpu_ce_count,
                                  "dev_gpu_ue_count": dev_gpu_ue_count,
                                  "exported_container": exported_container,
                                  "exported_namespace": exported_namespace,
                                  "exported_pod": exported_pod
                                  }
                data.append(gpu_dev_detail)
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/extract_enabled_table_views", response_model=GenericMultipleResponse[Dict])
def extract_enabled_table_views(
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    """
    查询 GPU table view 监控配置列表
    """
    data = []
    dashboards = rg_db.list_gpu_dashboard_config()
    """
    从数据库记录中提取 enable_table_view 不为 0 的项
    返回格式: {web_router: enable_table_view}
    """
    result = {}
    for gpuDashboardConfig in dashboards:
        # 检查 enable_table_view 是否不是 "0"

        if gpuDashboardConfig.enable_table_view != "0":
            dashboard_id = gpuDashboardConfig.dashboard_id
            if dashboard_id:  # 确保 web_router 存在
                result[dashboard_id] = gpuDashboardConfig.enable_table_view

    # 处理特殊指标类型 - 不区分 GPU/vGPU
    special_types = ["4", "5"]

    # 拿 dashboard_id 当做转换条件，一方面考虑到存量客户和其他客户，另外一方面 dashboard_id 为主键，不能替换
    prefix_mapping = {
        "1": "gpu-nvidia",
        "3": "npu-ascend",
        "4": "infiniband",
        "5": "hexaflake",
        "6": "dcu-hygon"
    }

    for dashboard_id, status_str in result.items():
        # 分割状态字符串并处理格式问题
        parts = status_str.strip().split(',')

        # 获取第一个状态值
        status_value = 0
        if parts and parts[0].isdigit():
            status_value = int(parts[0])

        # 获取指标前缀
        prefix = prefix_mapping.get(dashboard_id, dashboard_id)

        # 特殊处理：不区分 GPU/vGPU 的指标
        if dashboard_id in special_types:
            data.append({
                "dashboard_id": dashboard_id,
                prefix: status_value
            })
        # 正常处理：区分 GPU/vGPU 的指标
        else:
            # 获取第二个状态值（vGPU状态）
            vgpu_status = 0
            if len(parts) > 1 and parts[1].isdigit():
                vgpu_status = int(parts[1])

            # 创建转换后的字典
            data.append({
                "dashboard_id": dashboard_id,
                f"{prefix}": status_value,
                f"v{prefix}": vgpu_status
            })
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/global_monitoring_interface", response_model=GenericMultipleResponse[Dict])
def global_monitoring_interface(
        node: str = Query(description="GPU 节点名称", default=None),
        device_num: str = Query(description="GPU 显卡数", default=None),
        device_name: str = Query(description="GPU 型号", default=None),
        start_at: float = Query(description="开始时间", default=None),
        end_at: float = Query(description="结束时间", default=None),
        # template 请参考 Template_Handlers
        template: str = Query(description="全局监控模板类型", default="GPU_Monitor_Overview")
):
    get_public_metrics = MonitoringOperator().get_public_metrics
    monitoringQueryParams = MonitoringQueryParams(None)
    # 获取 node, device_num, device_name 的 labels
    gpu_node_labels = transform_label_metrics(get_public_metrics(GPU_Monitor_Metrics_Labels,
                                                                 monitoringQueryParams))
    # 定义替换映射, 可扩展，当入参为 None 时，查询全部
    replacements = {
        "$1": gpu_node_labels.get('GPU_Node_Labels') if node is None else node,
        "$2": gpu_node_labels.get('GPU_Device_Num_Labels') if device_num is None else device_num,
        "$3": ".*" if device_name is None else device_name
    }

    if template in Template_Handlers:
        metrics = Template_Handlers[template]()

        if template == "GPU_Monitor_Overview":
            data = [{
                "AICP_Overview_Node_Status":
                    get_public_metrics(AICP_Overview_Node_Status, monitoringQueryParams),
                "AICP_Overview_Manager_Node":
                    get_public_metrics(AICP_Overview_Manager_Node, monitoringQueryParams),
                "AICP_Overview_GPU_Cards":
                    get_public_metrics(replace_with_regex(AICP_Overview_GPU_Cards, replacements),
                                       monitoringQueryParams),
                "AICP_Overview_Work_Nodes":
                    get_public_metrics(replace_with_regex(AICP_Overview_Work_Nodes, replacements),
                                       monitoringQueryParams)
            }]
        else:
            # 默认计算5分钟前的时间戳（开始时间）
            end_timestamp = int(time.time())
            start_timestamp = end_timestamp - 5 * 60  # 5分钟 = 300秒
            data = get_public_metrics(replace_with_regex(metrics, replacements),
                                      MonitoringQueryParams(None,
                                                            start_at=start_timestamp if start_at is None else start_at,
                                                            end_at=end_timestamp if end_at is None else end_at))
    return GenericMultipleResponse[Dict](data=data, counts=len(data))


@router.get("/global_monitoring_labels", response_model=GenericMultipleResponse[Dict])
def global_monitoring_labels():
    gpu_node_labels = [transform_label_metrics(
        MonitoringOperator().get_public_metrics(GPU_Monitor_Metrics_Labels, MonitoringQueryParams(None)))]
    converted_data = [{k: v.split('|') for k, v in item.items()} for item in gpu_node_labels]
    return GenericMultipleResponse[Dict](data=converted_data, counts=len(converted_data))

@router.get("/global_pool_interface", response_model=GenericMultipleResponse[Dict])
def global_pool_interface(
        resource_pool: str = Query(description="资源池id", default=None),
        rg_db: GpuCrud = Depends(get_gpu_crud)
):
    node_statics = rg_db.list_node_static_info(compute_group=resource_pool)
    node_ids = '|'.join(str(NodeStaticInfo.hostname) for NodeStaticInfo in node_statics)
    logger.info(node_ids)
    # 定义替换映射, 可扩展，当入参为 None 时，查询全部
    replacements = {
        "$1": node_ids
    }
    get_public_metrics = MonitoringOperator().get_public_metrics
    data = get_public_metrics(replace_with_regex(GPU_Pool_Metrics_Labels, replacements), MonitoringQueryParams(None))
    return GenericMultipleResponse[Dict](data=data, counts=len(data))
