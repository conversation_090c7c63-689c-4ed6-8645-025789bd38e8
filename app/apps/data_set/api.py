from datetime import datetime
from typing import Dict

from fastapi import APIRouter, Depends, Query, Request

from app import logger
from app.common.response import RESOURCE_NOT_FOUND,ERROR_DELETE_RESOURCE
from app.cruds.data_set import DataSetCrud
from app.apps.data_set.dependencies import get_data_crud
from app.models.data_set import DataSet, DataSetCreate, DataSetListRep
from app.core.response import BaseGenericResponse, GenericMultipleResponse, GenericSingleResponse

from app.core.middlewares.auth.qingcloud_auth import QingcloudUser

router = APIRouter(prefix="/data", tags=["data"])


@router.get("", response_model=GenericMultipleResponse[DataSetListRep])
def get_data_set(
        request: Request,
        da_db: DataSetCrud = Depends(get_data_crud),
        offset: int = 0,
        limit: int = 20,
        reverse: bool = False,
        order_by: str = 'create_time',
        search_word: str = None
):
    """
    获取数据集列表
    """
    user: QingcloudUser = request.user
    logger.info(f"当前排序项{reverse},排序字段{order_by}")
    data_sets = da_db.get_da_by_user(user.user_id,
                                     order_by=order_by,
                                     reverse=reverse,
                                     search_word=search_word,
                                     offset=offset,
                                     limit=limit)
    if not data_sets:
        logger.info("当前未预置数据集")
        return GenericMultipleResponse[DataSetListRep](data=[], counts=0)
    data_set_list = []
    total_count = 0
    counts = da_db.get_data_count(search_word=search_word)
    sorted_list = sorted(data_sets, key=lambda x: x.create_time if x.create_time is not None else datetime.min, reverse=reverse)
    for item in sorted_list:
        data_set = DataSetListRep(**dict(item), total_count=total_count)
        data_set_list.append(data_set)

    return GenericMultipleResponse[DataSetListRep](data=data_set_list,
                                                   counts=counts)


@router.post("", response_model=GenericSingleResponse[DataSetListRep])
def create_resource_group(
        request: Request,
        data_set_create: DataSetCreate,
        pk_db: DataSetCrud = Depends(get_data_crud)
):
    """
    添加数据集
    """
    user = request.user

    resource_groups = pk_db.get_da_by_user(user.user_id)
    rep = GenericSingleResponse[Dict](data={})

    if resource_groups:
        for pk_data in resource_groups:
            if data_set_create.data_name == pk_data.data_name:
                logger.info("当前数据集已存在")
                rep.ret_code = -1
                rep.message = "当前数据集已存在,请重新输入"
                return rep
    public_key = pk_db.create_data_set(data_set_create, user)
    return GenericSingleResponse[DataSet](data=public_key)


@router.delete("", response_model=BaseGenericResponse)
def delete_data_set(
        request: Request,
        da_id: str = Query(description="多个参数之间用逗号分割"),
        da_db: DataSetCrud = Depends(get_data_crud)
):
    """
    删除数据集
    """
    rep = BaseGenericResponse()
    user = request.user
    da_ids = da_id.split(",")
    for da_id in da_ids:
        if not da_db.delete_da(da_id, user):
            rep.ret_code = RESOURCE_NOT_FOUND
            rep.message = ERROR_DELETE_RESOURCE
            return rep
    return rep


@router.put("", response_model=GenericSingleResponse[DataSet])
def update_data_set(
        request: Request,
        data_set_update: DataSetCreate,
        da_db: DataSetCrud = Depends(get_data_crud)
):
    """
    修改数据集
    """
    user = request.user
    data_set = da_db.update_data(data_set_update, user)
    return GenericSingleResponse[DataSet](data=data_set)





