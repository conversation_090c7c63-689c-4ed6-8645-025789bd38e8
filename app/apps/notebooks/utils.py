import json
import time
import async<PERSON>
from datetime import datetime

import app
from app import logger
from app.apps.notebooks import Notebook
from app.apps.notebooks.kr8s_objects.imagebuilder import ImageBuilder
from app.apps.notebooks.kr8s_objects.notebook import NoteBookCrOperator
from app.core.utils import log_background_task_exception
from app.cruds.operation_record import OperationR<PERSON>ord<PERSON>rud
from app.models.notebooks import NotebookSaveToImage
from app.models.operation_record import OperationRecord
from app.core.constant import RESOURCE_IMAGE
from app.core.kube.api import get_custom_rsrc
from app.core.qingcloud.common import send_to_push_server
from app.core.qingcloud.interface import ZONE_INFO, send_message_request, INSTANCE_ERROR
from app.core.redis.redis_client import create_redis_data, producer, redis_client
from app.core.middlewares.operation.reflection_api import URLPrefix, url_reflection_type
from app.core.middlewares.operation.comm_const import OPERATION
from app.core.config import request_id_context


@log_background_task_exception
def build_image_done(req: NotebookSaveToImage, notebook: Notebook, op_session: OperationRecordCrud, push_user_id):
    op_id = op_session.generate_id()

    send_to_push_server(action="创建镜像", resource=req.image_name,
                        status="Creating", reason="Creating",
                        user_id=notebook.user_id, op_id=op_id, resource_type=RESOURCE_IMAGE, extend_user_id=notebook.operation_user_id)
    notebook_cr = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)

    try:
        notebook_cr.precheck_for_save_image()
        ib: ImageBuilder = notebook_cr.save_image(to=f"{app.settings.DOCKER_REGISTRY}/{req.image_name}:{req.image_tag}")
        ib.wait_for_success()
        send_to_push_server(action="创建镜像", resource=req.image_name,
                            status="Succeeded", reason="创建镜像成功",
                            user_id=notebook.user_id, op_id=op_id, resource_type=RESOURCE_IMAGE,extend_user_id=notebook.operation_user_id)
    except Exception as e:
        logger.error(f"save image failed: {e}")
        send_data = {
            "zone": ZONE_INFO,
            "id": notebook.uuid,
            "name": notebook.name,
            "spec": f"{notebook.replica_specs.custom_cpu}核 {notebook.replica_specs.custom_memory}G {notebook.replica_specs.custom_gpu_type} {notebook.replica_specs.custom_gpu_memory}G * {notebook.replica_specs.custom_gpu}" if notebook.replica_specs.custom_aipods_type != "only_cpu" else f"{notebook.replica_specs.custom_cpu}核 {notebook.replica_specs.custom_memory}G",
            "action": "容器实例保存镜像失败"
        }
        send_to_push_server(action=f"创建镜像失败: [{str(e)}]", resource=req.image_name,
                            status="Failed", reason=str(e),
                            user_id=notebook.user_id, op_id=op_id, resource_type=RESOURCE_IMAGE, extend_user_id=notebook.operation_user_id)
        send_message_request(INSTANCE_ERROR, notebook.user_id, json.dumps(send_data))
        if push_user_id != notebook.user_id:
            send_message_request(INSTANCE_ERROR, push_user_id, json.dumps(send_data))
    #
    # group = "imagebuilder.ai.qingcloud.com"
    # version = "v1"
    # kind = "imagebuilders"
    # n = 600
    # while n > 0:
    #     time.sleep(10)
    #     result = get_custom_rsrc(group=group, version=version, kind=kind, namespace=user.root_user_id.lower(),
    #                              name=image_build_name)
    #     n = n - 10
    #     if result and "status" in result:
    #         if result["status"]["state"] != status:
    #             status = result["status"]["state"]
    #             send_to_push_server(action="创建镜像", resource=image_name,
    #                                 status=status, reason=result["status"].get("reason"),
    #                                 user_id=user.user_id,
    #                                 op_id=op_id,
    #                                 resource_type=RESOURCE_IMAGE)
    #         if result["status"]["state"] == "Succeeded":
    #             logger.debug("save image successful")
    #             return
    #         if result["status"]["state"] == "Failed":
    #             logger.error("save to image filed [%s]", result["status"].get("reason"))
    #             # Send message to IAAS.
    #             send_data = {
    #                 "zone": ZONE_INFO,
    #                 "id": notebook.uuid,
    #                 "spec": f"{notebook.replica_specs.custom_cpu}核 {notebook.replica_specs.custom_memory}G {notebook.replica_specs.custom_gpu_type} {notebook.replica_specs.custom_gpu_memory}G * {notebook.replica_specs.custom_gpu}" if notebook.replica_specs.custom_aipods_type != "only_cpu" else f"{notebook.replica_specs.custom_cpu}核 {notebook.replica_specs.custom_memory}G",
    #                 "action": "容器实例保存镜像失败"
    #             }
    #             send_message_request(INSTANCE_ERROR, notebook.user_id, json.dumps(send_data))
    #             if push_user_id != notebook.user_id:
    #                 send_message_request(INSTANCE_ERROR, push_user_id, json.dumps(send_data))
    #             return
