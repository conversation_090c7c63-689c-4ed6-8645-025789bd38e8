from app.core.exceptions import AICPBaseException


class NotebookDeleteException(AICPBaseException):
    BASE_CODE = 3001
    BASE_MESSAGE = "删除训练作业失败"


class NotebookCreateException(AICPBaseException):
    BASE_CODE = 3002
    BASE_MESSAGE = "创建容器实例失败"


class NotebookImagebuilderRunningException(AICPBaseException):
    BASE_CODE = 3003
    BASE_MESSAGE = "镜像构建中，请稍后再试"


class NotebookNotRunningException(AICPBaseException):
    BASE_CODE = 3004
    BASE_MESSAGE = "容器实例未运行"


class NotebookNotStoppedException(AICPBaseException):
    BASE_CODE = 3005
    BASE_MESSAGE = "容器实例未暂停"


class NotebookNotHaveGPUException(AICPBaseException):
    BASE_CODE = 3010
    BASE_MESSAGE = "容器实例未分配GPU"


class NotebookQuotaException(AICPBaseException):
    BASE_CODE = 3004
    BASE_MESSAGE = "{resource}申请失败:当前配额不足(总配额:{total},已使用:{used},申请数:{required}),请联系管理员申请更多配额或释放现有资源"

    def __init__(self, resource, total, used, required):
        final_message = self.BASE_MESSAGE.format(
            resource=resource,
            total=total,
            used=used,
            required=required
        )
        self.code = self.BASE_CODE
        super().__init__(final_message, use_base_message=False)


class SkuNotExistsException(AICPBaseException):
    BASE_CODE = 3005
    BASE_MESSAGE = "当前规格不存在, 请重新选择规格后再进行开机重试."


class SkuNotEnoughException(AICPBaseException):
    BASE_CODE = 3006
    BASE_MESSAGE = "当前规格资源不足, 请重新选择规格后再进行开机重试, 或提交工单后稍后再试. "


class ResourceGroupNotebookNotSupportChangeException(AICPBaseException):
    BASE_CODE = 3007
    BASE_MESSAGE = "资源组实例暂不支持修改规格操作"


class VolumeNotExistsException(AICPBaseException):
    BASE_CODE = 3011
    BASE_MESSAGE = "存储卷不存在"


class VolumeExistsException(AICPBaseException):
    BASE_CODE = 3011
    BASE_MESSAGE = "存储卷已存在"


class ResourceGroupNodeNotFoundException(AICPBaseException):
    BASE_CODE = 3011
    BASE_MESSAGE = "资源组无可用节点"


class DockerRepoNotExistsException(AICPBaseException):
    BASE_CODE = 3012
    BASE_MESSAGE = "自定义镜像仓库命名空间不存在, 请先创建命名空间."


class CannotChangeResourceGroupWithLocalDiskException(AICPBaseException):
    BASE_CODE = 3013
    BASE_MESSAGE = "存在本地盘的资源组实例暂不支持修改资源组操作"


class DockerRepoNotMatchException(AICPBaseException):
    BASE_CODE = 3014
    BASE_MESSAGE = "自定义镜像仓库不匹配, 请重新选择镜像仓库后再进行保存."


class ImageBuilderCreatingException(AICPBaseException):
    BASE_CODE = 3015
    BASE_MESSAGE = "镜像构建中, 请稍后再试."


class HarborQuotaExcceedException(AICPBaseException):
    BASE_CODE = 3016
    BASE_MESSAGE = "镜像仓库配额不足."


class NoHarborException(AICPBaseException):
    BASE_CODE = 3017
    BASE_MESSAGE = "未配置镜像仓库, 请先创建."


class HarborAuthException(AICPBaseException):
    BASE_CODE = 3018
    BASE_MESSAGE = "镜像仓库认证失败, 请检查镜像仓库配置."


class ImageBuilderTimeoutException(AICPBaseException):
    BASE_CODE = 3019
    BASE_MESSAGE = "镜像构建超时, 请稍后再试."


class ImageBuildFailedException(AICPBaseException):
    BASE_CODE = 3020
    BASE_MESSAGE = "镜像构建失败, 请稍后再试."

class ImageBuilderCheckDiskSpaceExcceedException(AICPBaseException):
    BASE_CODE = 3021
    BASE_MESSAGE = "镜像构建磁盘不足."
