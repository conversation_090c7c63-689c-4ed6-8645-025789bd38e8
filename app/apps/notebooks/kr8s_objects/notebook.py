import dataclasses
import datetime
import hashlib
import string
import time
import re
from copy import deepcopy
from functools import cached_property
from hashlib import md5
from typing import Dict, List, Optional, Union

from kr8s import NotFoundError
from kr8s._async_utils import run_sync, sync
from kr8s._objects import APIObject
from kr8s.asyncio.objects import Event, Pod as AsyncPod
from kr8s.objects import NetworkPolicy, Service, StatefulSet, Pod

import app
from app import logger
from app.apps.notebooks.exceptions import ImageBuilderCreatingException, NoHarborException
from app.core.exceptions import DuplicatePortException, InvalidTargetPortException, TooManyPortsException
from app.apps.notebooks.kr8s_objects.imagebuilder import ImageBuilder, ImageBuilderOperator
from app.core.constant import NOTEBOOK_PRE_IMAGE_INFO_KEY, NOTEBOOK_USER_VOLUME_TYPES, PASSWORD_CHARS, UserFrom
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.core.qingcloud.docker_api import QingcloudDockerApiClient
from app.models.notebooks import Notebook as NotebookModel, NotebookReason, NotebookStatus, NotebookCreate, \
    NotebookVolumeSpec
from app.core.kube.api import kr8s_event_timestamp
from app.core.kube.api.dockercfg import DockerCfgSecret
from app.core.models import CustomImageSecret, QingcloudUser
from app.core.qingcloud.resource import ProductCenterResource
from app.core.volumes.manager import VolumesDefinition, VolumesManager
from app.core.volumes.utils import create_k8s_secret, get_envs_definitions
from app.models.resource_group import TemplateSpecType
from app.core.db import get_db_session_local

EVENT_TYPE_WARNING = "Warning"
STOP_ANNOTATION = "kubeflow-resource-stopped"
STOPPING_ANNOTATION = "kubeflow-resource-stopping"

MPF = re.compile(app.settings.MANUAL_PVC_FILTER)

def generate_notebook_init_password(notebook_id: str, salt: str = "aicp") -> str:
    """
    生成 notebook init password
    """
    # use notebook_id md5 hash to generate password
    return md5(f"{salt}-{notebook_id}".encode()).hexdigest()[:8]


def generate_notebook_init_password_v2(notebook_id: str, salt: str = "aicp") -> str:
    """
    生成 notebook init password

    Passwords are composed of uppercase letters, lowercase letters, numbers and special characters
    """
    combined = f"{notebook_id}{salt}"

    # 使用 SHA-256 哈希算法生成一个固定长度的哈希值
    hash_value = hashlib.sha256(combined.encode()).hexdigest()

    # 定义密码字符集
    lowercase = string.ascii_lowercase  # 小写字母
    uppercase = string.ascii_uppercase  # 大写字母
    digits = string.digits  # 数字
    special_chars = string.punctuation  # 特殊字符

    # 将所有字符集合并
    all_chars = lowercase + uppercase + digits + special_chars

    # 从哈希值中提取足够的随机性来生成密码
    password = []

    # 第一个字符：小写字母
    index = int(hash_value[0:2], 16) % len(lowercase)
    password.append(lowercase[index])

    # 第二个字符：大写字母
    index = int(hash_value[2:4], 16) % len(uppercase)
    password.append(uppercase[index])

    # 第三个字符：数字
    index = int(hash_value[4:6], 16) % len(digits)
    password.append(digits[index])

    # 第四个字符：特殊字符
    index = int(hash_value[6:8], 16) % len(special_chars)
    password.append(special_chars[index])

    # 剩余的字符：从所有字符集中随机选择
    for i in range(8, len(hash_value), 2):
        if len(password) >= 16:
            break
        # 将哈希值的每两个字符转换为一个整数
        index = int(hash_value[i:i + 2], 16) % len(all_chars)
        password.append(all_chars[index])

    # 将列表转换为字符串
    return ''.join(password)


def generate_notebook_init_password_v3(notebook_id: str, salt: str = "aicp") -> str:
    """
    生成 notebook init password

    Passwords are composed of uppercase letters, lowercase letters, numbers and special characters
    """
    combined = f"{notebook_id}{salt}"

    # 使用 SHA-256 哈希算法生成一个固定长度的哈希值
    hash_value = hashlib.sha256(combined.encode()).hexdigest()

    # 定义密码字符集
    lowercase = string.ascii_lowercase  # 小写字母
    uppercase = string.ascii_uppercase  # 大写字母
    digits = string.digits  # 数字
    special_chars = "`-|%@"  # 特殊字符

    # 将所有字符集合并
    all_chars = lowercase + uppercase + digits + special_chars

    # 从哈希值中提取足够的随机性来生成密码
    password = []

    # 第一个字符：小写字母
    index = int(hash_value[0:2], 16) % len(lowercase)
    password.append(lowercase[index])

    # 第二个字符：大写字母
    index = int(hash_value[2:4], 16) % len(uppercase)
    password.append(uppercase[index])

    # 第三个字符：数字
    index = int(hash_value[4:6], 16) % len(digits)
    password.append(digits[index])

    # 第四个字符：特殊字符
    index = int(hash_value[6:8], 16) % len(special_chars)
    password.append(special_chars[index])

    # 剩余的字符：从所有字符集中随机选择
    for i in range(8, len(hash_value), 2):
        if len(password) >= 16:
            break
        # 将哈希值的每两个字符转换为一个整数
        index = int(hash_value[i:i + 2], 16) % len(all_chars)
        password.append(all_chars[index])

    # 将列表转换为字符串
    return ''.join(password)


# noinspection PyClassHasNoInit
@sync
class NoteBook(APIObject):
    """NoteBook is the definition of a notebooks.
    """

    version = "kubeflow.org/v1beta1"
    endpoint = "notebooks"
    kind = "Notebook"
    plural = "notebooks"
    singular = "notebook"
    namespaced = True

    @classmethod
    def get_resource_definition(cls, notebook: NotebookModel, user: QingcloudUser,
                                docker_secret: CustomImageSecret = None, keys: str = None,
                                create_data: NotebookCreate = None) -> Dict:
        """
        获取资源定义
        """
        volumes_manager = VolumesDefinition(user, notebook.namespace, notebook.volume_specs)
        volumes_definitions = volumes_manager.get_volumes_definition()
        volume_mounts_definitions = volumes_manager.get_volumes_mounts_definition()

        special_envs = [
            {
                "name": "HOME",
                "value": "/root"
            },
            {
                "name": "AICP_CONTAINER",
                "value": "true"
            },
            {
                "name": "NB_INIT_PASSWORD",
                "value": generate_notebook_init_password_v3(notebook.uuid)
            }
        ]
        # NOTEBOOK PROXY to env
        if app.settings.NOTEBOOK_ENABLE_PROXY:
            special_envs.append({"name": "NOTEBOOK_ENABLE_PROXY", "value": "True"})
        else:
            special_envs.append({"name": "NOTEBOOK_ENABLE_PROXY", "value": "False"})
        source_envs = [
            {
                "name": "PIP_SOURCE",
                "value": create_data.pip
            },
            {
                "name": "CONDA_SOURCE",
                "value": create_data.conda
            },
            {
                "name": "APT_SOURCE",
                "value": create_data.apt
            }
        ]
        special_envs.extend(source_envs)
        # public env
        user_info = get_qingcloud_user_info_by_user_id(notebook.user_id)
        public_env = [
            {
                "name": "AICP_PLATFORM",
                "value": app.settings.AICP_PLATFORM
            },
            {
                "name": "AICP_TYPE",
                "value": "INSTANCE"
            },
            {
                "name": "AICP_USER_NAME",
                "value": user_info.get("user_name")
            },
            {
                "name": "AICP_NAME",
                "value": notebook.name
            },
            {
                "name": "AICP_SPEC_COUNT",
                "value": str(notebook.replica_specs.replicas)
            },
            {
                "name": "AICP_SPEC_GPU",
                "value": str(notebook.replica_specs.custom_gpu * notebook.replica_specs.replicas)
            },
            {
                "name": "AICP_SPEC_CPU",
                "value": str(notebook.replica_specs.custom_cpu)
            },
            {
                "name": "AICP_SPEC_MEMORY",
                "value": str(notebook.replica_specs.custom_memory)
            },
            {
                "name": "AICP_SPEC_GPU_MEMORY",
                "value": str(notebook.replica_specs.custom_gpu_memory)
            },
            {
                "name": "AICP_SPEC_GPU_NAME",
                "value": str(notebook.replica_specs.custom_gpu_type)
            },
            {
                "name": "AICP_SPEC_GPU_TYPE",
                "value": notebook.replica_specs.resource.get_product_gpu_type()
            },
            {
                "name": "AICP_HOST_MACHINE",
                "valueFrom": {
                    "fieldRef": {"fieldPath": "spec.nodeName"}
                }
            },
            {
                "name": "AICP_HOSTNAME",
                "valueFrom": {
                    "fieldRef": {"fieldPath": "metadata.name"}
                }
            }
        ]
        special_envs.extend(public_env)
        # private env
        if app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK != "":
            # get user's name from iaas.
            user_info = get_qingcloud_user_info_by_user_id(notebook.user_id)
            private_env = [
                {
                    "name": "AICP_TYPE",
                    "value": "INSTANCE"
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_USER_NAME",
                    "value": user_info.get("user_name")
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_NAME",
                    "value": notebook.name
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_COUNT",
                    "value": str(notebook.replica_specs.replicas)
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_GPU",
                    "value": str(notebook.replica_specs.custom_gpu * notebook.replica_specs.replicas)
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_CPU",
                    "value": str(notebook.replica_specs.custom_cpu)
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_MEMORY",
                    "value": str(notebook.replica_specs.custom_memory)
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_MEMORY",
                    "value": str(notebook.replica_specs.custom_gpu_memory)
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_GPU_NAME",
                    "value": str(notebook.replica_specs.custom_gpu_type)
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_SPEC_GPU_TYPE",
                    "value": notebook.replica_specs.resource.get_product_gpu_type()
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_HOST_MACHINE",
                    "valueFrom": {
                        "fieldRef": {"fieldPath": "spec.nodeName"}
                    }
                },
                {
                    "name": f"{app.settings.ENV_VARIABLE_PREFIX_NOTEBOOK}_HOSTNAME",
                    "valueFrom": {
                        "fieldRef": {"fieldPath": "metadata.name"}
                    }
                },
                {
                    "name": "TZ",
                    "value": "Asia/Shanghai"
                }
            ]
            special_envs.extend(private_env)
        # Write GPFS information to env
        count = 0
        for vm in volumes_manager.volume_specs:
            if vm.volume_type.upper() == "GPFS":
                value = vm.mount_path
                permission = "(只读)" if vm.permission == "ro" else "(读写)"
                special_envs.extend([
                    {
                        "name": "GPFS_MOUNT_POINT_%d" % count,
                        "value": value + " " + permission
                    },
                    {
                        "name": "GPFS_MOUNT_POINT_PATH_%d" % count,
                        "value": value
                    }
                ])
                count += 1
        envs = get_envs_definitions(notebook.envs, special_envs)
        notebook.replica_specs.resource.append_envs(envs)

        if keys:
            secret_name = f"ssh-key-secrets-{notebook.uuid}"
            create_k8s_secret(notebook.namespace, secret_name, keys)

            volumes_definitions.append(
                {
                    "name": secret_name,
                    "secret": {
                        "secretName": secret_name
                    }
                }
            )

            volume_mounts_definitions.append(
                {
                    "name": secret_name,
                    "mountPath": app.settings.SECRET_PATH,
                    "subPath": "authorized_keys"
                }
            )
        acip_init_command = "exec /aicp-init/script/aicp-init"
        custom_command = f"{create_data.command if create_data.command else ''}|| true && tail -f /dev/null"
        template = {
            "apiVersion": cls.version,
            "kind": cls.kind,
            "metadata": {
                "name": notebook.uuid,
                "namespace": notebook.namespace,
                "labels": {
                    "app": notebook.uuid,
                    "aicp.group/workload": "container",
                    "user": notebook.user_id,
                    "aicp/api-version": app.settings.version
                },
                "annotations": {
                    "notebooks.kubeflow.org/password-version": "v3",
                    "notebooks.kubeflow.org/server-type": notebook.server_type,
                    "notebooks.kubeflow.org/creator": notebook.user_id,
                    "kubernetes.io/egress-bandwidth": app.settings.NOTEBOOK_EGRESS_BANDWIDTH,
                    "kubernetes.io/ingress-bandwidth": app.settings.NOTEBOOK_INGRESS_BANDWIDTH,
                    # 8888/8889 is jupyter/coder port, it should be proxy by istio
                    # other port should be workaround by istio
                    "traffic.sidecar.istio.io/includeInboundPorts": "8888,8889",
                    "traffic.sidecar.istio.io/includeOutboundIPRanges": "",
                    "custom_first": create_data.custom_first,
                    "custom_second": create_data.custom_second,
                }
            },
            "spec": {
                "template": {
                    "metadata": {
                        "labels": {
                            "app": notebook.uuid,
                            "user": notebook.user_id,
                            "train_uuid": str(notebook.uuid),
                            "aicp.group/workload": "container",
                        }
                    },
                    "spec": {
                        "securityContext": {
                            "runAsGroup": 0,
                            "runAsUser": 0,
                        },
                        "volumes": volumes_definitions,
                        "initContainers": [
                            {
                                "command": [
                                    "cp",
                                    "-r",
                                    "/notebook-init-container-files/.",
                                    "/aicp-init"
                                ],
                                "image": app.settings.NOTEBOOK_INIT_CONTAINER_IMAGE,
                                "imagePullPolicy": "IfNotPresent",
                                "name": "notebook-init-copy",
                                "resources": {
                                    "limits": {
                                        "cpu": "100m",
                                        "memory": "256Mi"
                                    },
                                    "requests": {
                                        "cpu": "100m",
                                        "memory": "256Mi"
                                    }
                                },
                                "volumeMounts": volume_mounts_definitions
                            }
                        ],
                        "schedulerName": notebook.replica_specs.resource.get_scheduler_name(),
                        "containers": [
                            {
                                "securityContext": {
                                    "capabilities": {
                                        "add": [
                                            "IPC_LOCK",
                                            "SYS_RESOURCE"
                                        ]
                                    },
                                },
                                "command": [
                                    "/bin/sh", "-c",
                                    ("mkdir -p /root/.ssh || true && cp /share/secret /root/.ssh/authorized_keys || true "
                                    "&& chmod 600 /root/.ssh/authorized_keys || true "
                                    f"&&{ custom_command if create_data.command else acip_init_command}")
                                ],
                                "workingDir": "/root",
                                "imagePullPolicy": "Always",
                                "name": notebook.uuid,
                                "image": notebook.image_url,
                                "env": envs,
                                "resources": notebook.replica_specs.resource.get_k8s_resources_definition(oversold=True),
                                "volumeMounts": deepcopy(volume_mounts_definitions),
                                # 开放 8888/8889/22 端口
                                "ports": [
                                    {
                                        "containerPort": 8888,
                                        "name": "jupyter-port",
                                        "protocol": "TCP"
                                    },
                                    {
                                        "containerPort": 8889,
                                        "name": "coder-port",
                                        "protocol": "TCP"
                                    },
                                    {
                                        "containerPort": 22,
                                        "name": "ssh",
                                        "protocol": "TCP"
                                    },
                                    {
                                        "containerPort": 9001,
                                        "name": "custom-port-1",
                                        "protocol": "TCP"
                                    },
                                    {
                                        "containerPort": 9002,
                                        "name": "custom-port-2",
                                        "protocol": "TCP"
                                    },
                                ]
                            }
                        ]
                    }
                }
            }
        }

        # set containers notebook-init-copy volume readonly
        for volume in template["spec"]["template"]["spec"]["containers"][0]["volumeMounts"]:
            if volume["name"] == "empty-dir-notebook-init-copy":
                volume["readOnly"] = True
            if volume["mountPath"] == app.settings.HOST_MODEL_MOUNT_PATH:
                volume["readOnly"] = True
            if volume["mountPath"] == app.settings.HOST_MODEL_NAME_MOUNT_PATH:
                volume["readOnly"] = True

        if ib_annotations := notebook.replica_specs.resource.get_ib_annotations(notebook.namespace):
            template["metadata"]["annotations"].update(ib_annotations)

        if node_selector := notebook.replica_specs.resource.get_k8s_node_selector_configuration():
            template["spec"]["template"]["spec"]["nodeSelector"] = node_selector

        if node_affinity := notebook.replica_specs.resource.get_k8s_affinity_configuration():
            template["spec"]["template"]["spec"]["affinity"] = node_affinity

        if toleration := notebook.replica_specs.resource.get_k8s_toleration_configuration():
            template["spec"]["template"]["spec"]["tolerations"] = toleration

        if secret_name := notebook.create_secret(notebook.namespace, create_data.custom_image_secret):
            template["spec"]["template"]["spec"]["imagePullSecrets"] = [{"name": secret_name}]

        return template

    def get_volume_index(self, volume_name: str):
        """
        获取 volume 在 volumes 中的索引
        :param volume_name:
        :return:
        """
        for i, volume in enumerate(self.spec.template.spec.volumes):
            if volume.name == volume_name:
                return i
        return -1

    def get_volume_index_by_prefix(self, volume_name_prefix: str):
        """
        获取 volume 在 volumes 中的索引
        :param volume_name:
        :return:
        """
        for i, volume in enumerate(self.spec.template.spec.volumes):
            if volume.name.startswith(volume_name_prefix):
                return i
        return -1

    def get_volume_mount_index_in_container(self, volume_name: str):
        """
        获取 volume 在 container volumeMounts 中的索引
        :param volume_name:
        :return:
        """
        for i, volume_mount in enumerate(self.spec.template.spec.containers[0].volumeMounts):
            if volume_mount.name == volume_name:
                return i
        return -1

    def get_volume_mount_index_in_container_by_prefix(self, volume_name_prefix: str):
        """
        获取 volume 在 container volumeMounts 中的索引
        :param volume_name:
        :return:
        """
        for i, volume_mount in enumerate(self.spec.template.spec.containers[0].volumeMounts):
            if volume_mount.name.startswith(volume_name_prefix):
                return i
        return -1

    def get_volume_mount_index_in_init_container_by_prefix(self, volume_name_prefix: str):
        """
        获取 volume 在 container volumeMounts 中的索引
        :param volume_name:
        :return:
        """
        for i, volume_mount in enumerate(self.spec.template.spec.initContainers[0].volumeMounts):
            if volume_mount.name.startswith(volume_name_prefix):
                return i
        return -1

    def get_volume_mount_index_in_init_container(self, volume_name: str):
        """
        获取 volume 在 init container volumeMounts 中的索引
        :param volume_name:
        :return:
        """
        for i, volume_mount in enumerate(self.spec.template.spec.initContainers[0].volumeMounts):
            if volume_mount.name == volume_name:
                return i
        return -1

    @property
    def pod_name(self):
        return f"{self.name}-0"

    @cached_property
    def env(self):
        ss = StatefulSet.get(name=self.name, namespace=self.namespace)
        return ss.spec.template.spec.containers[0].env.to_list()

    def get_node_name(self):
        pod = Pod.get(self.pod_name, namespace=self.namespace)
        return pod.spec.nodeName

    def shut_down(self):
        try:
            pod = Pod.get(self.pod_name, namespace=self.namespace)
            prefer_patches = [
                {
                    "op": "replace",
                    "path": "/spec/template/spec/containers/0/imagePullPolicy",
                    "value": "IfNotPresent"
                }
            ]
            if "affinity" in self.raw["spec"]["template"]["spec"]:
                node_affinity = {
                    "preferredDuringSchedulingIgnoredDuringExecution": [
                        {
                            "weight": 100,
                            "preference": {
                                "matchExpressions": [
                                    {
                                        "key": "kubernetes.io/hostname",
                                        "operator": "In",
                                        "values": [self.get_node_name()]
                                    }
                                ]
                            }
                        }
                    ]
                }
                prefer_patches.append(
                    {
                        "op": "replace",
                        "path": "/spec/template/spec/affinity/nodeAffinity",
                        "value": node_affinity
                    })
            if "kubeflow-resource-stopping" in self.annotations:
                prefer_patches.append(
                    {
                        "op": "remove",
                        "path": "/metadata/annotations/kubeflow-resource-stopping"
                    }
                )
            self.patch(prefer_patches, type="json")
        except NotFoundError as e:
            logger.error(f"Pod not found: {self.pod_name}, Skip change resource")
        self.annotate({"kubeflow-resource-stopped": "true"})

    def start(self):
        prefer_patches = []
        if "kubeflow-resource-stopped" in self.annotations:
            prefer_patches.append(
                {
                    "op": "remove",
                    "path": "/metadata/annotations/kubeflow-resource-stopped"
                }
            )
        if "kubeflow-resource-stopping" in self.annotations:
            prefer_patches.append(
                {
                    "op": "remove",
                    "path": "/metadata/annotations/kubeflow-resource-stopping"
                }
            )
        self.patch(
            prefer_patches,
            type="json"
        )

    def get_new_env(self, resource: ProductCenterResource):
        new_env = []
        for env in self.env:
            if env["name"] == "NVIDIA_VISIBLE_DEVICES":
                continue
            new_env.append(env)

        if not resource.is_gpu_product():
            new_env.append({
                "name": "NVIDIA_VISIBLE_DEVICES",
                "value": ""
            })
        elif resource.gpu_list and resource.gpu_list.value:
            new_env.append({
                "name": "NVIDIA_VISIBLE_DEVICES",
                "value": resource.gpu_list.value
            })
        return new_env

    def change_resource(self, resource: ProductCenterResource, aipods_type=None):
        patchs = [
            {
                "op": "replace",
                "path": "/spec/template/spec/containers/0/resources",
                "value": resource.get_k8s_resources_definition()
            }, {
                "op": "replace",
                "path": "/spec/template/spec/containers/0/env",
                "value": self.get_new_env(resource)
            }
        ]

        # bugfix QAI-1078 【IB 网卡分配】使用 IB 的容器不分配 secondary ip
        if not resource.get_ib_annotations(self.namespace) and "k8s.v1.cni.cncf.io/networks" in self.annotations:
            patchs.append({
                "op": "remove",
                "path": "/metadata/annotations/k8s.v1.cni.cncf.io~1networks"
            })

        if node_selector := resource.get_k8s_node_selector_configuration():
            patchs.append(
                {
                    "op": "replace",
                    "path": "/spec/template/spec/nodeSelector",
                    "value": node_selector
                }
            )
        if node_selector.get("aicp.group/aipods_type"):
            aipods_type = node_selector.get("aicp.group/aipods_type")
        if aipods_type in ["vGPU", "adapter", "vDCU"]:
            scheduler = "hami-scheduler"
        else:
            scheduler = "default-scheduler"
        patchs.append({
            "op": "replace",
            "path": "/spec/template/spec/schedulerName",
            "value": scheduler,
        })

        if affinity_configuration := resource.get_k8s_affinity_configuration():
            patchs.append(
                {
                    "op": "replace",
                    "path": "/spec/template/spec/affinity",
                    "value": affinity_configuration
                }
            )

        self.patch(
            patchs,
            type="json"
        )
        logger.info(f"change resource {self.name} done")

    def save_image(self):
        pass

    def change_image(self, image: str, secret_name: str = None, pre_image_info=None):
        patches = [
            {
                "op": "replace",
                "path": "/spec/template/spec/containers/0/image",
                "value": image
            }
        ]

        if pre_image_info:
            patches.append(
                {
                    "op": "replace",
                    "path": f"/metadata/annotations/{NOTEBOOK_PRE_IMAGE_INFO_KEY}",
                    "value": pre_image_info
                }
            )
        elif NOTEBOOK_PRE_IMAGE_INFO_KEY in self.annotations:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/metadata/annotations/{NOTEBOOK_PRE_IMAGE_INFO_KEY}"
                }
            )

        if secret_name:
            patches.append(
                {
                    "op": "replace",
                    "path": "/spec/template/spec/imagePullSecrets",
                    "value": [{"name": secret_name}]
                }
            )

        logger.info(f"change image {self.name} to {image} patches: {patches}")

        self.patch(
            patches,
            type="json"
        )

    def migration(self):
        pass

    def remove_mount(self, volume: VolumesManager):
        """
        移除挂载
        :param volume:
        :return:
        """
        patches = []
        volume_name = volume.get_volume_definition()["name"]
        volume_index = self.get_volume_index(volume_name)
        if volume_index >= 0:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/spec/template/spec/volumes/{volume_index}"
                }
            )

        volume_mounts_name = volume.get_volume_mounts_definition()["name"]
        container_volume_mounts_index = self.get_volume_mount_index_in_container(volume_mounts_name)
        if container_volume_mounts_index >= 0:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/spec/template/spec/containers/0/volumeMounts/{container_volume_mounts_index}"
                }
            )

        init_container_volume_mounts_index = self.get_volume_mount_index_in_init_container(volume_mounts_name)
        if init_container_volume_mounts_index >= 0:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/spec/template/spec/initContainers/0/volumeMounts/{init_container_volume_mounts_index}"
                }
            )
        if not patches:
            return

        self.patch(
            patches,
            type="json"
        )

    def remove_minio_mount(self):
        """

        """
        patches = []
        minio_name_prefix = "aicp-oss-"
        volume_index = self.get_volume_index_by_prefix(minio_name_prefix)
        if volume_index >= 0:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/spec/template/spec/volumes/{volume_index}"
                }
            )

        container_volume_mounts_index = self.get_volume_mount_index_in_container_by_prefix(minio_name_prefix)
        if container_volume_mounts_index >= 0:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/spec/template/spec/containers/0/volumeMounts/{container_volume_mounts_index}"
                }
            )

        container_volume_mounts_index = self.get_volume_mount_index_in_init_container_by_prefix(minio_name_prefix)
        if container_volume_mounts_index >= 0:
            patches.append(
                {
                    "op": "remove",
                    "path": f"/spec/template/spec/initContainers/0/volumeMounts/{container_volume_mounts_index}"
                }
            )

        if patches:
            self.patch(
                patches,
                type="json"
            )

    def add_mount(self, volume: VolumesManager):
        """
        添加挂载
        :param volume:
        :return:
        """
        patches = []
        volume_definition = volume.get_volume_definition()
        volume_index = self.get_volume_index(volume_definition["name"])
        if not volume_index >= 0:
            patches.append(
                {
                    "op": "add",
                    "path": "/spec/template/spec/volumes/-",
                    "value": volume_definition
                }
            )

        volume_mounts_definition = volume.get_volume_mounts_definition()
        container_volume_mounts_index = self.get_volume_mount_index_in_container(volume_mounts_definition["name"])
        if not container_volume_mounts_index >= 0:
            patches.append(
                {
                    "op": "add",
                    "path": "/spec/template/spec/containers/0/volumeMounts/-",
                    "value": volume_mounts_definition
                }
            )

        init_container_volume_mounts_index = self.get_volume_mount_index_in_init_container(
            volume_mounts_definition["name"])
        if not init_container_volume_mounts_index >= 0:
            patches.append(
                {
                    "op": "add",
                    "path": "/spec/template/spec/initContainers/0/volumeMounts/-",
                    "value": volume_mounts_definition
                }
            )
        if not patches:
            return

        self.patch(
            patches,
            type="json"
        )

    def change_resource_group(self, rg_id: str, rg_node_id: str):
        """
        修改资源组
        :param rg_id:
        :param rg_node_id:
        """
        tolerations_index = [x["key"] for x in self.raw["spec"]["template"]["spec"]["tolerations"]].index(
            "aicp.group/resource_group")
        patches = [
            {
                "op": "replace",
                "path": "/spec/template/spec/nodeSelector/aicp.group~1resource_group",
                "value": rg_id
            },
            # replace tolerations value=rg-id
            {
                "op": "replace",
                "path": f"/spec/template/spec/tolerations/{tolerations_index}/value",
                "value": rg_id
            }
        ]
        if rg_node_id:
            patches.append(
                {
                    "op": "replace",
                    "path": "/spec/template/spec/nodeSelector/aicp.group~1resource_group_node",
                    "value": rg_node_id
                }
            )
        else:
            if "aicp.group/resource_group_node" in self.raw["spec"]["template"]["spec"]["nodeSelector"]:
                patches.append(
                    {
                        "op": "remove",
                        "path": "/spec/template/spec/nodeSelector/aicp.group~1resource_group_node"
                    }
                )
        self.patch(
            patches,
            type="json"
        )

    def _get_zfs_change_patch(self, volumes: List[NotebookVolumeSpec]) -> Union[None, List[Dict]]:
        """
        获取zfs pv变化的patch
        if new volume is None, do nothing. Whether it exists or not on notebook.
        if the volume exists, change the volume size.
        if not exists, add the volume.
        """

        zfs_volume = [x for x in volumes if x.volume_type == "LOCAL"]
        if not zfs_volume:
            return

        volume_manager = VolumesManager(None, self.namespace, zfs_volume[0])

        for exist_volume in self.spec.template.spec.volumes:
            if exist_volume.name == volume_manager.get_volume_definition()["name"]:
                volume_manager.change()
                return

        patches = [
            {
                "op": "add",
                "path": "/spec/template/spec/volumes/-",
                "value": volume_manager.get_volume_definition()
            },
            {
                "op": "add",
                "path": "/spec/template/spec/containers/0/volumeMounts/-",
                "value": volume_manager.get_volume_mounts_definition()
            }
        ]

        return patches

    def change_volume(self, user, volumes: List[NotebookVolumeSpec]):
        """
        检查volume是否有变化,
        """
        patches = []
        volumes_to_remove = []
        added_volumes = []

        # 检查zfs local pv
        if p := self._get_zfs_change_patch(volumes):
            patches.extend(p)

        current_volumes, current_volume_mounts = self._get_current_volume_info()
        new_volume_map = self._build_new_volume_map(user, volumes)

        volumes_to_remove = self._find_volumes_to_remove(current_volumes, new_volume_map)
        patches.extend(self._create_remove_patches(volumes_to_remove))

        added_volumes, add_patches = self._create_add_patches(new_volume_map, current_volumes, current_volume_mounts)
        patches.extend(add_patches)

        if patches:
            logger.info(f"changing volumes:{patches}")
            self.patch(patches, type="json")

        return volumes_to_remove, added_volumes

    def _get_current_volume_info(self):
        """获取当前存储配置"""
        current_volumes = {v.name: v for v in self.spec.template.spec.volumes}
        current_volume_mounts = {vm.name: vm for vm in self.spec.template.spec.containers[0].volumeMounts}
        logger.debug(f"current_volumes={current_volumes}, current_volume_mounts={current_volume_mounts}")
        return current_volumes, current_volume_mounts

    def _build_new_volume_map(self, user, volumes):
        """创建新存储的映射"""
        new_volume_map = {}
        for vol_spec in volumes:
            volume_manager = VolumesManager(user, self.namespace, vol_spec)
            volume_name = volume_manager.get_volume_definition()["name"]
            new_volume_map[volume_name] = (vol_spec, volume_manager)
        logger.debug(f"new_volume_map={new_volume_map}")
        return new_volume_map

    def _find_volumes_to_remove(self, current_volumes, new_volume_map):
        """找出需要移除的存储"""
        volumes_to_remove = []
        for volume_name, volume_obj in current_volumes.items():
            if self._should_remove_volume(volume_name, new_volume_map):
                volumes_to_remove.append(volume_name)
        logger.debug(f"volumes_to_remove={volumes_to_remove}")
        return volumes_to_remove

    def _should_remove_volume(self, volume_name, new_volume_map):
        """判断是否应该移除volume"""
        if volume_name.startswith(("shared-memory-", "empty-dir-", "ssh-key-secrets-", "local-path-sharemaasfile")):
            return False

        is_user_volume = any(
            volume_name.startswith(prefix) for prefix in [
                "qingcloud-gpfs-", "local-path-"
            ]
        )
        is_manual_pvc = not MPF.match(volume_name)
        return (is_user_volume or is_manual_pvc) and volume_name not in new_volume_map

    def _create_remove_patches(self, volumes_to_remove):
        """创建移除存储的patches"""
        patches = []
        for volume_name in volumes_to_remove:
            patches.extend(self._create_single_remove_patch(volume_name))
        return patches

    def _create_single_remove_patch(self, volume_name):
        """为单个volume创建移除patch"""
        patches = []

        volume_index = self.get_volume_index(volume_name)
        if volume_index >= 0:
            patches.append({
                "op": "remove",
                "path": f"/spec/template/spec/volumes/{volume_index}"
            })

        container_mount_index = self.get_volume_mount_index_in_container(volume_name)
        if container_mount_index >= 0:
            patches.append({
                "op": "remove",
                "path": f"/spec/template/spec/containers/0/volumeMounts/{container_mount_index}"
            })

        init_mount_index = self.get_volume_mount_index_in_init_container(volume_name)
        if init_mount_index >= 0:
            patches.append({
                "op": "remove",
                "path": f"/spec/template/spec/initContainers/0/volumeMounts/{init_mount_index}"
            })

        return patches

    def _create_add_patches(self, new_volume_map, current_volumes, current_volume_mounts):
        """创建添加存储的patches"""
        patches = []
        added_volumes = []

        for volume_name, (vol_spec, volume_manager) in new_volume_map.items():
            if volume_name not in current_volumes:
                logger.debug(f"Adding volume '{volume_name}' volume define:{volume_manager.get_volume_definition()}.")
                volume_manager.create_storage()
                vol_spec.pvc_name = volume_manager.volume.pvc_name
                if vol_spec.volume_type == "GPFS":
                    vol_spec.quota = int(volume_manager.volume.quota[:-2])
                added_volumes.append(vol_spec)
                patches.extend(self._create_single_add_patch(volume_manager, current_volume_mounts))

        return added_volumes, patches

    def _create_single_add_patch(self, volume_manager, current_volume_mounts):
        """为单个volume创建添加patch"""
        patches = []

        patches.append({
            "op": "add",
            "path": "/spec/template/spec/volumes/-",
            "value": volume_manager.get_volume_definition()
        })

        volume_mount_def = volume_manager.get_volume_mounts_definition()
        if volume_mount_def["name"] not in current_volume_mounts:
            patches.append({
                "op": "add",
                "path": "/spec/template/spec/containers/0/volumeMounts/-",
                "value": volume_mount_def
            })

            patches.append({
                "op": "add",
                "path": "/spec/template/spec/initContainers/0/volumeMounts/-",
                "value": volume_mount_def
            })

        return patches

    def suspending(self):
        self.annotate({"kubeflow-resource-stopping": datetime.datetime.now().strftime("%Y-%m-%dT%H:%M:%SZ")})


class NoteBookCrOperator():
    notebook_cr: NoteBook

    def __init__(self, notebook_id: str, namespace: str, user_id: str):
        self.notebook_id = notebook_id
        self.namespace = namespace
        self.user_id = user_id
        self.__init_notebook_cr()

    @property
    def node_port_service_name(self):
        """

        :return:
        """
        return f"{self.notebook_id}-node-port"

    @property
    def network_policy_name(self):
        """

        :return:
        """
        return f"{self.notebook_id}-network-policy"

    def remove_minio_volume(self):
        self.notebook_cr.remove_minio_mount()

    def get_node_port_service(self) -> Service:
        """

        :return:
        """
        return Service.get(self.node_port_service_name, namespace=self.namespace)

    def get_network_policy(self):
        """

        :return:
        """
        return NetworkPolicy.get(self.network_policy_name, namespace=self.namespace)

    def _patch_add_node_port(self, target_port: int, protocol: str):
        if not 0 < target_port < 65536:
            raise InvalidTargetPortException(target_port)

        service = self.get_node_port_service()
        ports = [port.port for port in service.spec.ports]
        if 0 < app.settings.NODE_PORT_PER_NOTEBOOK <= len(ports):
            raise TooManyPortsException()

        if target_port in ports:
            raise DuplicatePortException(target_port)

        service.patch(
            [
                {
                    "op": "add",
                    "path": "/spec/ports/-",
                    "value": {
                        "name": f"port-{target_port}",
                        "port": target_port,
                        "targetPort": target_port,
                        "appProtocol": protocol,
                    }
                }
            ],
            type="json"
        )

    def _patch_remove_node_port(self, target_port: int):

        try:
            service = self.get_node_port_service()
        except NotFoundError as e:
            logger.warning(f"node port service not found: {self.node_port_service_name}")
            return

        ports = [port.port for port in service.spec.ports]
        if target_port not in ports:
            logger.warning(f"node port {target_port} not found in service {self.node_port_service_name}")
            return

        if len(ports) == 1:
            logger.info(f"delete node port service {self.node_port_service_name}, because no port left.")
            service.delete()
        else:
            service.patch(
                [
                    {
                        "op": "remove",
                        "path": f"/spec/ports/{[port.port for port in service.spec.ports].index(target_port)}"
                    }
                ],
                type="json"
            )

    def _patch_add_network_policy(self, target_port: int):
        if not app.settings.USE_NETWORK_POLICY:
            logger.info("network policy is disabled")
            return

        if not 0 < target_port < 65536:
            raise InvalidTargetPortException(target_port)

        network_policy = self.get_network_policy()
        network_policy.patch(
            [
                {
                    "op": "add",
                    "path": "/spec/ingress/-",
                    "value": {
                        "ports": [
                            {
                                "port": target_port,
                                "protocol": "TCP"
                            }
                        ]
                    }
                }
            ],
            type="json"
        )

    def _patch_remove_network_policy(self, target_port: int):
        if not app.settings.USE_NETWORK_POLICY:
            logger.info("network policy is disabled")
            return
        try:
            network_policy = self.get_network_policy()
        except NotFoundError as e:
            logger.warning(f"network policy not found: {self.network_policy_name}")
            return

        ports = [port.port for port in network_policy.spec.ingress[0].ports]
        if target_port not in ports:
            logger.warning(f"node port {target_port} not found in network policy {self.network_policy_name}")
            return

        if len(ports) == 1:
            logger.info(f"delete network policy {self.network_policy_name}, because no port left.")
            network_policy.delete()
        else:
            network_policy.patch(
                [
                    {
                        "op": "remove",
                        "path": f"/spec/ingress/0/ports/{ports.index(target_port)}"
                    }
                ],
                type="json"
            )

    def create_node_port_service(self, target_port: int, protocol: str):
        """

        :param protocol:
        :param target_port:
        :return:
        """
        service = Service(
            {
                "apiVersion": "v1",
                "kind": "Service",
                "metadata": {
                    "name": self.node_port_service_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": self.notebook_id,
                    }
                },
                "spec": {
                    "type": "NodePort",
                    "selector": {
                        "app": self.notebook_id
                    },
                    "ports": [
                        {
                            "name": f"port-{target_port}",
                            "port": target_port,
                            "targetPort": target_port,
                            "appProtocol": protocol,
                        }
                    ]
                }
            }
        )
        service.create()
        service.set_owner(self.notebook_cr)
        return service

    def create_network_policy(self, target_port: int):
        """

        :param target_port:
        :return:
        """
        network_policy = NetworkPolicy(
            {
                "apiVersion": "networking.k8s.io/v1",
                "kind": "NetworkPolicy",
                "metadata": {
                    "name": self.network_policy_name,
                    "namespace": self.namespace,
                    "labels": {
                        "app": self.notebook_id,
                    }
                },
                "spec": {
                    "podSelector": {
                        "matchLabels": {
                            "app": self.notebook_id
                        }
                    },
                    "policyTypes": [
                        "Ingress"
                    ],
                    "ingress": [
                        {
                            "ports": [
                                {
                                    "port": target_port,
                                    "protocol": "TCP"
                                }
                            ]
                        }
                    ]
                }
            }
        )
        network_policy.create()
        network_policy.set_owner(self.notebook_cr)
        return network_policy

    @property
    def ssh_service_name(self):
        """

        :return:
        """
        return f"{self.notebook_id}-ssh"

    def get_ssh_service(self):
        """
        get ssh service, 22 / 9001 / 9002
        :return:
        """
        return Service.get(self.ssh_service_name, namespace=self.namespace)

    @property
    def service_name(self):
        """

        :return:
        """
        return f"{self.notebook_id}"

    def get_service(self):
        """
        get notebook service, 8888 / 8889
        :return:
        """
        return Service.get(self.service_name, namespace=self.namespace)

    def __init_notebook_cr(self):
        self.notebook_cr = NoteBook.get(self.notebook_id, namespace=self.namespace)

    def refresh(self):
        self.notebook_cr.refresh()

    def shut_down(self):
        """
        关机
        :return:
        """
        logger.info(f"shut down notebook {self.notebook_id}")
        self.notebook_cr.shut_down()

    def start(self):
        logger.info(f"start notebook {self.notebook_id}")
        self.notebook_cr.start()

    def suspending(self):
        logger.info(f"suspending notebook {self.notebook_id}")
        self.notebook_cr.suspending()

    def save_image(self, to=None) -> ImageBuilder:
        user = QingcloudUser(**get_qingcloud_user_info_by_user_id(self.user_id), user_from=UserFrom.CONSOLE)
        repo_namespace = QingcloudDockerApiClient(user).get_repo_name()
        if not repo_namespace:
            raise NoHarborException(self.user_id)
        ibo = ImageBuilderOperator(self.notebook_id, self.namespace, self.user_id, repo_namespace)
        if ibo.has_creating():
            raise ImageBuilderCreatingException(self.notebook_id)
        ib = ibo.save(to)
        ib.set_owner(self.notebook_cr)
        return ib

    def check_image_creating(self, to=None) -> None:
        user = QingcloudUser(**get_qingcloud_user_info_by_user_id(self.user_id), user_from=UserFrom.CONSOLE)
        repo_namespace = QingcloudDockerApiClient(user).get_repo_name()
        if not repo_namespace:
            raise NoHarborException(self.user_id)
        ibo = ImageBuilderOperator(self.notebook_id, self.namespace, self.user_id, repo_namespace)
        if ibo.has_creating():
            raise ImageBuilderCreatingException(self.notebook_id)
        return

    def precheck_for_save_image(self, timeout=30):
        user = QingcloudUser(**get_qingcloud_user_info_by_user_id(self.user_id), user_from=UserFrom.CONSOLE)
        repo_namespace = QingcloudDockerApiClient(user).get_repo_name()
        if not repo_namespace:
            raise NoHarborException(self.user_id)
        ibo = ImageBuilderOperator(self.notebook_id, self.namespace, self.user_id, repo_namespace)
        if ibo.has_creating():
            raise ImageBuilderCreatingException(self.notebook_id)
        ib = ibo.check()
        ib.set_owner(self.notebook_cr)
        ib.wait_for_check_success(timeout)

    def precheck_diskspace_for_save_image(self, timeout=30):
        user = QingcloudUser(**get_qingcloud_user_info_by_user_id(self.user_id), user_from=UserFrom.CONSOLE)
        repo_namespace = QingcloudDockerApiClient(user).get_repo_name()
        if not repo_namespace:
            raise NoHarborException(self.user_id)
        ibo = ImageBuilderOperator(self.notebook_id, self.namespace, self.user_id, repo_namespace)
        if ibo.has_creating():
            raise ImageBuilderCreatingException(self.notebook_id)
        ib = ibo.check_disk_space()
        ib.set_owner(self.notebook_cr)
        ib.wait_for_check_success(timeout)

    def dumps_image(self):
        ibo = ImageBuilderOperator(self.notebook_id, self.namespace, self.user_id, "only_saved").dumps()
        ibo.set_owner(self.notebook_cr)
        return ibo

    def create_secret(self, image_type: str, image_url: str, docker_secret: CustomImageSecret = None) -> str:
        """
        创建镜像认证secret
        :param image_type:
        :param image_url:
        :param docker_secret:
        :return:
        """
        if image_type in ["official", "user","application","hpc","share"]:
            docker_secret = DockerCfgSecret(self.namespace, app.settings.DOCKER_REGISTRY,
                                            app.settings.DOCKER_ADMIN_USER, app.settings.DOCKER_ADMIN_PASSWORD)
            docker_secret.create()
            docker_secret_name = docker_secret.name
        elif docker_secret and docker_secret.has_auth:
            docker_secret = DockerCfgSecret(self.namespace, image_url.split("/")[0],
                                            docker_secret.username, docker_secret.password)
            docker_secret.create()
            docker_secret_name = docker_secret.name
        else:
            docker_secret_name = None
        return docker_secret_name

    def change_image(self, image_type: str, image: str, secret: CustomImageSecret = None, pre_image_info=None):
        logger.info(f"change notebook image {self.notebook_id} to {image}")
        secret_name = self.create_secret(image_type, image, secret)
        self.notebook_cr.change_image(image, secret_name, pre_image_info)

    def change_resource(self, resource: ProductCenterResource, aipods_type=None):
        logger.info(f"change notebook resource {self.notebook_id} to {resource}")
        self.notebook_cr.change_resource(resource, aipods_type)

    def add_node_port(self, target_port: int, protocol: str):
        """
        添加 node port  并且开放网络策略
        :param protocol:
        :param target_port:
        """
        logger.info(f"add node port {target_port} for notebook {self.notebook_id}")
        if not 0 < target_port < 65536:
            raise InvalidTargetPortException(target_port)

        try:
            self._patch_add_node_port(target_port, protocol)
        except NotFoundError as e:
            logger.info(f"create node port service for notebook {self.notebook_id}")
            self.create_node_port_service(target_port, protocol)

        try:
            self._patch_add_network_policy(target_port)
        except NotFoundError as e:
            logger.info(f"create network policy for notebook {self.notebook_id}")
            self.create_network_policy(target_port)

    def remove_node_port(self, target_port: int):
        """
        移除 node port 以及网络策略
        :param target_port:
        """
        logger.info(f"remove node port {target_port} for notebook {self.notebook_id}")
        self._patch_remove_node_port(target_port)
        self._patch_remove_network_policy(target_port)

    def remove_mount(self, volume: VolumesManager):
        """
        移除挂载
        :param volume:
        :return:
        """
        self.notebook_cr.remove_mount(volume)

    def add_mount(self, volume: VolumesManager):
        """
        添加挂载
        :param volume:
        :return:
        """
        self.notebook_cr.add_mount(volume)

    def change_resource_group(self, rg_id: str, rg_node_id: str):
        """
        修改资源组
        :param rg_node_id:
        :param rg_id:
        """
        logger.info(f"change notebook resource group {self.notebook_id} to {rg_id}")
        self.notebook_cr.change_resource_group(rg_id, rg_node_id)

    def change_volume(self, user, volumes: List[NotebookVolumeSpec]):
        """
        检查volume是否有变化
        """

        # 调用notebook_cr的change_volume方法，获取需要删除的volume名称和新增的volume信息
        removed_volume_names, added_volumes = self.notebook_cr.change_volume(user, volumes)

        # 处理数据库记录的增删
        if removed_volume_names or added_volumes:
            session = get_db_session_local()
            notebook = session.get(NotebookModel, self.notebook_id)
            if notebook:
                # 删除对应的数据库记录
                for volume_spec in notebook.volume_specs[:]:  # 使用切片复制避免修改迭代中的列表
                    volume_manager = VolumesManager(user, self.namespace, volume_spec)
                    volume_name = volume_manager.get_volume_definition()["name"]
                    if volume_name in removed_volume_names:
                        session.delete(volume_spec)

                # 新增数据库记录
                for volume_spec in added_volumes:
                    session.add(volume_spec)

                session.commit()
                session.close()


    @property
    async def get_gpu_thread(self):
        pod = await AsyncPod.get(self.notebook_id + "-0", namespace=self.namespace)
        command = await AsyncPod.exec(["nvidia-smi pmon -c 1"])
        result = command.stdout.decode()


@dataclasses.dataclass
class StatusPhase:
    status: Optional[str]
    reason: Optional[str]

    def __bool__(self):
        return self.status is not None


blank_status = StatusPhase(None, None)


def is_pending_status(notebook: Dict) -> StatusPhase:
    """
    获取是否处于 Pending 状态

    没有containerState且condition且时间小于10s, 代表刚创建Notebook, 此时状态为Pending
    :param notebook:
    :return:
    """
    delta = (datetime.datetime.utcnow().replace(microsecond=0) - datetime.datetime.strptime(
        notebook.get("metadata", {}).get("creationTimestamp"), "%Y-%m-%dT%H:%M:%SZ"))

    if not notebook.get("status", {}).get("containerState") and not notebook.get("status", {}).get("conditions") \
            and delta.total_seconds() <= 10:
        return StatusPhase(NotebookStatus.Pending, NotebookReason.waiting_for_scheduling)

    return blank_status


def is_suspend_status(notebook: Dict) -> StatusPhase:
    """
    获取是否处于 Suspended 状态

    存在 STOP_ANNOTATION 且 readyReplicas 为 0 代表 Notebook 已停止, 如果readyReplicas不为0, 代表正在停止
    :param notebook:
    :return:
    """
    if STOP_ANNOTATION in notebook.get("metadata", {}).get("annotations", {}):
        # If the Notebook is stopped, the status will be stopped
        if notebook.get("status", {}).get("readyReplicas", 0) == 0:
            return StatusPhase(NotebookStatus.Suspended, NotebookReason.suspend)
        # If the Notebook is being stopped, the status will be waiting
        else:
            return StatusPhase(NotebookStatus.Suspending, NotebookReason.suspending)

    return blank_status


def is_suspending_status(notebook: Dict) -> StatusPhase:
    """
    获取是否处于 Suspended 状态

    存在 STOP_ANNOTATION 且 readyReplicas 为 0 代表 Notebook 已停止, 如果readyReplicas不为0, 代表正在停止
    :param notebook:
    :return:
    """
    if STOPPING_ANNOTATION in notebook.get("metadata", {}).get("annotations", {}):
        # If the Notebook is stopped, the status will be stopped
        return StatusPhase(NotebookStatus.Suspending, NotebookReason.suspending)

    return blank_status


def is_terminating_status(notebook: Dict) -> StatusPhase:
    """
    获取是否处于 Terminating 状态
    :param notebook:
    :return:
    """
    if "deletionTimestamp" in notebook.get("metadata", {}):
        return StatusPhase(NotebookStatus.Terminating, NotebookReason.terminating)
    return blank_status


def is_running_status(notebook: Dict) -> StatusPhase:
    """
    获取是否处于 Running 状态
    :param notebook:
    :return:
    """
    if notebook.get("status", {}).get("readyReplicas", 0) == 1:
        return StatusPhase(NotebookStatus.Running, NotebookReason.running)
    return blank_status


async def get_status_from_container_state(notebook: Dict) -> StatusPhase:
    """
    从 containerState 获取状态
    :param notebook:
    :return:
    """
    container_state = notebook.get("status", {}).get("containerState", {})

    if "waiting" in container_state:
        # If the Notebook is initializing, the status will be waiting
        if container_state["waiting"]["reason"] == 'ImagePullBackOff':
            return StatusPhase(NotebookStatus.CreateFailed, NotebookReason.pulling_image_failed)
        if container_state["waiting"]["reason"] == 'PodInitializing':
            # add latest event
            latest_event = await get_latest_event(notebook)
            if not latest_event:
                return StatusPhase(NotebookStatus.Creating, NotebookReason.pulling_image)
            if latest_event["reason"] == "Pulling":
                return StatusPhase(NotebookStatus.Creating, NotebookReason.pulling_image)
            elif latest_event["reason"] == "FailedMount":
                return StatusPhase(NotebookStatus.CreateFailed, NotebookReason.mounting_failed)
            return StatusPhase(NotebookStatus.Creating, NotebookReason.pulling_image)
        else:
            return StatusPhase(
                NotebookStatus.Warning,
                f'{container_state["waiting"]["reason"]}: {container_state["waiting"]["message"]}')

    return blank_status


def get_status_from_conditions(notebook: Dict) -> StatusPhase:
    for condition in notebook.get("status", {}).get("conditions", []):
        # The status will be warning with a "reason: message" showing on hover
        if "reason" in condition:
            return StatusPhase(NotebookStatus.Warning, f'{condition["reason"]}: {condition["message"]}')

    return blank_status


async def get_notebook_kr8s_status(notebook) -> List[Event]:
    """
    获取 notebook 状态
    :param notebook:
    :return:
    """
    events: List[Event] = await Event.list(  # noqa
        namespace=notebook["metadata"]["namespace"],
        field_selector=f"involvedObject.kind=Notebook,involvedObject.name={notebook['metadata']['name']}"
    )

    nb_creation_time = datetime.datetime.strptime(notebook["metadata"]["creationTimestamp"], "%Y-%m-%dT%H:%M:%SZ")
    nb_events = sorted(
        filter(
            lambda _: kr8s_event_timestamp(_) >= nb_creation_time, events,
        ),
        key=kr8s_event_timestamp, reverse=True
    )

    return nb_events


async def get_latest_event(notebook) -> Event:
    events = await get_notebook_kr8s_status(notebook)
    return events[0] if events else None


async def get_status_from_kr8s_events(notebook):
    """
    Returns status and reason from the latest event that surfaces the cause
    of why the resource could not be created. For a Notebook, it can be due to:

          EVENT_TYPE      EVENT_REASON      DESCRIPTION
          Warning         FailedCreate      pods "x" is forbidden: error
            looking up service account ... (originated in statefulset)
          Warning         FailedScheduling  0/1 nodes are available: 1
            Insufficient cpu (originated in pod)

    """
    nb_events = await get_notebook_kr8s_status(notebook)

    for e in nb_events:
        if e["type"] == EVENT_TYPE_WARNING:
            return StatusPhase(NotebookStatus.Warning, f'{e["reason"]}: {e["message"]}')

    return blank_status


def is_unschedulable(reason: str):
    """
    判断是否为Unschedulable状态
    :param reason:
    :return:
    """
    if "Unschedulable" in reason and "PersistentVolumeClaims" not in reason:
        return True
    return False


def convert_status_phase_by_reason(status: StatusPhase, notebook_model: NotebookModel):
    if notebook_model.reason in [NotebookReason.suspend_by_billing]:
        status.reason = notebook_model.reason
    elif status.status in [NotebookStatus.Creating, NotebookStatus.Pending] \
            and notebook_model.status == NotebookStatus.Restarting:
        status.status = NotebookStatus.Restarting
    # 原始状态为Suspending且构建镜像的情况下, 临时的Running状态不变
    elif notebook_model.status == NotebookStatus.Suspending \
            and notebook_model.reason in [NotebookReason.image_building, NotebookReason.image_build_success] \
            and status.status in [NotebookStatus.Running, NotebookStatus.Suspending]:
        status.status = notebook_model.status
        status.reason = notebook_model.reason
    elif status.status == NotebookStatus.Warning:
        if "Unhealthy" in status.reason:
            status.status = notebook_model.status
            status.reason = notebook_model.reason
    if "Unschedulable" in status.reason:
        status.reason = NotebookReason.unschedulable


async def async_get_notebook_status(notebook_cr: Dict, notebook_model: NotebookModel) -> StatusPhase:
    """
    获取 notebook 状态
    :param notebook_cr:
    :param notebook_model:
    :return:
    """
    start_time = time.time()
    # In case the Notebook has no status
    if status := is_pending_status(notebook_cr):
        # pending
        pass
    elif status := is_suspend_status(notebook_cr):
        # suspended, suspending
        pass
    elif status := is_suspending_status(notebook_cr):
        # suspended, suspending
        pass
    elif status := is_terminating_status(notebook_cr):
        # terminating
        pass
    elif status := is_running_status(notebook_cr):
        # running
        pass
    elif status := await get_status_from_container_state(notebook_cr):
        # creating
        pass
    elif status := get_status_from_conditions(notebook_cr):
        # warning
        pass
    elif status := await get_status_from_kr8s_events(notebook_cr):
        # warning
        pass
    else:
        status = blank_status

    if not status:
        return StatusPhase(notebook_model.status, notebook_model.reason)

    # 添加status 变换, 比如资源不足
    convert_status_phase_by_reason(status, notebook_model)
    logger.info(f"get notebook status {notebook_cr['metadata']['name']} cost {time.time() - start_time} s")
    return status


get_notebook_status = run_sync(async_get_notebook_status)
