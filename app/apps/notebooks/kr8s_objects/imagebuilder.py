import os.path
import time
from datetime import datetime, timedelta
from typing import List

from kr8s import NotFoundError
from kr8s._async_utils import sync
from kr8s._objects import APIObject
from kr8s.objects import Pod

import app
from app import logger
from app.apps.notebooks.exceptions import <PERSON><PERSON><PERSON>Ex<PERSON>, HarborQuotaExcceedException, ImageBuildFailedException, \
    ImageBuilderTimeoutException, ImageBuilderCheckDiskSpaceExcceedException


class ImageBuilderStatus:
    Creating = "Creating"
    Failed = "Failed"
    Succeeded = "Succeeded"
    NoSpace = "NoSpace"
    NotConnHarbor = "NotConnHarbor"
    NoDiskSpace = "NoDiskSpace"


@sync
class ImageBuilder(APIObject):
    version = "imagebuilder.ai.qingcloud.com/v1"
    endpoint = "imagebuilders"
    kind = "ImageBuilder"
    plural = "imagebuilders"
    singular = "imagebuilder"
    namespaced = True

    pod_namespace = "aicp-system"

    @property
    def create_at(self):
        return datetime.strptime(self.metadata.creationTimestamp, "%Y-%m-%dT%H:%M:%SZ")

    @property
    def is_no_space(self):
        return self.raw.get("status", {}).get("state", "") == "NoSpace"

    @property
    def is_no_disk_space(self):
        return self.raw.get("status", {}).get("state", "") == ImageBuilderStatus.NoDiskSpace

    @property
    def is_not_conn_harbor(self):
        return self.raw.get("status", {}).get("state", "") == ImageBuilderStatus.NotConnHarbor

    @property
    def is_runnnig(self):
        return self.raw.get("status", {}).get("state", "") == ImageBuilderStatus.Creating

    @property
    def is_failed(self):
        return self.raw.get("status", {}).get("state", "") == ImageBuilderStatus.Failed

    @property
    def is_success(self):
        return self.raw.get("status", {}).get("state", "") == ImageBuilderStatus.Succeeded

    @property
    def is_committed(self) -> bool:
        """
        check image builder is committed
        :return:
        """
        try:
            pods = Pod.list(namespace=self.pod_namespace, label_selector=f"job-name={self.name}")
            if not pods:
                logger.warning(f"ImageBuilder Pods not found: {self}")
                return False
            sorted_pods = sorted(pods, key=lambda x: x.metadata.creationTimestamp, reverse=True)  # noqa
            pod = sorted_pods[0]
            # pod is deleted after image builder is done,
            # so there maybe raise httpx response not read() error
            for line in pod.logs(tail_lines=10):
                if "containerd commit success" in line or '"status":"Pushing"' in line:
                    logger.info(f"image builder is committed: {self.name}")
                    return True
        except Exception as e:
            logger.warning(f"check image builder is committed failed: {self.name}, {e}")
            return False
        return False

    def wait_for_committed(self, timeout=app.settings.IMAGE_SAVE_TIMEOUT):
        """
        wait for image builder commit success
        :param timeout:
        :return:
        """
        while True:
            self.refresh()
            if self.is_no_space:
                raise HarborQuotaExcceedException(self.namespace)
            if self.is_no_disk_space:
                raise ImageBuilderCheckDiskSpaceExcceedException(self.namespace)
            if self.is_not_conn_harbor:
                raise HarborAuthException(self.namespace)

            if self.is_runnnig or self.is_success:
                if self.is_committed:
                    logger.info(f"image builder is committed: {self.name}")
                    return
            elif self.is_failed:
                raise ImageBuildFailedException("image builder is failed")

            suspend_time = datetime.utcnow().replace(microsecond=0) - self.create_at
            if suspend_time > timedelta(minutes=timeout):
                logger.error(f"image builder is timeout, skip stop notebook: {self.name}")
                raise ImageBuilderTimeoutException(self.name)

            status = self.raw.get("status", {}).get("state", "")
            logger.info(f"image builder is running({status}), wait: {self.name}, suspend_time: {suspend_time}")
            time.sleep(10)

    def wait_for_success(self, timeout=app.settings.IMAGE_SAVE_TIMEOUT):

        while True:
            self.refresh()
            if self.is_success:
                logger.info(f"image builder is success: {self.name}")
                return

            if self.is_failed:
                raise ImageBuildFailedException("image builder is failed")

            suspend_time = datetime.utcnow().replace(microsecond=0) - self.create_at
            if suspend_time > timedelta(minutes=timeout):
                logger.error(f"image builder is timeout, skip stop notebook: {self.name}")
                raise ImageBuilderTimeoutException("image builder is timeout")

            status = self.raw.get("status", {}).get("state", "")
            logger.info(f"image builder is running({status}), wait: {self.name}, suspend_time: {suspend_time}")
            time.sleep(10)

    def wait_for_check_success(self, timeout):
        while True:
            self.refresh()
            if self.is_no_space:
                raise HarborQuotaExcceedException(self.namespace)
            if self.is_no_disk_space:
                raise ImageBuilderCheckDiskSpaceExcceedException(self.namespace)
            if self.is_not_conn_harbor:
                raise HarborAuthException(self.namespace)
            if self.is_failed:
                raise ImageBuildFailedException("image builder is failed")
            if self.is_success:
                logger.info(f"image builder is success: {self.name}")
                return

            duration_time = datetime.utcnow().replace(microsecond=0) - self.create_at
            if duration_time > timedelta(seconds=timeout):
                logger.error(f"image builder is timeout, skip stop notebook: {self.name}")
                raise ImageBuildFailedException("image builder is timeout")

            logger.info(f"image builder check is running, wait: {self.name}, duration_time: {duration_time}")
            time.sleep(1)


class ImageBuilderOperator:
    image_builder: ImageBuilder
    image_builders: List[APIObject]

    def __init__(self, notebook_uuid: str, namespace: str, user_id: str, repo_namespace: str):
        self.namespace = namespace
        self.notebook_uuid = notebook_uuid
        self.tag = datetime.now().strftime('%Y%m%d%H%M%S')
        self.name = f"{notebook_uuid}-{self.tag}"
        self.user_id = user_id
        self.repo_namespace = repo_namespace
        self.image_url = "/".join(
            [app.settings.DOCKER_REGISTRY, self.repo_namespace, f"{self.notebook_uuid}:{self.tag}"])

    def has_creating(self) -> bool:
        """
        check if image builder is creating
        :return:
        """
        image_builders = ImageBuilder.list(namespace=self.namespace, label_selector=f"notebook={self.notebook_uuid}")
        return bool([x for x in image_builders if x.raw.get("status", {}).get("state", "") == "Creating"]) # noqa

    def refresh(self):
        """
        refresh image builder
        """
        self.image_builder.refresh()

    def is_runnnig(self) -> bool:
        self.refresh()
        return self.image_builder.is_runnnig

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.delete()

    def delete(self):
        try:
            self.image_builder.delete()
        except Exception as e:
            logger.error(f"delete image builder failed: {self.name}, {e}")
            pass

    def get_template(self, operator: str, to=None) -> ImageBuilder:
        image_url = "/".join([app.settings.DOCKER_REGISTRY, self.repo_namespace, f"{self.notebook_uuid}:{self.tag}"])
        logger.info(f"save notebook {self.notebook_uuid} to image: {image_url}")
        return ImageBuilder({
            "apiVersion": ImageBuilder.version,
            "kind": ImageBuilder.kind,
            "metadata": {
                "name": self.name,
                "namespace": self.namespace,
                "labels": {
                    "imageBuilderName": self.name,
                    "notebook": self.notebook_uuid,
                    "user": self.user_id
                }
            },
            "spec": {
                "namespace": self.namespace,
                "protocol": app.settings.DOCKER_REGISTRY_PROTOCOL,
                "podName": f"{self.notebook_uuid}-0",
                "containerName": self.notebook_uuid,
                "operator": operator,
                "to": to or self.image_url,
                "username": app.settings.DOCKER_ADMIN_USER,
                "password": app.settings.DOCKER_ADMIN_PASSWORD,
                "localHostPath": app.settings.IMAGE_DUMP_PATH,
            }
        })

    def save(self, to=None) -> ImageBuilder:
        imagebuilder = self.get_template("push", to)
        imagebuilder.create()
        self.image_builder = imagebuilder
        return imagebuilder

    def check(self) -> ImageBuilder:
        imagebuilder = self.get_template("check")
        imagebuilder.create()
        self.image_builder = imagebuilder
        return imagebuilder

    def check_disk_space(self) -> ImageBuilder:
        imagebuilder = self.get_template("checkdisk")
        imagebuilder.create()
        self.image_builder = imagebuilder
        return imagebuilder

    def dumps(self):
        """
        dump image to local only
        :return:
        """
        imagebuilder = self.get_template("save")
        imagebuilder.create()
        self.image_builder = imagebuilder
        return imagebuilder

    def __repr__(self):
        return f"ImageBuilderOperator(notebook_uuid={self.notebook_uuid}, namespace={self.namespace})"

    def __str__(self):
        return self.__repr__()
