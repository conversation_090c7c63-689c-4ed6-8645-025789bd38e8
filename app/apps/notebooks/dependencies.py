from functools import partial

from fastapi import Depends, Request
from sqlmodel import Session

from app.core.db import get_db_session
from app.cruds.notebooks import NotebookCRUD
from app.models.notebooks import Notebook
from app.depends.authz import has_resource_permission_namespace


def get_notebooks_crud(
        request: Request,
        session: Session = Depends(get_db_session)
) -> NotebookCRUD:
    return NotebookCRUD(session=session, user=request.user)


has_notebook_resource_permission_namespace = partial(has_resource_permission_namespace, resource_type=Notebook)
