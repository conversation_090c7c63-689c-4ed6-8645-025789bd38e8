import json
import os
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Optional

from kr8s.objects import APIObject

import app
from app.apps.notebooks.kr8s_objects.imagebuilder import ImageBuilder
from app.apps.notebooks.kr8s_objects.notebook import NoteBook as K8SNoteBook, Note<PERSON>ookCrOperator
from app.core.exceptions import AICP<PERSON><PERSON>Exception
from app.core.qingcloud.docker_api import QingcloudDockerApiClient
from app.models.notebooks import NoteBookStartParam, Notebook, NotebookCreate, NotebookReason, NotebookReplicaSpec, \
    NotebookStatus, NotebookVolumeSpec
from app.models.operation_record import OperationRecord
from app.core.constant import UserFrom, NOTEBOOK_USER_VOLUME_TYPES
from app.core.loggers import logger
from app.core.models import QingcloudUser, VolumeSpecCreate
from app.core.opensearch.client import es_client
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.core.qingcloud.interface import ZON<PERSON>_INFO, send_message_request, INSTANCE_ERROR
from app.core.utils import log_background_task_exception
from app.core.volumes.manager import VolumesDefinition, VolumesManager


@log_background_task_exception
def create_notebooks_in_back_ground(notebook_id: str, create_date: NotebookCreate, user: QingcloudUser,
                                    user_keys=None):
    """
    create notebook in background
    :param notebook_id:
    :param user_keys:
    :param notebooks:
    :param create_date:
    :param user:
    """
    notebook = Notebook.one_by_id(notebook_id, eager_load=True)
    k8s_notebook: Optional[APIObject] = None

    try:
        notebook.set_volume_specs(user, create_date.volume_specs)

        definition: Dict = K8SNoteBook.get_resource_definition(notebook=notebook, user=user, keys=user_keys,
                                                               create_data=create_date)
        k8s_notebook: APIObject = K8SNoteBook(definition)
        OperationRecord.create_by_resource(notebook, action="SubmitK8sCR", status='SubmitK8sCR',
                                           reason='SubmitK8sCR', params=json.dumps(definition)).save()

        logger.debug(f"create notebook: {json.dumps(k8s_notebook.raw)}")
        k8s_notebook.create()
        notebook.update(kuid=k8s_notebook.metadata.uid, eager_load=True)
    except Exception as e:
        if k8s_notebook and k8s_notebook.exists():
            k8s_notebook.delete()
        notebook.unlease()
        logger.exception(f"clease notebook error: {notebook_id}")
        notebook.update(status=NotebookStatus.CreateFailed, reason=str(e),
                        auto_delete_time=datetime.now() + timedelta(hours=1))
        notebook.push_message("创建容器实例失败", "CreateFailed", str(e))
        OperationRecord.create_by_resource(notebook).insert()
        notebook.send_message("容器实例创建失败", INSTANCE_ERROR)
        return

    try:
        es_client.insert_or_update(_id=k8s_notebook.metadata.uid, body=k8s_notebook.raw)
    except Exception as e:
        logger.warning(f"write es error: {e}")
    logger.info(f"create notebook success: {notebook_id}")


@log_background_task_exception
def stop_notebooks_in_background(notebook_id: str, save_image=True):
    """
    stop notebook in background
    :param save_image:
    :param notebook_id:
    """
    logger.info(f"stop {notebook_id} in background")

    notebook = Notebook.one_by_id(notebook_id, eager_load=True)

    if notebook.status != NotebookStatus.Running:
        logger.info(f"notebook is not running, skip stop notebook: {notebook.uuid}")
        return

    try:
        notebook.suspend_billing()
    except Exception as e:
        logger.error(f"suspend billing error, {e}, skip suspend notebook: {notebook.uuid}")
        notebook.push_message("订单创建中, 请稍后操作", "Running", "计费暂停失败")
        OperationRecord.create_by_resource(notebook, status="SuspendFailed", reason="计费暂停失败").insert()
        notebook.send_message("容器实例暂停失败", INSTANCE_ERROR)
        return

    OperationRecord.create_by_resource(notebook, status=NotebookStatus.Suspending, reason="用户关机").insert()
    notebook_cro = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
    notebook_cro.suspending()
    image_builder: ImageBuilder
    image_changed = ""
    pre_image_info = ""
    if save_image:
        try:
            notebook.update(status=NotebookStatus.Suspending, reason=NotebookReason.image_building, stop_time=None,
                            eager_load=True)
            notebook_cro.precheck_diskspace_for_save_image()
            image_builder = notebook_cro.dumps_image()
            OperationRecord.create_by_resource(notebook).insert()
            image_changed = image_builder.raw["spec"]["to"]
            localhost_path = os.path.join(image_builder.raw["spec"]["localHostPath"],  image_changed.split("/")[-1] + ".tar")
            #image_builder.wait_for_success()
            image_builder.wait_for_committed()
            #image_builder.delete()
            pre_node = notebook_cro.notebook_cr.get_node_name()
            pre_image_info = f"{pre_node}|{localhost_path}"
        except Exception as e:
            logger.error(f"save image error, {e}, skip stop notebook: {notebook.uuid}")
            notebook.push_message(f"容器实例暂停失败: 镜像构建失败[{str(e)}]", "Failed", str(e))
            notebook.send_message("容器实例暂停失败", INSTANCE_ERROR)
            notebook_cro.refresh()
            notebook_cro.start()
            notebook.update(status=NotebookStatus.Running, reason=NotebookReason.image_build_failed)
            return

    logger.info(f"image builder is success, change image: {notebook.uuid}")
    notebook_cro.refresh()
    notebook_cro.shut_down()
    notebook.update(status=NotebookStatus.Suspending, reason=NotebookReason.image_build_success, stop_time=None,
                    eager_load=True)
    if image_changed:
        notebook_cro.change_image("official", image_changed, pre_image_info=pre_image_info)

    OperationRecord.create_by_resource(notebook).insert()
    logger.info(f"stop notebook success: {notebook.uuid}")


@log_background_task_exception
def stop_notebooks_in_background_by_billing_action(notebook_id: str, save_image=True):
    """
    stop notebook in background
    :param save_image:
    :param notebook_id:
    :param billing_action: 是否是从billing中调用的的停止
    """
    logger.info(f"stop {notebook_id} in background with billing action.")

    notebook = Notebook.one_by_id(notebook_id, eager_load=True)

    if notebook.reason == NotebookReason.suspend_by_billing:
        logger.info(f"notebook is suspend(ing) by billing, skip stop notebook: {notebook.uuid}")
        return
    should_dumps_image = notebook.status == NotebookStatus.Running
    notebook.update(status=NotebookStatus.Suspending, reason=NotebookReason.suspend_by_billing, stop_time=None,
                    eager_load=True)
    notebook_cr = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
    try:
        if should_dumps_image:
            image_builder = notebook_cr.dumps_image()
            image_builder.wait_for_committed()
    except Exception as e:
        logger.warning(f"dump image error: {e}")

    OperationRecord.create_by_resource(notebook).insert()
    notebook_cr.shut_down()

    logger.info(f"stop notebook with billing action success: {notebook.uuid}")


@log_background_task_exception
def start_notebooks_in_background(notebook_id, notebooks_start_params: NoteBookStartParam, user: QingcloudUser):
    """
    start notebook in background
    :param notebooks_start_params:
    :param notebook_id:
    """
    logger.info(f"start notebooks in background: {notebooks_start_params.uuids}")
    notebook: Notebook = Notebook.one_by_id(notebook_id, eager_load=True)

    notebook.update(status=NotebookStatus.Restarting, reason=NotebookReason.restarting, eager_load=True)
    try:
        nb_cr_op = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
        nb_cr_op.remove_minio_volume()
        if not notebook.is_resource_group() and notebooks_start_params.sku_id and notebook.replica_specs.specs != notebooks_start_params.sku_id:
            # 更新属性
            notebook.replica_specs.specs = notebooks_start_params.sku_id
            notebook.replica_specs.resource.init_properties()
            notebook.replica_specs.update_custom_specs()
            price_info = notebook.replica_specs.resource.get_billing_price_info()
            notebook.check_resource_balance(price_info)
            notebook.update_lease(price_info, count=0)
            notebook.replica_specs.save()
            nb_cr_op.change_resource(notebook.replica_specs.resource)
        elif notebook.is_resource_group() and notebooks_start_params.custom_spec:
            replica_spec = NotebookReplicaSpec(**notebooks_start_params.custom_spec.dict(exclude={"replicas"}))
            logger.info(f"replica_spec:{replica_spec}")
            nb_cr_op.change_resource_group(notebooks_start_params.custom_spec.rg_id,
                                           notebooks_start_params.custom_spec.specs)
            nb_cr_op.change_resource(replica_spec.resource, replica_spec.custom_aipods_type)
            notebook.replica_specs.update(**notebooks_start_params.custom_spec.dict(exclude={"template_id"}))

        if notebook.replica_specs.custom_data_disk_size:
            for volume in notebook.volume_specs:
                if volume.volume_type == "LOCAL":
                    volume.quota = notebook.replica_specs.custom_data_disk_size
                    break
            else:
                volume_spec = NotebookVolumeSpec(file_set=notebook.uuid, mount_path="/root/aicp-data",
                                                 volume_type="LOCAL", notebook_uuid=notebook.uuid,
                                                 quota=notebook.replica_specs.custom_data_disk_size)
                vm = VolumesManager(user, notebook.namespace, volume_spec)
                vm.create_storage()
                vs: NotebookVolumeSpec = NotebookVolumeSpec(**volume_spec.dict())
                vs.save()
                notebook.volume_specs.append(vs)
            nb_cr_op.change_volume(user, notebook.volume_specs)

        if notebooks_start_params.disable_mount:
            preserved_volume_specs = []
            for vs in notebook.volume_specs:
                if vs.volume_type in NOTEBOOK_USER_VOLUME_TYPES:
                    # add model mount path
                    if vs.volume_type == "HOST_PATH" and vs.file_set == app.settings.POD_MODEL_NAME_MOUNT_PATH:
                        preserved_volume_specs.append(vs)
                    else:
                        continue
                else:
                    preserved_volume_specs.append(vs)
            logger.info(f"preserved volume specs: {preserved_volume_specs}")
            nb_cr_op.change_volume(user, preserved_volume_specs)

        if notebooks_start_params.volume_specs:
            notebook_volume_specs = []
            for vs in notebooks_start_params.volume_specs:
                nvs = NotebookVolumeSpec(notebook_uuid=notebook.uuid, **vs.dict())
                notebook_volume_specs.append(nvs)
            logger.info(f"change volume specs: {notebook_volume_specs}")
            nb_cr_op.change_volume(user, notebook_volume_specs)

        OperationRecord.create_by_resource(notebook).insert()
        nb_cr_op.start()
    except Exception as e:
        logger.exception(f"start notebook error: {notebook.uuid}")
        notebook.push_message("容器实例启动失败", "Failed", "启动失败")
        notebook.update(status=NotebookStatus.Suspended, reason=f"启动失败: {e}")
        OperationRecord.create_by_resource(notebook, status="StartFailed", reason=str(e)).insert()
        notebook.send_message("容器实例启动失败", INSTANCE_ERROR)
        return

    logger.info(f"start notebook success: {notebook.uuid}")


@log_background_task_exception
def start_notebooks_in_background_by_billing_action(notebook_id):
    """
    start notebook in background
    :param notebooks_start_params:
    :param notebook_id:
    """
    logger.info(f"start notebooks in background: {notebook_id}")
    notebook = Notebook.one_by_id(notebook_id, eager_load=True)
    if notebook.status in [NotebookStatus.Restarting, NotebookStatus.Running]:
        logger.info(f"Notebook is running or restarting, skip start notebook: {notebook.uuid}")
        return

    notebook.update(status=NotebookStatus.Restarting, reason=NotebookReason.restarting, eager_load=True)
    try:
        nb_cr_op = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
        OperationRecord.create_by_resource(notebook).insert()
        nb_cr_op.start()
    except Exception as e:
        logger.exception(f"start notebook error: {notebook.uuid}")
        notebook.push_message("容器实例启动失败", "Failed", "启动失败")
        notebook.update(status=NotebookStatus.Suspended, reason=f"启动失败: {e}")
        OperationRecord.create_by_resource(notebook, status="StartFailed", reason="启动失败").insert()
        notebook.send_message("容器实例启动失败", INSTANCE_ERROR)

    logger.info(f"start notebook success: {notebook.uuid}")
