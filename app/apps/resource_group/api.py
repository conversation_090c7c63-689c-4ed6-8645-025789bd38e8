from statistics import mean
from typing import Annotated, Dict, List, Optional, Sequence, Tuple

from fastapi import APIRouter, Body, Depends, Query, Request, BackgroundTasks
from kubernetes import client
from kubernetes.client import V1Node, V1NodeList

import app
from app import logger
from app.core.exceptions import PermissionDeniedException
from app.core.prometheus.monitoring_operator import MonitoringOperator, prometheusQL_meter_templates_node_GPU
from app.core.prometheus.query_params import MonitoringQueryParams
from app.depends.rg_group import resource_group_sub_user_permission
from app.models.gpu import GpuStaticInfo
from app.cruds.resource_group import ResourceGroupCrud
from app.apps.resource_group.dependencies import get_resource_crud
from app.apps.resource_group.exceptions import ResourceGroupNodeNotFoundException, \
    ResourceGroupNodeNotSupportReuseGpuException, ResourceGroupNodeNotSupportVgpuException, \
    ResourceHasTemplateException, \
    ResourceNodeHasGpuTemplateException, ResourceNodeHasTemplateException, ResourceNodeQuotaException, \
    ResourceNodeHasResourcerRunning
from app.models.resource_group import OrderByField, ProductType, ReqGetProduct, ReqResourceGroupAddNode, \
    ResourceGroup, ResourceGroupCreate, ResourceGroupDetailRep, ResourceGroupNodeGpuReuseReq, ResourceGroupNodeVgpuReq, \
    ResourceGroupRep, \
    ResourceGroupUpdate, ResourceNode, \
    ResourceNodeRep, ResourceNodeStatus, AddResourceGroupShareUser, ReqMoveNode2ResourceGroup, ResourceTemplate, \
    CreateResourceTemplateReq, ResourceTemplateQuery, ResourceTemplateRep, \
    ShowUserInfo, UpdateResourceTemplateReq, PodTypeEnum, UserShareGroupInfo, RepResourceGroupAddNode, \
    RepResourceTemplateAdd, ResumeNodeReq
from app.apps.resource_group.utils import check_sku_salable, get_node_available_resources, get_vgpu_available_resources
from app.cruds.user import UserCRUD
from app.apps.user.dependencies import get_user_crud
from app.models.user import UserInfo
from app.common.response import CREATE_RESOURCE_FAILED, ERROR_REMOVE_NODE, INTERNAL_ERROR, NOT_ENOUGH_NODE, \
    PERMISSION_DENIED, PRODUCT_NOT_FOUND, RESOURCE_GROUP_NOT_FOUND, RESOURCE_IS_RUNNING, RESOURCE_NODE_NOT_FOUND, \
    RESOURCE_NOT_ENOUGH, RESOURCE_NOT_FOUND, NOT_SUPPORT_DELETE_NODE_WITH_GROUP, RESUME_PERMISSION_DENIED
from app.core.constant import AIPODS_TYPE, NODE_TAG_LOCK, RG_NODE_TAG_KEY, RG_TAG_KEY, RG_TOLERATION_KEY, STATUS_ACTIVE, \
    UNLIMITED
from app.core.kube.api import check_node_resource_satisfy, check_taints, check_taints_by_whitelist, check_user_resource, \
    get_allowable_nodes, \
    get_nodes_by_names, get_nodes_by_resource_groups, list_nodes, \
    read_nodes, replace_node_tag
from app.core.middlewares.auth.qingcloud_auth import QingcloudUser
from app.core.prometheus.client import PromethusClient
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.interface import describe_users, patch_user_info_for_models, product_attr_query_request, \
    product_center_query_request
from app.core.qingcloud.resource import ProductCenterResource
from app.core.response import BaseGenericResponse, GenericMultipleResponse, GenericSingleResponse
from app.core.rlock import Rlock
from app.core.utils import GPUManager, get_product_gpu_model, gpu_manager
from app.core.ufm.crud import UFMInfoCrud
from app.core.ufm.dependencies import get_ufm_crud
from app.core.utils import get_product_resource_info, get_gpu_sorted_key, get_product_aipods_type

router = APIRouter(prefix="/resource", tags=["resource"])


@router.post("/resource_group",
             summary="创建资源组",
             response_model=GenericSingleResponse[ResourceGroup],
             dependencies=[Depends(resource_group_sub_user_permission())])
def create_resource_group(
        request: Request,
        resource_group_create: ResourceGroupCreate,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    创建资源组
    """
    user = request.user
    resource_group = rg_db.create_rg(resource_group_create, user)
    return GenericSingleResponse[ResourceGroup](data=resource_group)


@router.put("/resource_group", response_model=GenericSingleResponse[ResourceGroup],
            summary="更新资源组信息",
            dependencies=[Depends(resource_group_sub_user_permission())])
def update_resource_group(
        request: Request,
        resource_group_update: ResourceGroupUpdate,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    更新资源组
    """
    user = request.user
    resource_group = rg_db.update_rg(resource_group_update, user)
    return GenericSingleResponse[ResourceGroup](data=resource_group)


@router.delete("/resource_group", response_model=BaseGenericResponse,
               summary="删除资源组",
               dependencies=[Depends(resource_group_sub_user_permission())])
def delete_resource_group(
        request: Request,
        background_tasks: BackgroundTasks,
        rg_id: str = Query(description="多个参数之间用逗号分割"),
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        ufm_db: UFMInfoCrud = Depends(get_ufm_crud)
):
    """
    删除资源组
    """
    user = request.user
    resource_group = rg_db.get_rg(rg_id)
    if not resource_group or resource_group.user_id != user.user_id:
        raise PermissionDeniedException(message=rg_id)

    resource_template = rg_db.get_template_by_rg_id(rg_id)
    if resource_template:
        raise ResourceHasTemplateException(rg_id)

    if app.settings.billing_enable:
        billing = QAIBillingService()
        billing.unlease(rg_id, request.user.user_id)

    ret = rg_db.terminate_by_uuid_and_user_id(rg_id, user)
    return ret


def ufm_delete_resources_group(ufm_db: UFMInfoCrud, user_id: str, hostnames: List[str]):
    guids = ufm_db.get_guids(hostnames)
    pkey = ufm_db.get_ufm_info_by_user(user_id)
    ufm_db.unbind_guids_pkey(guids, pkey.pkey)


@router.get("/resource_group", response_model=GenericMultipleResponse[ResourceGroupRep],
            summary="查询资源组",
            dependencies=[Depends(resource_group_sub_user_permission())])
def get_resource_group(
        request: Request,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        offset: int = 0,
        limit: int = 20,
        reverse: bool = False,
        order_by: str = 'created_at',
        search_word: str = None

):
    """
    查询资源组
    """
    user: QingcloudUser = request.user
    resource_groups = rg_db.get_rg_by_user(user,
                                           order_by=order_by, reverse=reverse,
                                           offset=offset, limit=limit,
                                           search_word=search_word)
    if not resource_groups:
        return GenericMultipleResponse[ResourceGroupRep](data=[], counts=0)

    data = []
    pc = PromethusClient()
    aicp_node_resource = pc.aicp_resource_used_node()
    aicp_gpu_resource = pc.get_count_used_gpu_and_vgpu_by_node()
    for resource_group in resource_groups:

        total_gpu, ava_gpu, nodes, sku_id, gpu_model = rg_db.get_rg_gpus_and_node_count(resource_group.rg_id, user)
        ava_gpu = total_gpu - sum([aicp_gpu_resource.get(x, 0) for x in nodes])
        resource_group_rep = ResourceGroupRep(
            **dict(resource_group), total_gpu=total_gpu, ava_gpu=ava_gpu, sku_id=sku_id, gpu_model=gpu_model,
            total_node_count=len(nodes), used_node_count=len(set(aicp_node_resource.keys() & set(nodes))),
            used_gpu_count=sum([aicp_gpu_resource.get(x, 0) for x in nodes], ),
        )
        data.append(resource_group_rep)

        if not nodes:
            continue  # 资源组没有节点

        label_selector = f"aicp.group/resource_group={resource_group.rg_id}"
        rg_nodes: V1NodeList = list_nodes(label_selector=label_selector)
        if not rg_nodes.items:
            logger.critical("resource group [%s] nodes not found", resource_group.rg_id)

        nodes_name = [node.metadata.name for node in rg_nodes.items]
        node: V1Node = rg_nodes.items[0]
        gpu_vendor = ""
        if gpu_matcher := GPUManager().get_node_gpu_type(node.status.allocatable):
            gpu_vendor = gpu_matcher.vendor

        if not gpu_vendor:
            continue

        resource_group_rep.unschedulable_gpu_count = rg_db.get_unschedulable_gpu_count(nodes_name)
        resource_group_rep.unschedulable_node_count = len(
            list(filter(lambda x: not check_taints_by_whitelist(x.spec.taints), rg_nodes.items))
        )

        gpu_metrics = MonitoringOperator().get_named_metrics(
            list(prometheusQL_meter_templates_node_GPU.keys()),
            MonitoringQueryParams(resource_filer=nodes_name, gpu_vendor=gpu_vendor)
        )
        for gm in gpu_metrics:
            if gm["metric_name"] == "node_gpu_util":
                resource_group_rep.gpu_ava_util = mean([float(x["value"][1]) for x in gm["result"]])
            elif gm["metric_name"] == "node_gpu_mem_usage":
                resource_group_rep.gpu_used_mem = sum([int(x["value"][1]) for x in gm["result"]])
            elif gm["metric_name"] == "node_gpu_mem_total":
                resource_group_rep.gpu_sum_mem = sum([int(x["value"][1]) for x in gm["result"]])

    patch_user_info_for_models(data)
    counts = rg_db.get_rg_count_by_user(user, search_word=search_word)

    return GenericMultipleResponse[ResourceGroupRep](data=data, counts=counts)


@router.get("/resource_group/detail",
            summary="查询资源组详情",
            response_model=GenericSingleResponse[ResourceGroupDetailRep])
def get_resource_group_detail(
        request: Request,
        rg_id: str,
        zone_id: str = None,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    查询资源组详情
    """
    user: QingcloudUser = request.user
    resource_group = rg_db.get_rg(rg_id)
    rep = ResourceGroupDetailRep(resource_group=resource_group)
    if resource_group:
        nodes = rg_db.get_rgns(rg_id, user, limit=100)
        logger.info("get nodes [%s]", nodes)
        rep.resource_nodes = nodes
    return GenericSingleResponse[ResourceGroupDetailRep](data=rep)


@router.post("/resource_group/share/user", response_model=BaseGenericResponse,
             summary="添加资源组共享用户",
             dependencies=[Depends(resource_group_sub_user_permission())])
def add_resource_group_share_user(
        request: Request,
        req: AddResourceGroupShareUser,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    资源组添加共享用户
    """
    rg_db.user = request.user
    resource_group = rg_db.get_by_uuid_with_permission(req.rg_id)
    if resource_group:
        rg_db.add_resource_group_share_user(request.user,
                                            req.share_user_ids, req.rg_id,
                                            req.is_all)
    else:
        logger.info("resource_group not found")
    return BaseGenericResponse()


@router.delete("/resource_group/share/user", response_model=BaseGenericResponse,
               summary="删除资源组共享用户",
               dependencies=[Depends(resource_group_sub_user_permission())])
def remove_resource_group_share_user(
        request: Request,
        rg_id: str = '',
        is_all: int = 0,
        share_user_ids: str = '',
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    资源组移除共享用户
    """
    rg_db.user = request.user
    rg_db.get_by_uuid_with_permission(rg_id)
    share_user_ids = share_user_ids.strip(",").split(",")
    rg_db.remove_resource_group_share_user(rg_id, is_all, share_user_ids)
    return BaseGenericResponse()


def get_resource_group_share():
    pass


@router.get("/resource_group/share/user", response_model=GenericMultipleResponse[UserShareGroupInfo],
            summary="查询资源组共享用户",
            dependencies=[Depends(resource_group_sub_user_permission())])
def list_resource_group_share_user(
        request: Request,
        rg_id: str = None,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        offset: int = 0,
        limit: int = 1000,
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    查询资源组已共享用户
    """
    rg_db.user = request.user
    if rg_id:
        rg_db.get_by_uuid_with_permission(rg_id)
        rep, count = rg_db.get_resource_group_share_user(rg_id, offset, limit)
    else:
        rep, count = rg_db.get_resource_group_share_all_sub_user(offset, limit)
    data = []
    for user in rep.get("user_set", []):
        resource = user_db.get_user_resource_num(user["user_id"])
        rgs = rg_db.get_resource_group_share(user["user_id"])
        user_info = user_db.get_user_info_by_user_id(user["user_id"])
        if not user_info:
            user_info = UserInfo(containers_number=10,
                                 jobs_number=10,
                                 priority=3)
        sub_user_info = UserShareGroupInfo(**user, resource_group=rgs)
        sub_user_info.quota_containers = user_info.containers_number
        sub_user_info.quota_jobs = user_info.jobs_number
        sub_user_info.jobs = resource[1]
        sub_user_info.containers = resource[0]
        sub_user_info.priority = user_info.priority
        data.append(sub_user_info)
    return GenericMultipleResponse[UserShareGroupInfo](data=data, counts=count)


@router.get("/resource_group/share",
            summary="查询被共享给我的资源组",
            response_model=GenericMultipleResponse[ResourceGroupRep])
def list_resource_group_share(
        request: Request,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    查询被共享的资源组
    """
    resource_groups = rg_db.get_resource_group_share(request.user.user_id)
    data = []
    for resource_group in resource_groups:
        total_gpu, ava_gpu, nodes, sku_id, gpu_model = rg_db.get_rg_gpus_and_node_count(resource_group.rg_id)
        resource_group_rep = ResourceGroupRep(**dict(resource_group),
                                              total_gpu=total_gpu, ava_gpu=ava_gpu,
                                              total_node_count=len(nodes),
                                              sku_id=sku_id,
                                              gpu_model=gpu_model)
        data.append(resource_group_rep)
    counts = len(resource_groups)
    return GenericMultipleResponse[ResourceGroupRep](data=data, counts=counts)


@router.get("/product",
            summary="查询产品列表",
            response_model=dict)
def get_resource_product(
        request: Request,
        region_id: str,
        console_id: str,
        value: Optional[ProductType] = Query(None, description="Query parameter in URL"),
        aipods_usage: list = Query([], description="资源类型"),
        reverse: int = Query(0, description="排序方式"),
        offset: int = 0,
        limit: int = 20,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    """
    查询产品
    value：
    resource_group  专属资源组
    sharing_compute  共享资源
    container_instance  开发机
    """
    kwargs = {}
    if aipods_usage:
        kwargs["aipods_usage"] = aipods_usage
    result = product_center_query_request([value] if value else None,
                                          console_id=console_id,
                                          regin_id=region_id,
                                          offset=offset, limit=limit, **kwargs)
    skus = result["skus"]
    sorted_skus = sorted(skus, key=get_gpu_sorted_key)

    allowable_nodes: List[V1Node] = get_allowable_nodes()
    nodes_status: List[ResourceNodeStatus] = get_node_available_resources(allowable_nodes)
    nodes_vgpu_status = get_vgpu_available_resources(allowable_nodes)
    resource_used_node = PromethusClient().aicp_resource_used_node()
    for sku in sorted_skus:
        sku["salable"], sku["max_applicable_number"], sku["salable_detail"] = check_sku_salable(sku, allowable_nodes, nodes_status,
                                                                                                nodes_vgpu_status, resource_used_node)

    result["skus"] = sorted_skus
    return result


@router.post("/product", response_model=dict,
             dependencies=[Depends(resource_group_sub_user_permission())])
def get_resource_product_filter(
        region_id: str,
        console_id: str,
        request: Request,
        req: ReqGetProduct,

):
    """
    查询产品信息
    """
    logger.info(req)
    result = product_center_query_request(filters=req.filters,
                                          offset=req.offset, limit=req.limit,
                                          console_id=console_id,
                                          regin_id=region_id)
    skus = result["skus"]
    sotred_skus = sorted(skus, key=get_gpu_sorted_key)
    result["skus"] = sotred_skus
    return result


@router.get("/product/attr",
            summary="查询产品属性",
            response_model=dict)
def get_resource_product_attr(
        request: Request,
        zone_id: str = None,
):
    """
    查询产品属性值
    """
    result = product_attr_query_request()
    return result


@router.post("/node", response_model=RepResourceGroupAddNode,
             summary="申请计算节点",
             dependencies=[Depends(resource_group_sub_user_permission())])
def add_node(
        request: Request,
        req: ReqResourceGroupAddNode,
        background_tasks: BackgroundTasks,
        zone_id: str = None,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        ufm_db: UFMInfoCrud = Depends(get_ufm_crud),
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    申请节点
    """
    # Quota verification
    user_db.quota_verification(request.user.user_id, "rg", req.count, req.sku_id)
    rep = RepResourceGroupAddNode(rgn_ids=[])
    user = request.user
    if app.settings.billing_enable:
        # 创建新订单
        billing = QAIBillingService()
        price_info = ProductCenterResource(req.sku_id, 1).get_billing_price_info()
        logger.info(price_info)
        billing.check_resource_balance("rgn-tmp", request.user.user_id,
                                       price_info, duration=req.duration, count=req.count)
    # return rep
    if req.rg_id:
        resource_group = rg_db.get_rg(req.rg_id)
        if not resource_group:
            rep.ret_code = RESOURCE_NOT_FOUND
            rep.message = RESOURCE_GROUP_NOT_FOUND
            return rep
    result = product_center_query_request(search_word=req.sku_id)
    if not result["skus"]:
        rep.ret_code = RESOURCE_NOT_FOUND
        rep.message = PRODUCT_NOT_FOUND
        return rep
    product = result["skus"][0]
    if product["status"] != 'sale':
        rep.ret_code = RESOURCE_NOT_FOUND
        rep.message = PRODUCT_NOT_FOUND
        return rep
    filters = product["filters"]
    resource_conf = get_product_resource_info(filters)
    aipods_type = get_product_aipods_type(filters)
    gpu_model = get_product_gpu_model(filters)
    gpu_model_resource_key = ""
    if gpu_model:
        gpu = gpu_manager.get_gpu_matcher(gpu_model)
        gpu_model_key = gpu.label
        gpu_model_resource_key = gpu.resource
        logger.debug("节点配置[%s]", resource_conf)
        label_selector = f"{AIPODS_TYPE}={aipods_type},{gpu_model_key}={gpu_model},!{RG_NODE_TAG_KEY}"
    else:
        label_selector = f"{AIPODS_TYPE}={aipods_type},!{RG_NODE_TAG_KEY}"
    resource_nodes = []
    with Rlock(NODE_TAG_LOCK):
        logger.info("label_selector[%s]", label_selector)
        rsp = list_nodes(label_selector=label_selector)
        nodes = rsp.items
        ava_nodes = []
        logger.info("[%s] node will be select", len(rsp.items))
        if len(nodes) < req.count:
            rep.ret_code = RESOURCE_NOT_ENOUGH
            rep.message = NOT_ENOUGH_NODE
            return rep
        for node in nodes:
            if check_node_resource_satisfy(node.metadata.name,
                                           gpu=resource_conf["gpu"],
                                           cpu=resource_conf["cpu"],
                                           memory=resource_conf["memory"],
                                           gpu_model_resource_key=gpu_model_resource_key):
                ava_nodes.append(node)
                if len(ava_nodes) == req.count:
                    break
        if len(ava_nodes) < req.count:
            rep.ret_code = RESOURCE_NOT_ENOUGH
            rep.message = NOT_ENOUGH_NODE
            return rep
        for node in ava_nodes:
            node_name = node.metadata.name
            logger.info(f"Node Name: {node_name}")
            node_labels = node.metadata.labels or {}
            rg_node_id = rg_db.generate_rgn_id()
            if req.rg_id:
                node_labels.update({RG_TAG_KEY: req.rg_id, RG_NODE_TAG_KEY: rg_node_id})
            else:
                node_labels.update({RG_NODE_TAG_KEY: rg_node_id})
            node.metadata.labels = node_labels
            logger.info(f"Node Labels: {node_labels}")
            taint = client.V1Taint(key=RG_NODE_TAG_KEY,
                                   value=rg_node_id, effect="NoSchedule")
            if node.spec.taints:
                node.spec.taints.append(taint)
            else:
                node.spec.taints = [taint]
            if req.rg_id:
                taint = client.V1Taint(key=RG_TOLERATION_KEY,
                                       value=req.rg_id, effect="NoSchedule")
                node.spec.taints.append(taint)
            if replace_node_tag(node_name, node):
                resource_node = ResourceNode(hostname=node_name,
                                             status=STATUS_ACTIVE,
                                             name=node_name,
                                             during=req.duration,
                                             auto_renewal=req.auto_renewal,
                                             sku_id=req.sku_id,
                                             user_id=user.user_id,
                                             root_user_id=user.root_user_id,
                                             rg_node_id=rg_node_id,
                                             next_charge_mode=req.next_charge_mode,
                                             billing_order_id=rg_node_id,
                                             **resource_conf)
                if req.rg_id:
                    resource_node.rg_id = req.rg_id
                else:
                    resource_node.rg_id = rg_node_id
                resource_nodes.append(resource_node)
                rep.rgn_ids.append(rg_node_id)
            else:
                logger.critical("when add node to rg update tag failed")
                rep.ret_code = RESOURCE_NOT_ENOUGH
                rep.message = NOT_ENOUGH_NODE
                return rep
    if not rg_db.add_rgn(resource_nodes):
        logger.critical("insert_ into db_node info failed")
        rep.message = CREATE_RESOURCE_FAILED
        rep.ret_code = INTERNAL_ERROR
        return rep
    if app.settings.billing_enable:
        for rgn in resource_nodes:
            # 创建新订单
            billing = QAIBillingService()
            price_info = ProductCenterResource(req.sku_id, 1).get_billing_price_info()
            logger.info(price_info)
            bill_rep = billing.lease(rgn.rg_node_id, request.user.user_id, price_info, duration=req.duration,
                                     charge_mode=req.charge_mode, auto_renew=req.auto_renewal,
                                     check_resource_balance=True, count=1)
            if bill_rep['ret_code'] != 0:
                logger.critical("创建订单失败%s", rgn.rg_node_id)
    # env have ufm
    if app.settings.UFM_ENABLE:
        # pkey bind guids
        background_tasks.add_task(pkey_bind_guids, ufm_db, request.user.user_id, resource_nodes)
    return rep


@router.post("/add/node", response_model=BaseGenericResponse,
             summary="添加节点到资源组",
             dependencies=[Depends(resource_group_sub_user_permission())])
def move_node_to_group(request: Request,
                       req: ReqMoveNode2ResourceGroup,
                       rg_db: ResourceGroupCrud = Depends(get_resource_crud),
                       ):
    '''
    把节点加入资源组
    :param request:
    :param req:
    :param rg_db:
    :return:
    '''
    rep = BaseGenericResponse()
    user = request.user
    # return rep
    if req.rg_id:
        resource_group = rg_db.get_rg(req.rg_id)
        if not resource_group:
            rep.ret_code = RESOURCE_NOT_FOUND
            rep.message = RESOURCE_GROUP_NOT_FOUND
            return rep
    resource_group_nodes = rg_db.get_rgn_by_ids_and_user(req.rg_node_ids, user.user_id)
    if not resource_group_nodes:
        return rep

    if rg_db.get_template_on_rgn_ids(req.rg_node_ids):
        raise ResourceNodeHasTemplateException(req.rg_node_ids)

    for rg_node in resource_group_nodes:
        rg_node_id = rg_node.rg_node_id
        label_selector = f"{RG_NODE_TAG_KEY}={rg_node_id}"
        rsp = list_nodes(label_selector=label_selector)
        nodes = rsp.items
        for node in nodes:
            node_name = node.metadata.name
            logger.info(f"Node Name: {node_name}")
            node_labels = node.metadata.labels or {}
            node_labels.update({RG_TAG_KEY: req.rg_id})
            node.metadata.labels = node_labels
            logger.info(f"Node Labels: {node_labels}")
            if req.rg_id:
                taint = client.V1Taint(key=RG_TOLERATION_KEY,
                                       value=req.rg_id, effect="NoSchedule")
                if node.spec.taints:
                    for t in node.spec.taints:
                        if t.key == RG_TOLERATION_KEY:
                            node.spec.taints.remove(t)
                            break
                    node.spec.taints.append(taint)
                else:
                    node.spec.taints = [taint]
            if replace_node_tag(node_name, node):
                rg_node.rg_id = req.rg_id
                rg_db.session.commit()

    return rep


@router.post("/remove/node", response_model=BaseGenericResponse,
             summary="从资源组中移除节点",
             dependencies=[Depends(resource_group_sub_user_permission())])
def remove_node_from_group(request: Request,
                           req: ReqMoveNode2ResourceGroup,
                           rg_db: ResourceGroupCrud = Depends(get_resource_crud),
                           ):
    '''
    从资源组中移除节点
    :param request:
    :param req:
    :param rg_db:
    :return:
    '''
    rep = BaseGenericResponse()
    user = request.user
    resource_group_nodes = rg_db.get_rgn_by_ids_and_user(req.rg_node_ids, user.user_id)
    if not resource_group_nodes:
        return rep
    if rg_db.get_template_by_rg_id_and_rgn_ids(req.rg_id, req.rg_node_ids):
        raise ResourceNodeHasTemplateException(req.rg_node_ids)
    for rg_node in resource_group_nodes:
        rg_node_id = rg_node.rg_node_id
        label_selector = f"{RG_NODE_TAG_KEY}={rg_node_id}"
        rsp = list_nodes(label_selector=label_selector)
        nodes = rsp.items
        for node in nodes:
            node_name = node.metadata.name
            logger.info(f"Node Name: {node_name}")
            node_labels = node.metadata.labels or {}
            node_labels.update({RG_TAG_KEY: rg_node_id})
            node.metadata.labels = node_labels
            logger.info(f"Node Labels: {node_labels}")
            if node.spec.taints:
                for t in node.spec.taints:
                    if t.key == RG_TOLERATION_KEY:
                        node.spec.taints.remove(t)
                        break
            if replace_node_tag(node_name, node):
                rg_node.rg_id = rg_node_id
                rg_db.session.add(rg_node)
    return rep


@router.delete("/node", response_model=BaseGenericResponse,
               summary="删除计算节点",
               dependencies=[Depends(resource_group_sub_user_permission())])
def del_node(
        request: Request,
        background_tasks: BackgroundTasks,
        rg_id: str = None,
        rg_node_ids: str = Query(description="多个参数之间用逗号分割"),
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        ufm_db: UFMInfoCrud = Depends(get_ufm_crud)):
    """
    删除node
    """
    rg_node_ids = rg_node_ids.split(",")
    rep = BaseGenericResponse()
    ids = []
    hostnames = []
    rg_db.user = request.user
    resource_nodes = rg_db.get_rgn_by_ids(rg_node_ids, limit=UNLIMITED)
    for r in resource_nodes:
        ids.append(r.rg_node_id)
        hostnames.append(r.hostname)
    resource = check_user_resource(hostnames, request.user.user_id.lower())
    if resource:
        rep.ret_code = PERMISSION_DENIED
        rep.message = RESOURCE_IS_RUNNING % resource
        return rep
    for rg_node_id in rg_node_ids:
        if rg_node_id not in ids:
            rep.ret_code = RESOURCE_NOT_FOUND
            rep.message = RESOURCE_NODE_NOT_FOUND
            return rep
    if rg_db.get_template_on_rgn_ids(rg_node_ids):
        raise ResourceNodeHasTemplateException(rg_node_ids)
    if not rg_db.delete_rgn_by_rg_node_ids(rg_node_ids, request.user.user_id):
        rep.ret_code = -1
        rep.message = ERROR_REMOVE_NODE
        return rep
    # env have ufm
    if app.settings.UFM_ENABLE:
        # pkey bind guids
        background_tasks.add_task(pkey_unbind_guids, ufm_db, request.user.user_id, resource_nodes)
    return rep


@router.post("/node/resume", response_model=BaseGenericResponse,
               summary="恢复节点",
               dependencies=[Depends(resource_group_sub_user_permission())])
def resume_node(
        request: Request,
        req: ResumeNodeReq,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)):
    """
    恢复
    """
    rep = BaseGenericResponse()
    rg_db.user = request.user
    resource_nodes = rg_db.get_rgn_by_ids(req.rg_node_ids, limit=UNLIMITED)
    if not resource_nodes:
        rep.ret_code = RESOURCE_NOT_FOUND
        rep.message = RESOURCE_NODE_NOT_FOUND
    if not rg_db.resume_rgn_by_rg_node_ids(req.rg_node_ids, request.user.user_id):
        rep.ret_code = -1
        rep.message = RESUME_PERMISSION_DENIED
        return rep
    return rep


@router.get("/node",
            summary="查询计算节点",
            response_model=GenericMultipleResponse[ResourceNodeRep])
def get_resource_group_node(
        request: Request,
        rg_id: str,
        limit: int = 10,
        offset: int = 0,
        search_word: str = Query(description="搜索", default=None),
        node_status_filter: str = Query(description="节点状态筛选", default=None),
        worker_status_filter: str = Query(description="工作状态筛选", default=None),
        gpu_name: str = Query(description="GPU型号", default=None),
        aipods_usage: str = Query(description="资源类型筛选，可选资源参考产品中心", default=None),
        order_by: OrderByField = Query(description="排序", default="created_at"),
        is_reused_gpu_node: Optional[bool] = Query(
            description="是否共享节点, null为查询所有, True为查询GPU共享节点, False为查询非共享节点", default=None),
        is_vgpu_node: Optional[bool] = Query(
            description="是否vGPU节点, null为查询所有, True为查询vGPU节点, False为查询非vGPU节点", default=None),
        reverse: int = 0,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)):
    """
    查询资源组节点
    """
    resource_nodes, count = rg_db.get_rgns_and_status(rg_id=rg_id, user=request.user,
                                                      limit=limit, offset=offset,
                                                      search_word=search_word,
                                                      node_status_filter=node_status_filter,
                                                      worker_status_filter=worker_status_filter,
                                                      aipods_usage=aipods_usage,
                                                      order_by=order_by,
                                                      reverse=reverse,
                                                      gpu_name=gpu_name,
                                                      is_reused_gpu_node=is_reused_gpu_node,
                                                      is_vgpu_node=is_vgpu_node)
    if not resource_nodes:
        return GenericMultipleResponse[ResourceNodeRep](data=[], counts=0)

    data = []
    for r in resource_nodes:
        result = PromethusClient().get_aicp_resource_requests(r.ResourceNode.hostname)
        used_gpu = 0
        used_cpu = 0
        used_memory = 0
        for node in result:
            if node["metric"]["resource"] == "cpu":
                used_cpu = int(float(node["value"][1]))
            if node["metric"]["resource"] == "memory":
                used_memory = int(float(node["value"][1])) / 1024 / 1024 / 1024
            if node["metric"]["resource"] == "nvidia_com_gpu":
                used_gpu = int(float(node["value"][1]))
        used_disk, os_used_disk = rg_db.get_user_used_disk(r.ResourceNode.rg_node_id)
        if r.ResourceNodeStatus:
            r.ResourceNodeStatus.ava_data_disk = r.ResourceNode.disk - used_disk
            r.ResourceNodeStatus.ava_os_disk = r.ResourceNode.os_disk - os_used_disk
            r.ResourceNodeStatus.ava_cpu = max(r.ResourceNode.cpu - used_cpu, 0)
            r.ResourceNodeStatus.ava_memory = max(r.ResourceNode.memory - used_memory, 0)
            r.ResourceNodeStatus.ava_gpu = r.ResourceNode.gpu - used_gpu

            r.ResourceNodeStatus.total_cpu = r.ResourceNode.cpu
            r.ResourceNodeStatus.total_memory = r.ResourceNode.memory
            r.ResourceNodeStatus.total_data_disk = r.ResourceNode.disk
            r.ResourceNodeStatus.total_gpu = r.ResourceNode.gpu
            r.ResourceNodeStatus.total_os_disk = r.ResourceNode.os_disk

        resource_node = ResourceNodeRep(**dict(r.ResourceNode), node_status=r.ResourceNodeStatus,
                                        resource_group=r.ResourceGroup)
        data.append(resource_node)
    patch_user_info_for_models(data)
    return GenericMultipleResponse[ResourceNodeRep](data=data, counts=count)


@router.get("/node/all", response_model=GenericMultipleResponse[ResourceNodeRep],
            summary="查询所有计算节点",
            dependencies=[Depends(resource_group_sub_user_permission())])
def get_resource_group_node_all(
        request: Request,
        rg_id: str = '',
        limit: int = 10,
        offset: int = 0,
        gpu_model: str = '',
        search_word: str = Query(description="搜索", default=None),
        node_status_filter: str = Query(description="节点状态筛选", default=None),
        worker_status_filter: str = Query(description="工作状态筛选", default=None),
        aipods_usage: str = Query(description="资源类型筛选，可选资源参考产品中心", default=None),
        order_by: OrderByField = Query(description="排序", default="created_at"),
        reverse: int = 0,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)):
    """
    查询所有计算节点
    """
    resource_nodes, count = rg_db.get_rgns_and_status(user=request.user,
                                                      limit=limit, offset=offset,
                                                      search_word=search_word,
                                                      node_status_filter=node_status_filter,
                                                      worker_status_filter=worker_status_filter,
                                                      aipods_usage=aipods_usage,
                                                      order_by=order_by,
                                                      reverse=reverse,
                                                      gpu_model=gpu_model,
                                                      rg_id=rg_id)
    if not resource_nodes:
        return GenericMultipleResponse[ResourceNodeRep](data=[], counts=0)

    data = []
    for r in resource_nodes:
        result = PromethusClient().get_aicp_resource_requests(r.ResourceNode.hostname)
        used_gpu = 0
        used_cpu = 0
        used_memory = 0
        for node in result:
            if node["metric"]["resource"] == "cpu":
                used_cpu = int(float(node["value"][1]))
            if node["metric"]["resource"] == "memory":
                used_memory = int(float(node["value"][1])) / 1024 / 1024 / 1024
            if node["metric"]["resource"] == "nvidia_com_gpu":
                used_gpu = int(float(node["value"][1]))
        used_disk, os_used_disk = rg_db.get_user_used_disk(r.ResourceNode.rg_node_id)
        if r.ResourceNodeStatus:
            r.ResourceNodeStatus.ava_data_disk = r.ResourceNode.disk - used_disk
            r.ResourceNodeStatus.ava_os_disk = r.ResourceNode.os_disk - os_used_disk
            r.ResourceNodeStatus.ava_cpu = r.ResourceNode.cpu - used_cpu
            r.ResourceNodeStatus.ava_memory = r.ResourceNode.memory - used_memory
            r.ResourceNodeStatus.ava_gpu = r.ResourceNode.gpu - used_gpu

            r.ResourceNodeStatus.total_cpu = r.ResourceNode.cpu
            r.ResourceNodeStatus.total_memory = r.ResourceNode.memory
            r.ResourceNodeStatus.total_data_disk = r.ResourceNode.disk
            r.ResourceNodeStatus.total_gpu = r.ResourceNode.gpu
            r.ResourceNodeStatus.total_os_disk = r.ResourceNode.os_disk

        resource_node = ResourceNodeRep(**dict(r.ResourceNode),
                                        node_status=r.ResourceNodeStatus,
                                        resource_group=r.ResourceGroup)
        data.append(resource_node)
    patch_user_info_for_models(data)
    return GenericMultipleResponse[ResourceNodeRep](data=data, counts=count)


@router.get("/node/status", response_model=GenericMultipleResponse[ResourceNodeStatus],
            summary="查询计算节点详情",
            dependencies=[Depends(resource_group_sub_user_permission())])
def get_resource_node_status(
        request: Request,
        rg_node_id: str = None,
        rg_id: str = None,
        limit: int = 10,
        offset: int = 0,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)):
    """
    查询资源组节点
    """
    resource_nodes = rg_db.get_node_status(rg_id, rg_node_id, request.user, limit, offset)
    return GenericMultipleResponse[ResourceNodeStatus](data=resource_nodes, counts=len(resource_nodes))


@router.get("/node/price", response_model=GenericSingleResponse[dict],
            summary="获取计算节点价格",
            dependencies=[Depends(resource_group_sub_user_permission())])
def get_price(
        request: Request,
        rg_id: str = Query(description="资源组id"),
        sku_id: str = Query(description="sku id"),
        count: int = Query(1, description="replicas"),
        charge_mode="monthly",
        duration=1
):
    """
    获取计算节点价格
    """
    production = ProductCenterResource(sku_id, count)
    billing = QAIBillingService()
    price = billing.get_price(request.user.user_id, production.get_billing_price_info(),
                              charge_mode=charge_mode,
                              duration=duration)
    return GenericSingleResponse[dict](data={"price": price["price_set"][0]})


@router.post("/template", response_model=RepResourceTemplateAdd,
             summary="创建资源模板",
             dependencies=[Depends(resource_group_sub_user_permission())])
def create_resource_template(
        request: Request,
        req: CreateResourceTemplateReq,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    '''
    创建资源模板
    :param request:
    :param req:
    :param rg_db:
    :return:
    '''
    user = request.user
    tmp_ids = rg_db.add_resource_template(req, user)
    rep = RepResourceTemplateAdd(tmp_ids=tmp_ids)
    return rep


@router.get("/template",
            summary="查询资源模板",
            response_model=GenericMultipleResponse[ResourceTemplateRep])
def get_resource_template(
        query: Annotated[ResourceTemplateQuery, Query()],
        request: Request,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    '''
    查询资源模板
    :param pod_type: 资源类型 all表示所有
    :param request:
    :param rg_id:
    :param rg_db:
    :return:
    '''
    result = rg_db.get_resource_template(query)

    show_users = list(set([user_id for rt, rg in result if rt.show_user for user_id in rt.show_user]))
    users = describe_users(show_users) or []

    rgs_id = list(set(rt.rg_id for rt, rg in result if rt.rg_id))
    rg_nodes: Sequence[ResourceNode] = rg_db.get_rgn_by_rgs_id(rgs_id)
    rg_nodes_name: Dict[str, str] = {x.rg_node_id: x.name for x in rg_nodes}

    gpu_map = {x.gpu_name: x.gpu_model for x in rg_nodes}
    allowable_nodes: List[V1Node] = get_nodes_by_resource_groups(rgs_id)
    nodes_status: List[ResourceNodeStatus] = get_node_available_resources(allowable_nodes, rg_nodes)
    nodes_vgpu_status = get_vgpu_available_resources(allowable_nodes)
    resource_used_node = PromethusClient().aicp_resource_used_node()

    data = []
    resource_template: ResourceTemplate
    for resource_template, resource_group in result:
        item = ResourceTemplateRep(
            **resource_template.dict(exclude={"pod_type"}), resource_group=resource_group,
            pod_type=resource_template.pod_type.split(",") if resource_template.pod_type else [],
            show_users_info=[
                ShowUserInfo(**x) for x in users if x['user_id'] in resource_template.show_user
            ] if resource_template.show_user else None,
            rg_node_name=rg_nodes_name.get(resource_template.rg_node_id, None)
        )
        # if resource_template.gpu_list:
        #     # share gpu do not check salable
        #     salable, max_applicable_number = True, 99
        # else:
        salable, max_applicable_number, _ = check_sku_salable(resource_template.to_sku(gpu_map), allowable_nodes,
                                                           nodes_status, nodes_vgpu_status, resource_used_node)
        item.salable, item.max_applicable_number = salable, max_applicable_number
        data.append(item)
    return GenericMultipleResponse[ResourceTemplateRep](data=data, counts=len(result))


@router.put("/template", response_model=BaseGenericResponse,
            summary="更新资源模板",
            dependencies=[Depends(resource_group_sub_user_permission())])
def update_resource_template(
        request: Request,
        req: UpdateResourceTemplateReq,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    '''
    更新资源模板
    :param request:
    :param req:
    :param rg_db:
    :return:
    '''
    user = request.user
    rg_db.update_resource_template(req, user)
    rep = BaseGenericResponse()
    return rep


@router.put("/node/gpu_reuse", response_model=GenericSingleResponse[ResourceNode],
            summary="设置节点为GPU共享节点",
            dependencies=[Depends(resource_group_sub_user_permission())])
def node_gpu_reuse(
        request: Request,
        req: ResourceGroupNodeGpuReuseReq,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    '''
    设置节点为GPU共享节点
    :param request:
    :param req:
    :param rg_db:
    :return:
    '''
    user = request.user
    nodes = rg_db.get_rgn_by_ids_and_user([req.rg_node_id], user.user_id)
    if not nodes:
        raise ResourceGroupNodeNotFoundException()

    if not req.is_reused_gpu_node:
        if rg_db.get_reused_gpu_template_on_rgn_id(req.rg_node_id):
            raise ResourceNodeHasGpuTemplateException(req.rg_node_id)

    node = nodes[0]
    if node.get_gpu_vendor() != "NVIDIA":
        raise ResourceGroupNodeNotSupportReuseGpuException()

    node.is_reused_gpu_node = req.is_reused_gpu_node
    return GenericSingleResponse[ResourceNode](data=node)


# 设置节点为vgpu
@router.put("/node/vgpu", response_model=BaseGenericResponse,
            summary="设置节点为vGPU节点",
            dependencies=[Depends(resource_group_sub_user_permission())])
def node_vgpu(
        request: Request,
        req: ResourceGroupNodeVgpuReq,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    '''
    设置节点为vGPU节点
    :param request:
    :param req:
    :param rg_db:
    :return:
    '''
    user = request.user
    nodes = rg_db.get_rgn_by_ids_and_user([req.rg_node_id], user.user_id)
    if not nodes:
        raise ResourceGroupNodeNotFoundException()

    rg_node = nodes[0]
    if rg_node.is_vgpu_node == req.is_vgpu_node:
        return BaseGenericResponse()

    gpu_matcher = rg_node.get_gpu_matcher()
    if not gpu_matcher.virtual:
        raise ResourceGroupNodeNotSupportVgpuException()

    for r in nodes:
        resource = check_user_resource(r.hostname, request.user.user_id.lower())
        if resource:
            raise ResourceNodeHasResourcerRunning()

    with Rlock(NODE_TAG_LOCK):
        node: V1Node = read_nodes(rg_node.hostname)
        if not node:
            raise ResourceGroupNodeNotFoundException()
        key = "aicp.group/aipods_type"
        pre_key = "pre.aicp.group/aipods_type"
        node_labels = node.metadata.labels or {}
        if req.is_vgpu_node:
            if key in node_labels:
                node_labels[pre_key] = node_labels[key]
            node_labels[key] = gpu_matcher.virtual
        else:
            if pre_key in node_labels:
                node_labels[key] = node_labels.pop(pre_key)
        node.metadata.labels = node_labels
        if replace_node_tag(node.metadata.name, node):
            rg_node.is_vgpu_node = req.is_vgpu_node
    return BaseGenericResponse()


@router.get("/node/gpu/list", response_model=GenericMultipleResponse[GpuStaticInfo],
            summary="查询节点GPU信息",
            dependencies=[Depends(resource_group_sub_user_permission())])
def node_gpu_list(
        request: Request,
        rg_db: ResourceGroupCrud = Depends(get_resource_crud),
        # query参数
        rg_node_id: str = Query(..., description="资源节点id"),
):
    '''
    更新资源模板
    :param request:
    :param query:
    :param rg_db:
    :return:
    '''
    user = request.user
    nodes = rg_db.get_rgn_by_ids_and_user([rg_node_id], user.user_id)
    if not nodes:
        return GenericMultipleResponse[GpuStaticInfo](data=[])
    gpus = GpuStaticInfo.all_by_field("hostname", nodes[0].hostname, session_=rg_db.session)
    return GenericMultipleResponse[GpuStaticInfo](data=gpus)


@router.delete("/template", response_model=BaseGenericResponse,
               summary="删除资源模板",
               dependencies=[Depends(resource_group_sub_user_permission())])
def delete_resource_template(
        request: Request,
        ids: str = Query(1, description="模板id，用逗号分割"),
        rg_db: ResourceGroupCrud = Depends(get_resource_crud)
):
    '''
    删除资源模型
    :param request:
    :param ids:
    :param rg_db:
    :return:
    '''
    id_str_list = ids.split(",")
    id_list = [int(i) for i in id_str_list]
    rg_db.delete_resource_template(id_list, request.user)
    return BaseGenericResponse()


def pkey_bind_guids(ufm_db: UFMInfoCrud, user_id: str, resource_nodes: List[ResourceNode]):
    # get hostnames
    hostnames = [item.hostname for item in resource_nodes]
    guids = ufm_db.get_guids(hostnames)
    # get UFMInfo
    ufm_info = ufm_db.get_ufm_info_by_user(user_id)
    ufm_db.bind_guids_pkey(guids, ufm_info.pkey)


def pkey_unbind_guids(ufm_db: UFMInfoCrud, user_id: str, resource_nodes: List[ResourceNode]):
    # get hostnames
    hostnames = [item.hostname for item in resource_nodes]
    guids = ufm_db.get_guids(hostnames)
    # get UFMInfo
    ufm_info = ufm_db.get_ufm_info_by_user(user_id)
    ufm_db.unbind_guids_pkey(guids, ufm_info.pkey)
