import re
from enum import Enum
from typing import Dict, List, Optional, Tuple, Sequence

from kubernetes.client import V1Node

import app
from app import logger
from app.models.resource_group import ResourceNodeStatus
from app.core.constant import RG_NODE_TAG_KEY
from app.core.prometheus.client import PromethusClient
from app.core.utils import <PERSON><PERSON><PERSON><PERSON>, gpu_manager, to_k8s_rfc_1123, convert_value
from app.models.resource_group import ResourceNode



def cover_to_gb(input_string):
    match = re.match(r'^(\d+)', input_string)
    if match:
        values = match.group(1)
        if input_string[-2:].upper() == "KI":
            return int(values) / 1024 / 1024
        elif input_string[-2:].upper() == "MI":
            return int(values) / 1024
        elif input_string[-2:].upper() == "GI":
            return int(values)
        else:
            return int(values) / 1024 / 1024 / 1024
    else:
        return 0


def get_node_available_resources(nodes: List[V1Node], rg_nodes: Sequence[ResourceNode] = None) -> List[ResourceNodeStatus]:
    """
    获取节点可用资源
    :param node:
    :return:
    """
    if not nodes:
        return []

    nodes_name = [node.metadata.name for node in nodes]
    resource_requests_res = PromethusClient().get_resource_requests_by_nodes(nodes_name)
    nodes_requests = {node["metric"]["node"]: {} for node in resource_requests_res}
    for resource_request in resource_requests_res:
        nodes_requests[resource_request["metric"]["node"]][to_k8s_rfc_1123(resource_request["metric"]["resource"])] = \
            resource_request["value"][1]

    if rg_nodes:
        node_resource_precise = PromethusClient().get_aicp_resource_requests_precise(nodes_name)
        nodes_requests_precise = {node["metric"]["node"]: {} for node in node_resource_precise}
        for resource_request in node_resource_precise:
            nodes_requests_precise[resource_request["metric"]["node"]][to_k8s_rfc_1123(resource_request["metric"]["resource"])] = \
                resource_request["value"][1]

    nodes_status = []
    for node in nodes:
        allocatable = node.status.allocatable
        gpu_type = gpu_manager.get_node_gpu_type(allocatable)
        node_requests = nodes_requests.get(node.metadata.name, {})
        logger.info("requests [%s]", node_requests.get("memory", 0))

        total_cpu = int(allocatable.get("cpu", "0").strip("m"))
        total_memory = int(cover_to_gb(allocatable.get("memory", "0"))) * 1024 * 1024 * 1024
        total_gpu = int(allocatable.get(gpu_type.resource)) if gpu_type else 0
        used_cpu = int(float(node_requests.get("cpu", 0)) * 1000)
        used_memory = round(float(node_requests.get("memory", 0)), 2)
        used_gpu = int(node_requests.get(gpu_type.resource_rfc_1123, 0)) if gpu_type else 0
        #logger.info("memory [%s]", allocatable.get("memory", "0"))
        logger.info(f"total_cpu={total_cpu}, used_cpu={used_cpu}, total_memory={total_memory}, used_memory={used_memory}, total_gpu={total_gpu}, used_gpu={used_gpu}")
        if rg_nodes:
            for rg_node in rg_nodes:
                if rg_node.name == node.metadata.name:
                    node_requests = nodes_requests_precise.get(node.metadata.name, {})
                    total_cpu = rg_node.cpu * 1000
                    total_memory = rg_node.memory * 1024 * 1024 * 1024
                    total_gpu = rg_node.gpu if gpu_type else 0
                    used_cpu = int(float(node_requests.get("cpu", 0)) * 1000)
                    used_memory = round(float(node_requests.get("memory", 0)), 2)
                    used_gpu = int(node_requests.get(gpu_type.resource_rfc_1123, 0)) if gpu_type else 0
                    logger.info(f"resource group node total_cpu={total_cpu}, used_cpu={used_cpu}, total_memory={total_memory}, used_memory={used_memory}, total_gpu={total_gpu}, used_gpu={used_gpu}")

        node_status = ResourceNodeStatus(
            node_id=node.metadata.name,
            total_cpu=total_cpu,
            ava_cpu=total_cpu - used_cpu,
            total_gpu=total_gpu,
            ava_gpu=(total_gpu - used_gpu) if gpu_type else 0,
            total_memory=total_memory,
            ava_memory=total_memory - used_memory,
            total_os_disk=0,
            ava_os_disk=0,
            total_data_disk=0,
            ava_data_disk=0,
            worker_node_type="GPU" if gpu_type else "CPU",
            uid="",
            ib_enabled=0,
            gpu_product=gpu_type.label if gpu_type else "",
            vendor = gpu_type.vendor if gpu_type else "",
        )
        nodes_status.append(node_status)
        logger.debug(f"node_info: {node_status.dict()}")
    return nodes_status


def get_vgpu_available_resources(nodes: List[V1Node]):
    nodes_name = [node.metadata.name for node in nodes]
    vgpu_available_info = PromethusClient().get_vgpu_resource_by_nodes(nodes_name)
    logger.debug(f"vgpu_available_info: {vgpu_available_info}")
    return vgpu_available_info


def cal_vgpu_available_nums(sku_attr, node_available):
    """
    计算可用的 vgpu 数量
    :param requests_mem: unit is GiB
    :param available_mem: unit is Bytes
    :return:
    """
    try:
        available_mem = int(node_available.get("available", 0))
        requests_mem = int(sku_attr["gpu_memory"])
        available_nums = available_mem // (requests_mem * 1000) // (1024 ** 2)
        if "hashrate_allocation" not in sku_attr:
            return available_nums

        available_core = int(node_available.get("core_available", 0))
        requests_core = int(sku_attr["hashrate_allocation"])
        return min(available_nums, available_core // requests_core)
    except Exception as e:
        # 防止boss配置有问题导致的报错
        logger.exception(f"cal_vgpu_available_nums error: {e}")
        return 0


class ProductType(str, Enum):
    resource_group = "resource_group"
    sharing_compute = "sharing_compute"
    container_instance = "container_instance"
    inference_compute = "inference_compute"


def enable_salable_check(aipods_scope: str):
    if not app.settings.ENABLE_SALABLE_CHECK:
        return False
    if aipods_scope == ProductType.sharing_compute:
        return app.settings.ENABLE_SALABLE_CHECK_FOR_TRAIN
    elif aipods_scope == ProductType.container_instance:
        return app.settings.ENABLE_SALABLE_CHECK_FOR_NOTEBOOK
    elif aipods_scope == ProductType.inference_compute:
        return app.settings.ENABLE_SALABLE_CHECK_FOR_INFERENCE
    elif aipods_scope == ProductType.resource_group:
        return app.settings.ENABLE_SALABLE_CHECK_FOR_RESOURCE_GROUP
    else:
        return True


def get_allocatable_nodes(
        sku_attr: Dict, allowable_nodes: List[V1Node], nodes_status: List[ResourceNodeStatus]
) -> List[ResourceNodeStatus]:
    """
    获取可分配的节点
    :param sku_attr:
    :param allowable_nodes:
    :param nodes_status:
    :return:
    """
    sku_aipods_type = sku_attr.get("aipods_type")
    sku_resource_group = sku_attr.get("resource_group")
    sku_resource_group_node = sku_attr.get("resource_group_node")

    nodes_allocatable: List[ResourceNodeStatus] = []
    for node_status in nodes_status:
        for node in allowable_nodes:
            if node.metadata.name != node_status.node_id:
                continue

            node_aipods_type = node.metadata.labels.get("aicp.group/aipods_type")
            node_resource_group = node.metadata.labels.get("aicp.group/resource_group")
            node_resource_group_node = node.metadata.labels.get("aicp.group/resource_group_node")

            if sku_aipods_type and sku_aipods_type == node_aipods_type and not node_resource_group_node:
                nodes_allocatable.append(node_status)
            elif sku_resource_group and sku_resource_group == node_resource_group and node_resource_group_node:
                if sku_resource_group_node and sku_resource_group_node != node_resource_group_node:
                    continue
                nodes_allocatable.append(node_status)
    return nodes_allocatable


def get_cpu_salable(node_allocatable: ResourceNodeStatus, sku_attr: Dict, nodes_allocatable_m: Dict[str, V1Node]) -> tuple[int, str]:
    """"""
    # check cpu schedule
    # disabled
    # sku_cpu_manufacturer = sku_attr.get("cpu_manufacturer")
    # node_cpu_manufacturer = nodes_allocatable_m.get(node_allocatable.node_id).metadata.labels.get("feature.node.kubernetes.io/cpu-model.vendor_id")
    # if sku_cpu_manufacturer and sku_cpu_manufacturer != node_cpu_manufacturer:
    #     return 0, f"[cpu manufacturer] not equal. {node_cpu_manufacturer=}, {sku_cpu_manufacturer=}"
    # sku_cpu_model = sku_attr.get("cpu_model")
    # node_cpu_model = nodes_allocatable_m.get(node_allocatable.node_id).metadata.labels.get("feature.node.kubernetes.io/cpu-model.id")
    # if sku_cpu_model and sku_cpu_model != node_cpu_model:
    #     return 0, f"[cpu model] not equal. {node_cpu_model=}, {sku_cpu_model=}"

    oversold_ratio = app.settings.OVERSOLD_RATIO if sku_attr.get("aipods_scope") in \
                                                    [ProductType.container_instance, ProductType.inference_compute] else 1
    requests_cpu = int(int(sku_attr["cpu_count"]) * 10 // oversold_ratio * 100)
    available_by_cpu_reason = f"[cpu] not enough. [{node_allocatable.ava_cpu=}, {requests_cpu=}, {oversold_ratio=}]"
    if node_allocatable.ava_cpu < requests_cpu:
        logger.info(f"sku_id: {sku_attr['sku_id']} node_id: {node_allocatable.node_id} [cpu] not enough")
        available_by_cpu = 0
    else:
        available_by_cpu = node_allocatable.ava_cpu // requests_cpu
    return available_by_cpu, available_by_cpu_reason


def get_mem_salable(node_allocatable: ResourceNodeStatus, sku_attr: Dict) -> tuple[int, str]:
    """"""
    oversold_ratio = app.settings.OVERSOLD_RATIO_MEM if sku_attr.get("aipods_scope") \
                                                    in [ProductType.container_instance, ProductType.inference_compute] else 1
    requests_mem = int(int(sku_attr["memory"]) * 1024 ** 3 // oversold_ratio)
    available_by_memory_reason = f"{node_allocatable.ava_memory=}, {requests_mem=}, {oversold_ratio=}"
    if node_allocatable.ava_memory < requests_mem:
        logger.info(f"sku_id: {sku_attr['sku_id']} node_id: {node_allocatable.node_id} [memory] not enough")
        available_by_memory = 0
    else:
        # node_allocatable.ava_memory is Ki and sku_attr["memory"] is Gi
        available_by_memory = node_allocatable.ava_memory // requests_mem
    return available_by_memory, available_by_memory_reason


def get_gpu_salable(node_allocatable: ResourceNodeStatus, sku_attr: Dict, nodes_allocatable_m: Dict, nodes_vgpu_status: Dict) -> tuple[
    Optional[int], str]:
    """"""
    gpu: GPUMatcher = gpu_manager.get_gpu_matcher(sku_attr.get("gpu_model", ""))
    logger.info(f"sku_id: {sku_attr['sku_id']} use gpu: {gpu.label if gpu is not None else None}, ")
    available_by_gpu: Optional[int] = None
    available_by_gpu_reason = ""
    requests_gpu = int(sku_attr.get("gpu_count", 0))
    if gpu is not None and requests_gpu:
        # check GPU model
        available_by_gpu_reason = f"{node_allocatable.ava_gpu=}, {requests_gpu=}"
        if nodes_allocatable_m[node_allocatable.node_id].metadata.labels.get(gpu.label) != sku_attr["gpu_model"]:
            logger.debug(f"{node_allocatable.node_id}, {gpu.label}, {nodes_allocatable_m[node_allocatable.node_id].metadata.labels.get(gpu.label)} ,{sku_attr}")
            logger.info(f"sku_id: {sku_attr['sku_id']} node_id: {node_allocatable.node_id} [gpu model] not match")
            available_by_gpu = 0
            available_by_gpu_reason = f"node.gpu.label={nodes_allocatable_m[node_allocatable.node_id].metadata.labels.get(gpu.label)}, {sku_attr['gpu_model']=}"
        elif sku_attr["aipods_type"].upper() == "VGPU" or sku_attr["spec_type"] == "vGPU" or sku_attr["aipods_type"].upper() == "VDCU":
            # 计算当前节点每张卡用于当前sku可申请的个数, 求和等于总可申请个数
            available_by_gpu = sum(
                [
                    cal_vgpu_available_nums(sku_attr, available)
                    for available in nodes_vgpu_status.get(node_allocatable.node_id, {}).values()
                ]
            )
            if not available_by_gpu:
                logger.info(f"sku_id: {sku_attr['sku_id']} node_id: {node_allocatable.node_id} [vgpu] not enough")
                available_by_gpu_reason = f"vgpu not enough"
        elif node_allocatable.ava_gpu >= int(sku_attr["gpu_count"]):
            available_by_gpu = node_allocatable.ava_gpu // int(sku_attr["gpu_count"])
        else:
            available_by_gpu = 0
            logger.info(f"sku_id: {sku_attr['sku_id']} node_id: {node_allocatable.node_id} [gpu count] not enough")
    return available_by_gpu, available_by_gpu_reason


def check_sku_salable(sku: Dict, allowable_nodes: List[V1Node], nodes_status: List[ResourceNodeStatus],
                      nodes_vgpu_status: Dict, resource_used_node=None) -> Tuple[bool, int, List[Dict]]:
    """
    检查sku是否可售
    :param resource_used_node:
    :param nodes_vgpu_status:
    :param nodes_status:
    :param allowable_nodes:
    :param sku:
    :return:
    """
    if resource_used_node is None:
        resource_used_node = dict()

    sku_attr = {x["attr_id"]: x["attr_value"] for x in sku["filters"]}
    sku_attr["sku_id"] = sku["sku_id"]
    sku_attr["spec_type"] = sku.get("spec_type")

    if not enable_salable_check(sku_attr.get("aipods_scope")):
        logger.info("sku_id: %s not enable salable check", sku["sku_id"])
        return True, 9999, []

    nodes_allocatable_m = {node.metadata.name: node for node in allowable_nodes}

    nodes_allocatable: List[ResourceNodeStatus] = get_allocatable_nodes(sku_attr, allowable_nodes, nodes_status)

    # not has aipods_type machine
    if not nodes_allocatable:
        logger.info(f"sku_id: {sku['sku_id']} not has aipods_type [{sku_attr.get('aipods_type')}] machine")
        return False, 0, []

    logger.info(f"sku_id: {sku['sku_id']} nodes_allocatable: {[node.node_id for node in nodes_allocatable]}")

    # check sku is salable
    node_allocatable: ResourceNodeStatus

    # allocatable nums for every  attribute on nodes
    nodes_attribute_salable = []
    max_applicable_number = 0
    for node_allocatable in nodes_allocatable:
        available_by_cpu, available_by_cpu_reason = get_cpu_salable(node_allocatable, sku_attr, nodes_allocatable_m)
        available_by_memory, available_by_memory_reason = get_mem_salable(node_allocatable, sku_attr)
        available_by_gpu, available_by_gpu_reason = get_gpu_salable(node_allocatable, sku_attr, nodes_allocatable_m, nodes_vgpu_status)

        logger.debug(
            f"{node_allocatable.node_id} applicable_number for [{sku['sku_id']}] : "
            f"[available_by_cpu={available_by_cpu}, available_by_memory={available_by_memory}, available_by_gpu={available_by_gpu} ]")

        node_applicable_number = min(filter(lambda x: x is not None, [available_by_cpu, available_by_memory, available_by_gpu]))
        available_by_resource_group_reason = ""
        if sku_attr["aipods_scope"] == "resource_group" and node_applicable_number:
            if node_allocatable.node_id in resource_used_node:
                node_applicable_number = 0
                available_by_resource_group_reason = f"node {node_allocatable.node_id} has been used"
            else:
                node_applicable_number = 1

        sku_spec_type = sku.get("spec_type")
        if node_allocatable.vendor == "VGPU":
            if sku_spec_type != None and sku_spec_type == "common" :
                logger.info(f"the node {node_allocatable} is vGPU mode, but the sku({sku_spec_type}) is GPU template.")
                continue
        else:
            if sku_spec_type != None and sku_spec_type == "vGPU" :
                logger.info(f"the node {node_allocatable} is not vGPU mode, but the sku({sku_spec_type}) is vGPU template.")
                continue

        #if sku.get("spec_type") == "only_cpu" and node_allocatable.vendor != "":
        #    continue
        #if sku.get("spec_type") == "common" and node_allocatable.vendor != "NVIDIA" and node_allocatable.vendor != "HYGON" and node_allocatable.vendor != "ASCEND" and node_allocatable.vendor != "HEXAFLAKE":
        #    continue
        #if sku.get("spec_type") == "vGPU" and node_allocatable.vendor != "VGPU":
        #    continue

        nodes_attribute_salable.append(
            {
                "hostname": node_allocatable.node_id,
                "applicable_number": node_applicable_number,
                "available_by_cpu": available_by_cpu,
                "available_by_memory": available_by_memory,
                "available_by_gpu": available_by_gpu,
                "available_by_cpu_reason": available_by_cpu_reason,
                "available_by_memory_reason": available_by_memory_reason,
                "available_by_gpu_reason": available_by_gpu_reason,
                "available_by_resource_group_reason": available_by_resource_group_reason,
            }
        )

        max_applicable_number += node_applicable_number

    # all nodes not satisfy
    return bool(max_applicable_number), max_applicable_number, nodes_attribute_salable
