from app.core.exceptions import AICPBaseException


class ResourceGroupNotFoundException(AICPBaseException):
    BASE_CODE = 3404
    BASE_MESSAGE = "资源组不存在"


class ResourceGroupNodeNotFoundException(AICPBaseException):
    BASE_CODE = 3404
    BASE_MESSAGE = "资源组下无可用节点"

class ResourceGroupNodeSuspended(AICPBaseException):
    BASE_CODE = 3405
    BASE_MESSAGE = "资源组节点已欠费，无法继续创建资源"


class ResourceNodeQuotaException(AICPBaseException):
    BASE_CODE = 3406
    BASE_MESSAGE = "配额不足，请联系管理员"


class NotGpuReuseNodeException(AICPBaseException):
    BASE_CODE = 3407
    BASE_MESSAGE = "非支持重用GPU节点"


class ResourceHasTemplateException(AICPBaseException):
    BASE_CODE = 3408
    BASE_MESSAGE = "资源组存在相关模板, 请先删除模板后再删除资源组"


class ResourceNodeHasTemplateException(AICPBaseException):
    BASE_CODE = 3409
    BASE_MESSAGE = "资源节点存在相关模板, 请先删除模板后再删除资源节点"


class ResourceNodeHasGpuTemplateException(AICPBaseException):
    BASE_CODE = 3410
    BASE_MESSAGE = "资源节点存在GPU相关模板, 请先删除模板后再调整资源节点"



class ResourceGroupNodeNotSupportVgpuException(AICPBaseException):
    BASE_CODE = 3411
    BASE_MESSAGE = "该资源节点不支持虚拟GPU"


class ResourceGroupNodeNotSupportReuseGpuException(AICPBaseException):
    BASE_CODE = 3412
    BASE_MESSAGE = "该资源节点不支持GPU复用"


class SubUserHasNotResourceGroupPermissions(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "子账号没有资源组功能权限"

class ResourceNodeHasResourcerRunning(AICPBaseException):
    BASE_CODE = 3413
    BASE_MESSAGE = "资源节点存在运行的资源, 请先关机或暂停, 再次启动时需要更改规格"
