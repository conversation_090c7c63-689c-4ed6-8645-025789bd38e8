import os

import app
from app.apps.trains.kr8s_objects import TRAINS_ENDPOINTS_MAP
from app.apps.trains.kr8s_objects.basejob import BaseJob
from app.core.utils import get_hash


def get_api_object(endpoint) -> BaseJob:
    """
    获取作业的APIObject
    :param endpoint:
    :return:
    """
    if endpoint not in TRAINS_ENDPOINTS_MAP:
        raise ValueError(f"endpoint {endpoint} not supported, just support {TRAINS_ENDPOINTS_MAP.keys()}")

    return TRAINS_ENDPOINTS_MAP[endpoint]


def create_tensorboard_log_path(namespace: str, user_id: str, train_id: str) -> str:
    """
    获取用户的tensorboard pvc
    用于用户自定义的 tensorboard logs 输出
    :return: path
    """
    if not app.settings.TENSORBOARD_HOME:
        return ""

    if namespace and user_id and train_id:
        log_path = os.path.join(app.settings.TENSORBOARD_HOME, get_hash(namespace), get_hash(user_id), get_hash(train_id))
        os.makedirs(log_path, exist_ok=True)
        return log_path

    raise ValueError("namespace, user_id, train_uuid must be provided")



