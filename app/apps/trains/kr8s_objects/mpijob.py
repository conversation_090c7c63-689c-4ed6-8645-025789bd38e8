"""
M<PERSON><PERSON><PERSON> is the definition of a mpijob.
"""
from copy import deepcopy
from typing import Dict

from app.core.models import QingcloudUser
from app.models.trains import Train
from .basejob import BaseJob


# noinspection PyClassHasNoInit
class MpiJob(BaseJob):
    """Mpi<PERSON>ob is the definition of a mpijob.
    """

    version = "kubeflow.org/v1"
    endpoint = "mpijobs"
    kind = "MPIJob"
    plural = "mpijobs"
    singular = "mpijob"
    namespaced = True

    show_name = "Mpi"

    replica_specs_name = "mpiReplicaSpecs"
    replica_types = ["Launcher", "Worker"]

    @classmethod
    def get_resource_definition(cls, train: Train, user: QingcloudUser, docker_secret: str = None) -> dict:
        """add mpi"""
        resource_definition: Dict = super().get_resource_definition(train, user, docker_secret)
        # set a pod as Launcher
        # 将一个节点身份设置成master
        resource_definition["spec"][cls.replica_specs_name]["Launcher"] = \
            deepcopy(resource_definition["spec"][cls.replica_specs_name]["Worker"])
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["replicas"] = 1
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["spec"]["containers"][0][
            "name"] = "launcher"
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["spec"]["containers"][0][
            "resources"] = {
            "requests": {
                "cpu": "1",
                "memory": "1Gi"
            },
            "limits": {
                "cpu": "1",
                "memory": "1Gi"
            }
        }
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["spec"]["containers"][0]["command"] \
            = ["/bin/sh", "-c", "sleep 10;%s" % train.command]
        # mpijob remove ib annotations
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["metadata"]["annotations"].pop(
            "k8s.v1.cni.cncf.io/networks", None
        )

        # Launcher will run common on per worker, not need set command and args
        need_del_keys = ["command", "args"]
        for need_del_key in need_del_keys:
            if need_del_key in \
                    resource_definition["spec"][cls.replica_specs_name]["Worker"]["template"]["spec"]["containers"][0]:
                del resource_definition["spec"][cls.replica_specs_name][
                    "Worker"]["template"]["spec"]["containers"][0][need_del_key]

        resource_definition["spec"][cls.replica_specs_name]["Worker"]["template"]["spec"]["containers"][0][
            "name"] = "worker"

        # set slotsPerWorker is gpu number
        resource_definition["spec"]["slotsPerWorker"] = train.replica_specs[0].custom_gpu \
            if train.replica_specs[0].custom_gpu else train.replica_specs[0].custom_cpu

        # mpijob remove ib annotations
        # resource_definition["spec"][cls.replica_specs_name]["Worker"]["template"]["metadata"]["annotations"].pop(
        #     "k8s.v1.cni.cncf.io/networks", None
        # )

        return resource_definition
