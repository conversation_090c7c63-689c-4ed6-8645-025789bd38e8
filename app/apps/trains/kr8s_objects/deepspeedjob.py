"""
DeepspeedJob is the definition of a deepspeedjob.
"""
from copy import deepcopy
from typing import Dict

from app.core.models import QingcloudUser
from app.models.trains import Train
from .basejob import BaseJob


# noinspection PyClassHasNoInit
class DeepspeedJob(BaseJob):
    """deepspeedJob is the definition of a deepspeedjob.
    """

    version = "kubeflow.org/v1"
    endpoint = "deepspeedjobs"
    kind = "DeepspeedJob"
    plural = "deepspeedjobs"
    singular = "deepspeedjob"
    namespaced = True

    show_name = "Deepspeed"

    replica_specs_name = "deepspeedReplicaSpecs"
    replica_types = ["Launcher", "Worker"]

    @classmethod
    def get_resource_definition(cls, train: Train, user: QingcloudUser, docker_secret: str = None) -> dict:
        """add deepspeed"""
        resource_definition: Dict = super().get_resource_definition(train, user, docker_secret)
        # set a pod as Launcher
        # 将一个节点身份设置成master
        resource_definition["spec"][cls.replica_specs_name]["Launcher"] = \
            deepcopy(resource_definition["spec"][cls.replica_specs_name]["Worker"])
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["metadata"]["annotations"].pop(
            "k8s.v1.cni.cncf.io/networks", None
        )
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["replicas"] = 1
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["spec"]["containers"][0]["name"] \
            = "launcher"
        resource_definition["spec"][cls.replica_specs_name]["Launcher"]["template"]["spec"]["containers"][0]["command"]\
            = ["/bin/sh", "-c", "echo StrictModes no >> /etc/ssh/sshd_config;service ssh start;%s" % train.command]
        # worker -1
        if resource_definition["spec"]["deepspeedReplicaSpecs"]["Worker"]["replicas"] >= 1:
            # 将一个节点身份设置成master
            resource_definition["spec"]["deepspeedReplicaSpecs"]["Worker"]["replicas"] -= 1
            if resource_definition["spec"]["deepspeedReplicaSpecs"]["Worker"]["replicas"] == 0:
                # 单节点只使用launcher
                del resource_definition["spec"]["deepspeedReplicaSpecs"]["Worker"]
            else:
                need_del_keys = ["command", "args"]
                for need_del_key in need_del_keys:
                    if need_del_key in \
                            resource_definition["spec"][cls.replica_specs_name]["Worker"]["template"]["spec"]["containers"][0]:
                        del resource_definition["spec"][cls.replica_specs_name][
                            "Worker"]["template"]["spec"]["containers"][0][need_del_key]

                resource_definition["spec"][cls.replica_specs_name]["Worker"]["template"]["spec"]["containers"][0][
                    "name"] = "worker"

        # set slotsPerWorker is gpu number
        resource_definition["spec"]["slotsPerWorker"] = train.replica_specs[0].custom_gpu

        return resource_definition
