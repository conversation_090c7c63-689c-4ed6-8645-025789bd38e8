"""
PyTorchJob is the definition of a PyTorchJob.
"""
from copy import deepcopy
from typing import Dict

import app
from app.core.models import QingcloudUser
from .basejob import BaseJob
from app.models.trains import Train


# noinspection PyClassHasNoInit
class PyTorchJob(BaseJob):
    """PyTorchJob is the definition of a PyTorchJob.
    """

    version = "kubeflow.org/v1"
    endpoint = "pytorchjobs"
    kind = "PyTorchJob"
    plural = "pytorchjobs"
    singular = "pytorchjob"
    namespaced = True

    show_name = "PyTorch"

    replica_specs_name = "pytorchReplicaSpecs"
    replica_types = ["Master", "Worker"]
    replica_containers_name = "pytorch"

    @classmethod
    def get_resource_definition(cls, train: Train, user: QingcloudUser, docker_secret: str = None) -> dict:
        resource_definition: Dict = super().get_resource_definition(train, user, docker_secret)
        resource_definition["spec"]["nprocPerNode"] = "auto"

        # set a worker to master
        if resource_definition["spec"]["pytorchReplicaSpecs"]["Worker"]["replicas"] >= 1:
            # 将一个节点身份设置成master
            resource_definition["spec"]["pytorchReplicaSpecs"]["Master"] = \
                deepcopy(resource_definition["spec"]["pytorchReplicaSpecs"]["Worker"])
            resource_definition["spec"]["pytorchReplicaSpecs"]["Master"]["replicas"] = 1
            resource_definition["spec"]["pytorchReplicaSpecs"]["Worker"]["replicas"] -= 1
            if resource_definition["spec"]["pytorchReplicaSpecs"]["Worker"]["replicas"] == 0:
                # 单节点使用MASTER身份运行
                del resource_definition["spec"]["pytorchReplicaSpecs"]["Worker"]

        return resource_definition
