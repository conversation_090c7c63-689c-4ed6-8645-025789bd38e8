from .deepspeedjob import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .tfjob import <PERSON><PERSON><PERSON><PERSON>
from .mxjob import <PERSON><PERSON><PERSON><PERSON>
from .pytorchjob import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .mpijob import <PERSON><PERSON><PERSON><PERSON>
from .xgboostjob import <PERSON><PERSON><PERSON>t<PERSON>ob
from .paddlejob import <PERSON><PERSON><PERSON>ob
from .basejob import BaseJob

ALL_TRAINS = [T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>]

ALL_ENDPOINTS = [i.endpoint for i in ALL_TRAINS]

ALL_ENDPOINTS_SHOW_NAME = [{"endpoint": i.endpoint, "name": i.show_name} for i in ALL_TRAINS]

TRAINS_ENDPOINTS_MAP = {i.endpoint: i for i in ALL_TRAINS}

__all__ = ["TfJob", "MXJ<PERSON>", "<PERSON><PERSON><PERSON>or<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>t<PERSON><PERSON>", "<PERSON>ddleJ<PERSON>", "ALL_TRAINS", "TRAINS_ENDPOINTS_MAP",
           "ALL_ENDPOINTS",  "<PERSON><PERSON><PERSON>", "ALL_ENDPOINTS_SHOW_NAME", "<PERSON><PERSON><PERSON>J<PERSON>"]
