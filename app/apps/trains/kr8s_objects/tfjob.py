"""
TfJob is the definition of a tfjob.
"""
from kr8s.objects import APIObject
from .basejob import BaseJob


# noinspection PyClassHasNoInit
class TfJob(BaseJob):
    """TfJob is the definition of a tfjob.
    """

    version = "kubeflow.org/v1"
    endpoint = "tfjobs"
    kind = "TFJob"
    plural = "tfjobs"
    singular = "tfjob"
    namespaced = True

    show_name = "TensorFlow"

    replica_specs_name = "tfReplicaSpecs"
    replica_types = ["Worker", "PS", "Chief", "Evalator"]
    replica_containers_name = "tensorflow"

