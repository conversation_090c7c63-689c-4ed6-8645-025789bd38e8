from datetime import datetime

from app import logger
from app.apps.trains.query import TrainSearchQuaryParams
from app.core.db import get_db_context_session
from app.core.utils import get_tmp_file_url, log_background_task_exception
from app.cruds.task import TaskService
from app.cruds.trains import TrainCRUD
from app.models.trains import ExportRecord, ExportRecordStatusEnum


@log_background_task_exception
def export_train_list_in_back_ground_task(task: TaskService, user, namespace: str, query: TrainSearchQuaryParams):
    """
    Export train list in background task
    :param task:
    :param user:
    :param namespace:
    :param query:
    """
    record = ExportRecord(
        task_id=task.task_info.id, user_id=user.user_id, root_user_id=user.root_user_id, download_url="",
        status=ExportRecordStatusEnum.Running, user_name=user.user_name
    )
    record.save()
    try:
        with get_db_context_session() as session:
            train_crud = TrainCRUD(session, user)
            count, train_list = train_crud.search(namespace, query)
            task.update_progress(10)
            name = train_crud.save_trains_in_back_ground_task(train_list, task)
            url = get_tmp_file_url(name, f'trains-{datetime.now().strftime("%Y%m%d%H%M%S")}.csv')
            task.finish(
                result={"url": url}
            )
            record.update(status=ExportRecordStatusEnum.Succeeded, download_url=url)
    except Exception as e:
        logger.exception(f"Export train list failed: {str(e)}")
        task.finish(status="Failed", result={"error": str(e)})
        record.update(status=ExportRecordStatusEnum.Failed)

