from datetime import datetime
from typing import List, Optional, Union

from fastapi import Query

from app.core.models import CommonSearchQueryParams


class TrainSearchQuaryParams(CommonSearchQueryParams):
    def __init__(self, name: str = None, image_name: str = None, reverse: bool = True,
                 offset: int = 0, limit: int = 10,
                 order_by: str = Query(default="created_at", description="运行状态"),
                 status: Optional[List[str]] = Query(default=None, description="运行状态"),
                 endpoints: Optional[List[str]] = Query(default=None, description="作业类型"),
                 start_at: Optional[datetime] = Query(default=None, description="开始时间"),
                 end_at: Optional[datetime] = Query(default=None, description="结束时间"),
                 owner: Optional[str] = Query(default=None, description="作业所有者"),
                 job_type: Optional[List[str]] = Query(default=["AI"], description="AI or HPC or FT"),
                 project_category: Optional[List[str]] = Query(default=[], description="项目分类"),
                 ):
        super().__init__(offset, limit)
        self.name = name
        self.offset = offset
        self.limit = limit
        if status and "Paused" in status:
            status.remove("Paused")
            status.append("Suspended")
        self.status = status
        self.order_by = order_by
        self.image_name = image_name
        self.reverse = reverse
        self.endpoints = endpoints
        self.start_at = start_at
        self.end_at = end_at
        self.owner = owner
        self.project_category = project_category
        self.job_type = job_type

    def __str__(self):
        return f"TrainSearchQuaryParams(name={self.name}, image_name={self.image_name}, reverse={self.reverse}, " \
               f"offset={self.offset}, limit={self.limit}, order_by={self.order_by}, status={self.status}, " \
               f"endpoints={self.endpoints}, start_at={self.start_at}, end_at={self.end_at}, owner={self.owner}, " \
               f"project_category={self.project_category}, job_type={self.job_type})"


class TrainPodMonitoringQueryParams:
    """
    Pod监控查询参数
    """

    def __init__(self, namespace: str, resource_filer: List[str]):
        self.namespace = namespace
        self.resource_filer = resource_filer
        self.metrics_filter = []
        self.start_at = None
        self.end_at = None


class TrainLogQueryParams:

    def __init__(
            self,
            train_uuid: str = Query(..., description="作业的uuid"),
            query: str = Query(None, description="查询关键字"),
            pods_name: Optional[List[str]] = Query(None, description="pod名称"),
            start_time: Union[int, datetime] = Query(None, description="开始时间"),
            end_time: Union[int, datetime] = Query(None, description="结束时间"),
            container_name: str = Query(None),
            size: int = Query(100, description="返回的日志条数"),
            page: int = Query(1, description="页码"),
            reverse: bool = Query(True, description="是否按照时间倒叙排列, 默认以时间倒叙排列, False为正序排列"),
            fuzzy: bool = Query(True, description="是否模糊查询, 默认模糊查询, False为精确查询")
    ):
        self.train_uuid = train_uuid
        self.query = query
        self.pods_name = pods_name
        self.start_time = start_time
        self.end_time = end_time
        self.container_name = container_name
        self.size = size
        self.page = page
        self.reverse = reverse
        self.fuzzy = fuzzy
        self.namespace = None

class DownloadExportQueryParams:
    def __init__(
            self,
            start_time: datetime = Query(None, description="开始时间"),
            end_time: datetime = Query(None, description="结束时间"),
            status: Optional[List[str]] = Query(None, description="状态"),
            owner: Optional[str] = Query(None, description="作业所有者, 仅对超管用户查询有效"),
            page: int = Query(1, description="页码"),
            size: int = Query(100, description="返回的日志条数"),
    ):
        self.start_time = start_time
        self.end_time = end_time
        self.status = status
        self.owner = owner
        self.page = page
        self.size = size
