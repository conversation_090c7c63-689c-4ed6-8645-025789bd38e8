import io
import os
import uuid as uuid_pkg
from datetime import datetime
from statistics import mean
from typing import Dict, List

import kr8s
from fastapi import APIRouter, BackgroundTasks, Depends, Query, Request, status as http_status
from kr8s import NotFoundError
from kr8s.objects import Pod, Service

from app.depends import has_permission
from app.core.opensearch.client import OpenSearchClient, es_client
from app.core.response import BaseGenericResponse, GenericMultipleResponse, GenericSingleResponse
from app.cruds.trains import TrainCRUD
from .background_task import export_train_list_in_back_ground_task
from .exceptions import TrainPriorityValueException
from .dependencies import get_train_crud, has_train_resource_permission_namespace
from .exceptions import TrainNotFound, TrainQuotaException
from .kr8s_objects import ALL_ENDPOINTS_SHOW_NAME, BaseJob, TRAINS_ENDPOINTS_MAP
from app.models.trains import ExportRecord, LogsModel, Train, TrainCreate, TrainPatch, TrainPortParam, TrainRead
from .opensearch import TrainOpenSearch
from .query import DownloadExportQueryParams, TrainLogQueryParams, TrainSearchQuaryParams
from app.cruds.user import UserCRUD
from ..user.dependencies import get_user_crud
from ... import logger
from ...core.global_server.exceptions import UserHasNotPermissionException
from ...core.kube.api.kfam import KfamClient
from ...core.kube.models.kfam import WorkGroupInfo, Namespace
from ...core.models import TaskInfo
import app
from ...core.models import TaskInfo, AicpServers
from ...core.prometheus.monitoring_operator import MonitoringOperator, train_pod_metrics
from ...core.prometheus.query_params import MonitoringQueryParams
from ...core.qingcloud.interface import patch_user_info_for_models
from ...core.qingcloud.resource import ProductCenterResource
from ...core.utils import get_tmp_file_url
from ...cruds.finetuning import FinetuningCrud
from ...cruds.task import TaskService
from ...depends.authz import PermissionLevel
from ...depends.tr_group import train_has_permission, train_namespace_permission

router = APIRouter(prefix="/trains", tags=["trains"])


@router.get("/users",
            summary="获取可以查看的用户列表",
            response_model=GenericSingleResponse[WorkGroupInfo])
def get_users(
        request: Request,
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取各个账号可以看到的用户列表
    """
    # 获取主账号的所有绑定信息
    kfam_client: KfamClient = KfamClient(request.user.root_user_id)
    workgroup: WorkGroupInfo = kfam_client.get_all_profile(user_db)
    # 主账号返回所有的绑定信息
    if not request.user.is_subuser():
        return GenericSingleResponse[WorkGroupInfo](data=workgroup)
    # 子账号根据权限返回
    permission = next(item["permission"] for item in request.user.permissions if item["module"] == "TN")
    if permission == "NO":
        raise UserHasNotPermissionException()
    if permission == "OWN":
        for item in workgroup.namespaces:
            if item.namespace_user_id == request.user.user_id:
                ns_data = Namespace(
                    user=item.user,
                    namespace=item.namespace,
                    namespace_user_id=item.namespace_user_id,
                    role=item.role,
                    email=item.email
                )
                res_data = WorkGroupInfo(
                    user=request.user.user_id,
                    namespaces=[ns_data],
                    isClusterAdmin=workgroup.is_cluster_admin
                )
                return GenericSingleResponse[WorkGroupInfo](data=res_data)
    if permission in ["READALL", "ALL"]:
        return GenericSingleResponse[WorkGroupInfo](data=workgroup)


@router.get("/inqueuing",
            summary="获取是否开启排队功能",
            response_model=GenericSingleResponse[bool])
def get_inqueuing_status():
    return GenericSingleResponse[bool](data=app.settings.VOLCANO_ENABLE)


@router.get("/endpoints",
            summary="获取所有的endpoint(训练类型)",
            response_model=GenericSingleResponse[List[Dict]])
def get_endpoints():
    """
    获取所有的作业类型
    """
    endpoint_filter = ["tfjobs", "mpijobs", "pytorchjobs", "deepspeedjobs"]
    endpoints = list(filter(lambda x: x["endpoint"] in endpoint_filter, ALL_ENDPOINTS_SHOW_NAME))
    return GenericSingleResponse[List[Dict]](data=endpoints)


@router.post("/namespaces/{namespace}/create", response_model=GenericSingleResponse[Train],
             summary="创建训练作业",
             dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE))])
def create_train(
        data: TrainCreate,
        request: Request,
        background_tasks: BackgroundTasks,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud),
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    创建训练作业
    """
    # Quota verification
    # 查询规格卡数
    data_number = 1
    replicas = data.replica_specs[0].replicas
    data_number = data_number * replicas
    user_db.quota_verification(request.user.user_id, "tn", data_number,  data.replica_specs[0].specs)

    data.namespace = namespace
    data.user_id = request.user.user_id
    data.root_user_id = request.user.root_user_id
    # Get user's priority.
    data.priority = user_db.get_user_info_by_user_id(request.user.user_id).priority
    data.project_category_key = user_db.get_project_category_key_by_name(data.project_category)

    train = train_session.create(data)
    return GenericSingleResponse[Train](data=train)


@router.get(
    "/namespaces/{namespace}/trains",
    response_model=GenericMultipleResponse[TrainRead],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业列表",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_trains_list(
        request: Request,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        query: TrainSearchQuaryParams = Depends(TrainSearchQuaryParams),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    获取所有的作业
    """
    count, trains = train_session.search(namespace, query)
    for train in trains:
        train.running_time = train.get_running_time()
        if train.status == "Suspended" and train.reason == "欠费暂停":
            train.status = "Paused"
        if "FT" in train.job_type:
            train.finetuning = FinetuningCrud(session=train_session.session, user=request.user).get(train.uuid)
    return GenericMultipleResponse[TrainRead](data=trains, counts=count)


@router.get(
    "/namespaces/{namespace}/export",
    response_model=GenericSingleResponse[str],
    status_code=http_status.HTTP_200_OK,
    summary="导出训练作业列表",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))],
    deprecated=True
)
def export_trains_list(
        request: Request,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        query: TrainSearchQuaryParams = Depends(TrainSearchQuaryParams),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    获取所有的作业
    """
    # limit to 100000
    query.offset = 0
    query.limit = 100000
    count, trains = train_session.search(namespace, query)
    name = train_session.save_trains(trains)

    # url = AdminMinio().put_object_and_presigned_get(os.path.basename(trains_path), trains_path)
    return GenericSingleResponse[str](
        data=get_tmp_file_url(name, f'trains-{datetime.now().strftime("%Y%m%d%H%M%S")}.csv'))


@router.get(
    "/namespaces/{namespace}/list/task",
    response_model=GenericSingleResponse[TaskInfo],
    status_code=http_status.HTTP_200_OK,
    summary="[异步]导出训练作业列表",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))],
    deprecated=True
)
def export_trains_list_by_background_tasks(
        request: Request,
        background_tasks: BackgroundTasks,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        query: TrainSearchQuaryParams = Depends(TrainSearchQuaryParams)
):
    """
    获取所有的作业
    """
    # limit to 100000
    query.offset = 0
    query.limit = 100000
    task = TaskService.create("ExportTrainList", str(query))
    background_tasks.add_task(export_train_list_in_back_ground_task, task, request.user, namespace, query)
    return GenericSingleResponse[TaskInfo](data=task.task_info)


@router.get(
    "/namespaces/{namespace}/list/task/{task_id}",
    response_model=GenericSingleResponse[TaskInfo],
    status_code=http_status.HTTP_200_OK,
    summary="获取导出训练作业列表的任务状态",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_export_trains_list_by_background_tasks_info(
        task_id: str,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
):
    """
    获取所有的作业
    """
    # limit to 100000
    task = TaskService.get(task_id)
    return GenericSingleResponse[TaskInfo](data=task.task_info)

@router.get(
    "/namespaces/{namespace}/list/tasks",
    response_model=GenericMultipleResponse[ExportRecord],
    status_code=http_status.HTTP_200_OK,
    summary="获取导出训练作业的的所有任务列表",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_export_trains_list(
        request: Request,
        query: DownloadExportQueryParams = Depends(DownloadExportQueryParams),
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        curd: TrainCRUD = Depends(get_train_crud)
):
    """
    获取作业的导出列表
    """
    # limit to 100000
    tasks, count = curd.get_tasks(request.user, query)
    return GenericMultipleResponse[ExportRecord](data=tasks, counts=count)


@router.get(
    "/namespaces/{namespace}/trains/metrics",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业的监控指标",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_train_resource_permission_namespace("resource_ids"))]
)
def get_trains_metrics(
        request: Request,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        resource_ids: List[str] = Query(default=[], description="资源ID列表"),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    获取所有的作业
    """
    trains: List[Train] = Train.all_by_ids(resource_ids, session_=train_session.session)
    if not trains:
        return GenericMultipleResponse[Dict](data=[], counts=0)

    pods = es_client.get_pods_by_lables_app(resource_ids)
    if not pods:
        return GenericMultipleResponse[Dict](data=[], counts=0)

    resource_filer = [x["metadata"]["name"] for x in pods]
    metrics_options = MonitoringQueryParams(resource_filer=resource_filer, namespace=namespace)
    if trains[0].ended_at:
        metrics_options.step = "5m"
        metrics_options.start_at = trains[0].created_at.timestamp()
        metrics_options.end_at = trains[0].ended_at.timestamp()
    metrics_options.gpu_vendor = trains[0].get_gpu_vendor()
    prometheus_res = MonitoringOperator().get_named_metrics(train_pod_metrics, metrics_options)

    if not trains[0].ended_at:
        for metrics in filter(lambda x: x["result"], prometheus_res):
            values = [float(item['value'][1]) for item in metrics['result']]
            metrics["avg_value"] = round(mean(values), 3)
            metrics["max_value"] = round(max(values), 3)
            metrics["min_value"] = round(min(values), 3)
            metrics["sum_value"] = round(sum(values), 3)
    else:
        for metrics in filter(lambda x: x["result"], prometheus_res):
            metrics["avg_value"] = round(mean([x["avg_value"] for x in metrics["result"]]), 3)
            metrics["max_value"] = round(max([x["max_value"] for x in metrics["result"]]), 3)
            metrics["min_value"] = round(min([x["min_value"] for x in metrics["result"]]), 3)
            metrics["sum_value"] = round(sum([x["current_value"] for x in metrics["result"]]), 3)

    return GenericMultipleResponse[Dict](data=prometheus_res, counts=0)


@router.patch(
    "/namespaces/{namespace}/endpoints/{endpoints}/trains/{uuid}/priority/{priority}",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="修改训练作业的优先级",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def update_train_priority(
        uuid: str,
        priority: int,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    if not (1 <= priority <= 10):
        raise TrainPriorityValueException(priority)
    train_session.update_train_priority(uuid, priority)
    return BaseGenericResponse()


@router.delete(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="删除训练作业",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def delete_train(
        uuid: str,
        endpoint: str,
        request: Request,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    获取所有的作业
    """
    train_session.delete(uuid)
    return BaseGenericResponse()


@router.patch(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="修改训练作业",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def patch_train(
        data: TrainPatch,
        request: Request,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    修改训练作业
    """
    train = train_session.patch_train(data)
    return BaseGenericResponse()


@router.post(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}/stop",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="暂停训练作业",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def stop_train(
        request: Request,
        endpoint: str,
        uuid: str,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    暂停训练作业
    """
    train_session.suspended(uuid)
    return BaseGenericResponse()


@router.post(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}/start",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="启动训练作业",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def restart_train(
        request: Request,
        endpoint: str,
        uuid: str,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    启动训练作业
    """
    # Quota verification
    train_session.restart(uuid)
    return BaseGenericResponse()


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/train",
    response_model=GenericSingleResponse[TrainRead],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业详情",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def get_train(
        endpoint: str,
        uuid: str,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    获取训练作业详情
    :return:
    """
    train: TrainRead = TrainRead.from_orm(Train.one_by_id(uuid, session_=train_session.session))
    train_object = OpenSearchClient().get_by_uid(str(train.kuid))
    train.train_k8s_objects = train_object
    train.running_time = train.get_running_time()
    if train.reason == "Unschedulable":
        if not train_object["status"]["conditions"]:
            train_object["status"]["conditions"] = []
        train_object["status"]["conditions"].append(
            {"type": "Unschedulable", "status": "True", "lastTransitionTime": train.updated_at,
             "lastUpdateTime": train.updated_at, "reason": "Unschedulable",
             "message": f"PyTorchJob {train.uuid} is Unschedulable"}
        )
    if train.job_type == "FT":
        train.finetuning = FinetuningCrud(session=train_session.session).get(train.uuid)
    return GenericSingleResponse[TrainRead](data=train)


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{name}",
    response_model=GenericSingleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业k8s对象",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_train_k8s_object(endpoint: str, name: str,
                         namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取单个作业的k8s对象, 仅限于未终止的作业
    :return:
    """
    r = kr8s.get(endpoint, name, namespace=namespace)
    if not r:
        raise TrainNotFound(name)
    return GenericSingleResponse[Dict](data=r[0].raw)


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{train_name}/pods",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业的pod",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_trains_pods(endpoint: str, train_name: str,
                    namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取作业的pod, 仅限于未终止的作业
    """
    r = Pod.list(namespace=namespace, label_selector={"training.kubeflow.org/job-name": train_name})
    # r = kr8s.get("pods", namespace=namespace,
    #              label_selector={"training.kubeflow.org/job-name": train_name})
    pods_raw = [i.raw for i in r]
    return GenericMultipleResponse[Dict](data=pods_raw, counts=len(pods_raw))


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/logs",
    response_model=GenericMultipleResponse[LogsModel],
    response_description="返回的是一个list, 每个元素是一个dict, {@timestamp: str, message: str, stream: str}}",
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业的log",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_train_resource_permission_namespace("train_uuid"))]
)
def get_trains_pod_log(
        endpoint: str,
        query: TrainLogQueryParams = Depends(TrainLogQueryParams),
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    获取作业的log, 仅限于未终止的作业
    """
    query.namespace = namespace
    logs, count = TrainOpenSearch().search_train_log(query)
    return GenericMultipleResponse[LogsModel](data=logs, counts=count)


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/download_logs",
    response_model=GenericSingleResponse[str],
    response_description="返回的是一个list, 每个元素是一个dict, {@timestamp: str, message: str, stream: str}}",
    status_code=http_status.HTTP_200_OK,
    summary="下载训练作业的log",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_train_resource_permission_namespace("train_uuid"))]
)
def download_trains_pod_log(
        endpoint: str,
        query: TrainLogQueryParams = Depends(TrainLogQueryParams),
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
):
    """
    获取作业的log, 仅限于未终止的作业
    """
    query.namespace = namespace
    name = TrainOpenSearch().save_train_log(query)
    return GenericSingleResponse[str](data=get_tmp_file_url(name, f'{datetime.now().strftime("%Y%m%d%H%M%S")}.log'))


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{train_name}/events",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业的events",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_trains_events(endpoint: str, train_name: str,
                      namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取作业的events, 仅限于未终止的作业
    """
    field_selector = {"involvedObject.name": train_name, "involvedObject.kind": TRAINS_ENDPOINTS_MAP[endpoint].kind}
    r = kr8s.get("events", namespace=namespace, field_selector=field_selector)
    return GenericMultipleResponse[Dict](data=[i.raw for i in r], counts=len(r))


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{train_name}/pod/{pod_name}/events",
    response_model=GenericMultipleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业对应POD的events",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))]
)
def get_trains_pod_events(endpoint: str, train_name: str, pod_name: str,
                          namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取作业对应POD的events, 仅限于未终止的作业
    """
    field_selector = {"involvedObject.name": pod_name, "involvedObject.kind": "Pod"}
    r = kr8s.objects.Event.list(namespace=namespace, field_selector=field_selector)
    return GenericMultipleResponse[Dict](data=[i.raw for i in r], counts=len(r))


@router.post(
    "/namespaces/{namespace}/code/path/create",
    response_model=GenericSingleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    summary="创建一个代码临时上传路径, 用于当前训练上传代码.",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE))],
    deprecated=True,
)
def create_code_path(
        request: Request,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.WRITE)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    """
    创建一个代码临时上传路径, 用于当前训练上传代码.
    :param request:
    :param namespace:
    :param train_session:
    :return:
    """
    return GenericSingleResponse[str](data={"uuid": ""})


@router.get(
    "/namespaces/{namespace}/code/presigned_put_url",
    response_model=GenericSingleResponse[str],
    status_code=http_status.HTTP_200_OK,
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ))],
    deprecated=True,
)
def get_code_presigned_put_object_url(
        request: Request,
        uuid: uuid_pkg.UUID,
        path: str,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ)),
        train_session: TrainCRUD = Depends(get_train_crud)
):
    # r = train_session.get_presigned_put_object_url(namespace, uuid, path)
    # return GenericSingleResponse[str](data=r)
    return GenericSingleResponse[str](data="")


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}/pods_name",
    response_model=GenericSingleResponse[List[str]],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业的pods_name",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.READ)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def get_pods_name(
        request: Request,
        uuid: str,
        endpoint: str,
        namespace: str = Depends(train_namespace_permission(permission_level=PermissionLevel.READ))
):
    pods_name = es_client.get_train_pods_name(uuid)
    return GenericSingleResponse[List[str]](data=pods_name)


@router.get(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}/pods_name/{pod_name}/ports",
    response_model=GenericSingleResponse[List[AicpServers]],
    status_code=http_status.HTTP_200_OK,
    summary="获取训练作业的开放端口",
    dependencies=[Depends(train_has_permission()),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def get_train_pod_ports(
        endpoint: str,
        request: Request,
        uuid: str,
        pod_name: str,
        namespace: str = Depends(train_namespace_permission()),

):
    try:
        node_service = Service.get(name=f"{pod_name}-node-port", namespace=namespace).raw
        node_service_ports = node_service['spec']['ports']
    except NotFoundError as e:
        logger.warning(f"node port service not found: {e}")
        node_service_ports = []

    servers = []
    for port in node_service_ports:
        server_type = server_name = "custom"
        servers.append(
            AicpServers(
                server_name=server_type, target_port=port["targetPort"],
                server_type=server_name, status=False,
                url=f"{app.settings.SSH_HOST}:{port['nodePort']}"
            )
        )
    return GenericMultipleResponse[AicpServers](data=servers, counts=len(servers))


@router.post(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}/pods_name/{pod_name}/ports/{port}",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="新增训练作业开放端口",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def create_train_pod_ports(
        endpoint: str,
        request: Request,
        uuid: str,
        pod_name: str,
        port: int,
        namespace: str = Depends(train_namespace_permission()),
):
    train: Train = Train.one_by_id(uuid)
    train_object: List[BaseJob] = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace) # noqa
    train_object[0].add_node_port(pod_name, port)
    return BaseGenericResponse()


@router.delete(
    "/namespaces/{namespace}/endpoints/{endpoint}/trains/{uuid}/pods_name/{pod_name}/ports/{port}",
    response_model=BaseGenericResponse,
    status_code=http_status.HTTP_200_OK,
    summary="删除训练作业开放端口",
    dependencies=[Depends(train_has_permission(permission_level=PermissionLevel.WRITE)),
                  Depends(has_train_resource_permission_namespace("uuid"))]
)
def delete_train_pod_ports(
        endpoint: str,
        request: Request,
        uuid: str,
        pod_name: str,
        port: int,
        namespace: str = Depends(train_namespace_permission()),
):
    train: Train = Train.one_by_id(uuid)
    train_object: List[BaseJob] = kr8s.get(train.endpoint, train.uuid, namespace=train.namespace) # noqa
    train_object[0].remove_node_port(pod_name, port)
    return BaseGenericResponse()


