import json

from kr8s.objects import APIObject

from app import logger
from app.apps.trains.exceptions import TrainEndpointNotFound
from app.apps.trains.kr8s_objects import BaseJob, TRAINS_ENDPOINTS_MAP
from app.models.trains import Train
from app.core.models import QingcloudUser


class TrainsK8sManager:
    """
    作业管理, 用于获取作业信息
    """

    def __init__(self, train: Train, user: QingcloudUser, docker_secret: str = None):
        self.train = train
        self.user = user
        self.docker_secret = docker_secret

    def get_api_object(self) -> BaseJob:
        """
        获取作业的APIObject
        :param endpoint:
        :return:
        """
        if self.train.endpoint not in TRAINS_ENDPOINTS_MAP:
            logger.error(f"endpoint {self.train.endpoint} not supported, just support {TRAINS_ENDPOINTS_MAP.keys()}")
            raise TrainEndpointNotFound(self.train.endpoint)

        return TRAINS_ENDPOINTS_MAP[self.train.endpoint]

    def get_resource_definition(self) -> APIObject:
        """
        获取资源定义
        :return:
        """
        api_object: BaseJob = self.get_api_object()
        template = api_object.get_resource_definition(self.train, self.user, self.docker_secret)
        logger.info(f"template: {json.dumps(template)}")
        return api_object(template)
