import json
import os.path
import uuid
from typing import Dict, <PERSON>, Tuple, Union
from datetime import datetime

from app import logger, settings
from app.apps.trains.query import TrainLogQueryParams
from app.core.opensearch.client import OpenSearchClient
from app.core.utils import datetime_to_nano, get_tmp_file_full_path, nano_to_datetime


def get_time_query(t: Union[datetime, int, str], start: bool) -> Dict:
    """

    :param t:
    :param start: 是否是开始时间, 或者结束时间
    :return:
    """
    if isinstance(t, datetime):
        t_int_nano = datetime_to_nano(t)
        t_dt = t
    else:
        t_int_nano = int(t)
        t_dt = nano_to_datetime(t_int_nano)

    op = "gte" if start else "lte"

    return {
        "bool": {
            "should": [
                {
                    "bool": {
                        "must": [
                            {
                                "exists": {
                                    "field": "unixtime"
                                }
                            }, {
                                "range": {
                                    "unixtime": {
                                        op: t_int_nano
                                    }
                                }
                            }
                        ]
                    }
                },
                {
                    "bool": {
                        "must": [
                            {
                                "bool": {
                                    "must_not": [
                                        {
                                            "exists": {
                                                "field": "unixtime"
                                            }
                                        }
                                    ]
                                }
                            }, {
                                "range": {
                                    "timestamp": {
                                        op: t_dt
                                    }
                                }
                            }
                        ]
                    }
                }
            ]
        }
    }


def write_logs_to_file(f, logs):
    for l in logs:
        f.write(f'{l["_source"]["kubernetes"]["pod_name"]}: {l["_source"]["log"]}\n')


class TrainOpenSearch(OpenSearchClient):
    def __init__(self):
        super().__init__()

    def get_search_train_query(self, query: TrainLogQueryParams) -> Dict:
        if not query.pods_name:
            query.pods_name = self.get_train_pods_name(query.train_uuid)

        musts = [
            {
                "terms": {
                    "kubernetes.pod_name.keyword": query.pods_name
                }
            }
        ]

        if query.query:
            q = {
                "bool": {
                    "should": [
                        {
                            "wildcard": {
                                "log": {
                                    "value": f"*{query.query}*",
                                    "case_insensitive": True
                                }
                            }
                        },
                        {
                            "match": {
                                "log": {
                                    "query": query.query,
                                }
                            }
                        }
                    ],
                    "minimum_should_match": 1
                }
            }
            if query.fuzzy:
                q["bool"]["should"][1]["match"]["log"]["fuzziness"] = "1"
            musts.append(q)

        if query.namespace != 'ALL' and query.namespace:
            musts.append({
                "term": {
                    "kubernetes.namespace_name.keyword": {
                        "value": query.namespace
                    }
                }
            })

        if query.start_time:
            musts.append(get_time_query(query.start_time, True))
        if query.end_time:
            musts.append(get_time_query(query.end_time, False))

        body = {
            "query": {
                "bool": {
                    "must": musts,
                    "must_not": [
                        {
                            "terms": {
                                "kubernetes.container_name.keyword": [
                                    "kubectl-delivery"
                                ]
                            }
                        }
                    ]
                }
            },
            "sort": [
                {
                    "unixtime": {
                        "order": "desc" if query.reverse else "asc",
                        "missing": "_last" if query.reverse else "_first",
                        "unmapped_type": "long"
                    }
                },
                {
                    "time": {
                        "order": "desc" if query.reverse else "asc",
                    }
                }
            ],
        }
        return body

    def search_train_log(self, query: TrainLogQueryParams) -> Tuple[List, int]:
        """
        :param query:
        :return:
        """
        body = self.get_search_train_query(query)
        body.update({
            "size": query.size,
            "from": query.size * (query.page - 1),
            "track_total_hits": True
        })

        if query.query:
            body["highlight"] = {
                "pre_tags": [
                    "<highlight>"
                ],
                "post_tags": [
                    "</highlight>"
                ],
                "fields": {
                    "log": {
                        "type": "plain",
                        "fragmenter": "span",
                        "number_of_fragments": 0
                    }
                }
            }

        logger.debug(f"search_train_log: {json.dumps(body, default=str)}")
        logs_res = self._client.search(index=self.log_index_name, body=body)
        logs = []
        for log in logs_res["hits"]["hits"]:
            log["_source"]["log"] = log["highlight"]["log"][0] if "highlight" in log else log["_source"]["log"]
            logs.append(log["_source"])

        return logs, logs_res["hits"]["total"]["value"]

    def save_train_log(self, query: TrainLogQueryParams) -> str:
        """
        :param query:
        :return: str
        """
        body = self.get_search_train_query(query)
        body["_source"] = ["log", "kubernetes.pod_name"]

        # FORMAT datetime microsecond, pod_name, log
        name = str(uuid.uuid4())
        path = get_tmp_file_full_path(name)
        # use opensearch scroll api to get all logs and save to file
        with open(path, "w") as f:
            logs_res = self._client.search(index=self.log_index_name, body=body, scroll="3m", size=1000)
            write_logs_to_file(f, logs_res["hits"]["hits"])
            while True:
                logs_res = self._client.scroll(scroll_id=logs_res["_scroll_id"], scroll="3m")
                if len(logs_res["hits"]["hits"]) == 0:
                    break
                write_logs_to_file(f, logs_res["hits"]["hits"])

        return name
