from app.core.exceptions import AICPBaseException


class TrainDeleteException(AICPBaseException):
    BASE_CODE = 1001
    BASE_MESSAGE = "删除训练作业失败"


class TrainCreateException(AICPBaseException):
    BASE_CODE = 1002
    BASE_MESSAGE = "创建训练作业失败"


class TrainNameAlreadyExistsException(AICPBaseException):
    BASE_CODE = 1003
    BASE_MESSAGE = "训练作业名称已存在"


class VolumeCreateException(AICPBaseException):
    BASE_CODE = 1004
    BASE_MESSAGE = "挂载数据集失败"


class CreateCodePathException(AICPBaseException):
    BASE_CODE = 1005
    BASE_MESSAGE = "创建代码上传路径失败"


class UploadPathException(AICPBaseException):
    BASE_CODE = 1006
    BASE_MESSAGE = "无法处理的文件路径"


class MountCodePathUsedException(AICPBaseException):
    BASE_CODE = 1007
    BASE_MESSAGE = "已被使用的代码路径"


class TrainPatchException(AICPBaseException):
    BASE_CODE = 1008
    BASE_MESSAGE = "更新训练作业失败"


class TrainNotFound(AICPBaseException):
    BASE_CODE = 1009
    BASE_MESSAGE = "训练作业不存在"


class TrainReplicaTypeException(AICPBaseException):
    BASE_CODE = 1010
    BASE_MESSAGE = "训练作业节点类型错误"


class TrainEndpointNotFound(AICPBaseException):
    BASE_CODE = 1011
    BASE_MESSAGE = "训练作业框架类型不存在"


class TrainQuotaException(AICPBaseException):
    BASE_CODE = 1012
    BASE_MESSAGE = "配额不足，请联系管理员"


class TrainPriorityStatusException(AICPBaseException):
    BASE_CODE = 1013
    BASE_MESSAGE = "当前状态不允许修改优先级"


class TrainPriorityValueException(AICPBaseException):
    BASE_CODE = 1014
    BASE_MESSAGE = "优先级数值不正确"

class TrainRestartDenied(AICPBaseException):
    BASE_CODE = 1015
    BASE_MESSAGE = "任务正在停止中，请稍后重试"
