from typing import List

from fastapi import Query
from pydantic import BaseModel, Field, root_validator

from app.core.models import CommonSearchQueryParams


class ProductQueryParams(CommonSearchQueryParams):
    def __init__(
            self,
            offset: int = 0,
            limit: int = 10,
            type: str = Query(default="gpu_count", description="attr_id"),
    ):
        super().__init__()
        self.offset = offset
        self.limit = limit
        self.type = type


class AddProductRequest(BaseModel):
    aipods_type: str = Field(..., description="规格类型")
    aipods_scope: str = Field(..., description="产品范围")
    aipods_usage: str = Field(..., description="资源类型")
    cpu_count: str = Field(..., description="CPU核数")
    memory: str = Field(..., description="内存")
    cpu_model: str = Field(..., description="CPU型号")
    gpu_model: str = Field(..., description="GPU型号")
    gpu_count: str = Field(..., description="GPU数量")
    gpu_memory: str = Field(..., description="GPU显存")
    os_disk: str = Field(..., description="系统盘")
    disk: str = Field(..., description="数据盘")
    nvlink: str = Field("0", description="nvlink")
    network: str = Field("0", description="IB网络")


class UpdateProductRequest(AddProductRequest):
    sku_id: str = Field(..., description="sku_id")


class VolcanoConfigCreateReq(BaseModel):
    actions: List[str] = Field(..., min_length=1, description="volcano调度行为")
    policies: List[str] = Field(..., min_length=1, description="volcano调度策略")
    algorithms: List[str] = Field(..., min_length=1, description="volcano调度算法")

    @root_validator
    def check_actions_not_empty(cls, values):
        actions = values.get("actions")
        policies = values.get("policies")
        algorithms = values.get("algorithms")
        if not policies or not actions or not algorithms:
            raise ValueError('actions or policies or algorithms list cannot be empty')
        return values


class VolcanoEnableReq(BaseModel):
    enable: bool = Field(..., description="是否开启volcano")
