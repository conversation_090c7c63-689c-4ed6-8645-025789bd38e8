from typing import Dict

import yaml
from fastapi import APIRouter, Depends
from kubernetes import config, client
from kubernetes.client import ApiException
from kubernetes.config import ConfigException

import app
from app.apps.manage_config.exceptions import VolcanoCMNotFoundException, BillingEnableException
from app.apps.manage_config.requests import VolcanoConfigCreateReq, VolcanoEnableReq, ProductQueryParams, \
    AddProductRequest, UpdateProductRequest
from app.core.constant import TRAINNING_OPERATOR_COMMAND
from app.core.qingcloud.interface import product_attr_query_request, product_center_query_request, qai_spec_info, \
    product_aipod_type_add_attribute, qai_add_sku, qai_delete_sku
from app.core.response import BaseGenericResponse, GenericSingleResponse, GenericMultipleResponse
from app.core.loggers import logger

# api prefix: /aicp/manage
router = APIRouter(prefix="/manage", tags=["manage"])


# 处理action顺序
def process_array(input_array):
    required_order = ["enqueue", "allocate", "backfill", "preempt"]
    result = [item for item in required_order if item in input_array]
    return result


@router.get("/status/volcano", response_model=GenericSingleResponse[bool])
def get_volcano_status():
    return GenericSingleResponse[bool](data=app.settings.VOLCANO_ENABLE)


@router.post("/status/volcano", response_model=BaseGenericResponse)
def change_volcano_status(
        req: VolcanoEnableReq,
):
    """
    Enable or disable volcano.
    :param manage_db:
    :param req:
    :return:
    """
    if app.settings.VOLCANO_ENABLE and req.enable:
        logger.info("Volcano status not need to be changed.")
        return BaseGenericResponse()
    if not app.settings.VOLCANO_ENABLE and not req.enable:
        logger.info("Volcano status not need to be changed.")
        return BaseGenericResponse()
    try:
        config.load_incluster_config()
    except ConfigException:
        config.load_kube_config()
    namespace = "kubeflow"
    deployment_name = "training-operator"
    configmap_namespace = "aicp-system"
    configmap_name = "aicp-web-app-parameters"
    # Update training-operator deployment.
    apps_v1 = client.AppsV1Api()
    # Change configmap.
    v1 = client.CoreV1Api()
    try:
        deployment = apps_v1.read_namespaced_deployment(deployment_name, namespace)
        # Change command.
        if req.enable:
            deployment.spec.template.spec.containers[0].command = TRAINNING_OPERATOR_COMMAND["volcano-enable"]
        else:
            deployment.spec.template.spec.containers[0].command = TRAINNING_OPERATOR_COMMAND["volcano-disable"]
        # update deployment
        apps_v1.patch_namespaced_deployment(
            name=deployment_name,
            namespace=namespace,
            body=deployment
        )
        # Get aicp config.
        configmap = v1.read_namespaced_config_map(name=configmap_name, namespace=configmap_namespace)
        if "api_server.yaml" in configmap.data:
            api_server_yaml_content = configmap.data["api_server.yaml"]
            api_server_data = yaml.safe_load(api_server_yaml_content)
            api_server_data["VOLCANO_ENABLE"] = "true" if req.enable else "false"
            # update configmap
            updated_api_server_yaml_content = yaml.dump(api_server_data)
            configmap.data["api_server.yaml"] = updated_api_server_yaml_content
            v1.replace_namespaced_config_map(name=configmap_name, namespace=configmap_namespace, body=configmap)
            logger.info("AICP configMap updated successfully.")
    except ApiException as e:
        logger.error("修改volcano状态失败")
    return BaseGenericResponse()


@router.post("/volcano", response_model=BaseGenericResponse)
def modify_volcano_config(
        req: VolcanoConfigCreateReq,
):
    """
    Modify volcano config
    :param req:
    :return:
    """
    try:
        config.load_incluster_config()
    except ConfigException:
        config.load_kube_config()
    v1 = client.CoreV1Api()
    try:
        # Get volcano config.
        configmap = v1.read_namespaced_config_map(name=app.settings.VOLCANO_CONFIGMAP_NAME, namespace=app.settings.VOLCANO_NAMESPACE)
    except ApiException as e:
        if e.status == 404:
            logger.error("volcano配置文件不存在，请检查配置文件位置")
            raise VolcanoCMNotFoundException
        else:
            raise e
    vc_conf = "actions: \""
    require_actions = process_array(req.actions)
    for action in require_actions:
        vc_conf = vc_conf + action + ", "
    vc_conf = vc_conf.rstrip(", ")
    vc_conf += "\"\ntiers:\n- plugins:\n"
    for plugin in req.policies:
        if "gang" == plugin and "preempt" in require_actions:
            vc_conf = vc_conf + "  - name: " + plugin + "\n    enablePreemptable: false\n"
            continue
        vc_conf = vc_conf + "  - name: " + plugin + "\n"
    vc_conf += "- plugins:\n"
    for plugin in req.algorithms:
        vc_conf = vc_conf + "  - name: " + plugin + "\n"
    # Modify vc_conf.
    configmap.data['volcano-scheduler.conf'] = vc_conf
    try:
        # Update cm.
        v1.replace_namespaced_config_map(name=app.settings.VOLCANO_CONFIGMAP_NAME, namespace=app.settings.VOLCANO_NAMESPACE, body=configmap)
        # 重启 volcano-scheduler
        pods = v1.list_namespaced_pod(namespace=app.settings.VOLCANO_NAMESPACE)
        for pod in pods.items:
            if "volcano-scheduler" in pod.metadata.name:
                v1.delete_namespaced_pod(pod.metadata.name, app.settings.VOLCANO_NAMESPACE)
        logger.info(f'VC ConfigMap updated successfully.')
    except ApiException as e:
        logger.error(e)
    return BaseGenericResponse()


@router.get("/volcano", response_model=GenericSingleResponse[Dict])
def get_volcano_config():
    try:
        config.load_incluster_config()
    except ConfigException:
        config.load_kube_config()
    v1 = client.CoreV1Api()
    try:
        # Get volcano config.
        configmap = v1.read_namespaced_config_map(name=app.settings.VOLCANO_CONFIGMAP_NAME, namespace=app.settings.VOLCANO_NAMESPACE)
    except ApiException as e:
        if e.status == 404:
            logger.error("volcano配置文件不存在，请检查配置文件位置")
            raise VolcanoCMNotFoundException
        else:
            raise e
    original_cm_data = configmap.data.get("volcano-scheduler.conf")
    data = yaml.safe_load(original_cm_data)
    actions_str = data.get('actions', '')
    actions = [action.strip() for action in actions_str.split(',')]
    tiers = data.get('tiers', [])
    policies = []
    algorithms = []
    for plugin in tiers[0]['plugins']:
        policies.append(plugin['name'])
    for plugin in tiers[1]['plugins']:
        algorithms.append(plugin['name'])
    response_data = {
        "actions": actions,
        "policies": policies,
        "algorithms": algorithms,
    }
    return GenericSingleResponse[Dict](data=response_data)


@router.get("/product/heading", response_model=GenericMultipleResponse[Dict])
def get_boss_product_heading():
    result = product_attr_query_request()
    res_data = []
    for attribute in result["attributes"]:
        item = {
            "name": attribute['name'],
            "prod_id": attribute['prod_id'],
            "attr_id": attribute['attr_id'],
            "attr_code": attribute['attr_code'],
        }
        res_data.append(item)
    return GenericMultipleResponse[Dict](data=res_data, counts=len(res_data))


@router.get("/product/data", response_model=GenericMultipleResponse[Dict])
def get_boss_product_data(
        query: ProductQueryParams = Depends(ProductQueryParams)
):
    # 获取所有的产品规格信息
    res = product_center_query_request(offset=0, limit=500, status=["sale", "no_sale"])
    skus = res["skus"]
    sorted_skus = sorted(skus, key=lambda sku: get_sorted_key(sku, query.type))
    # 将数据切片返回给前端
    return GenericMultipleResponse[Dict](data=sorted_skus[query.offset:query.offset+query.limit], counts=query.limit)


def get_sorted_key(sku, sort_kind):
    """
    用于sorted lambda表达式排序使用
    :param sku:
    :param sort_kind:
    :return:
    """
    data = sku["filters"]
    for i in range(len(data)):
        if data[i]["attr_id"] == sort_kind:
            return data[i]["attr_value"]
    return ""


@router.post("/product/data", response_model=BaseGenericResponse)
def add_product_data(
        query: AddProductRequest
):
    # 获取QAI规格项信息
    qai_spec = qai_spec_info()
    qai_prod_id = qai_spec["specs"][0]["prod_id"]
    qai_spec_id = qai_spec["specs"][0]["spec_id"]
    qai_spec_code = qai_spec["specs"][0]["spec_code"]
    # 获取boss规格属性
    result = product_attr_query_request()
    product_spec_attribute = {}
    for attribute in result["attributes"]:
        product_spec_attribute[f"{attribute['name']}"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"
    sku_item = set_sku_infos(query.aipods_type,
                             query.aipods_scope,
                             query.aipods_usage,
                             query.cpu_count,
                             query.memory,
                             query.cpu_model,
                             query.gpu_model,
                             query.gpu_count,
                             query.gpu_memory,
                             query.os_disk,
                             query.disk,
                             query.nvlink,
                             query.network)
    try:
        boss_add_sku_attributes(product_spec_attribute, sku_item)
        qai_add_sku(qai_spec_code, sku_item, qai_prod_id, qai_spec_id)
    except Exception as e:
        logger.error("添加产品规格异常")
        raise e
    return BaseGenericResponse()


@router.get("/product/billing", response_model=GenericSingleResponse[bool])
def get_billing_enable():
    return GenericSingleResponse[bool](data=app.settings.billing_enable)


@router.patch("/product/data", response_model=BaseGenericResponse)
def update_product_data(
        query: UpdateProductRequest
):
    if app.settings.billing_enable:
        logger.warning("计费已开启，无法修改规格。")
        raise BillingEnableException()
    # 获取QAI规格项信息
    qai_spec = qai_spec_info()
    qai_prod_id = qai_spec["specs"][0]["prod_id"]
    qai_spec_id = qai_spec["specs"][0]["spec_id"]
    qai_spec_code = qai_spec["specs"][0]["spec_code"]
    # 获取boss规格属性
    result = product_attr_query_request()
    product_spec_attribute = {}
    for attribute in result["attributes"]:
        product_spec_attribute[f"{attribute['name']}"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"
    try:
        # 删除原有规格
        qai_delete_sku(qai_prod_id, qai_spec_id, [query.sku_id])
        sku_item = set_sku_infos(query.aipods_type,
                                 query.aipods_scope,
                                 query.aipods_usage,
                                 query.cpu_count,
                                 query.memory,
                                 query.cpu_model,
                                 query.gpu_model,
                                 query.gpu_count,
                                 query.gpu_memory,
                                 query.os_disk,
                                 query.disk,
                                 query.nvlink,
                                 query.network)
        boss_add_sku_attributes(product_spec_attribute, sku_item)
        qai_add_sku(qai_spec_code, sku_item, qai_prod_id, qai_spec_id)
    except Exception as e:
        logger.error("修改产品规格异常")
        raise e
    return BaseGenericResponse()


def set_sku_infos(
        aipods_type,
        aipods_scope,
        aipods_usage,
        cpu_count,
        memory,
        cpu_model,
        gpu_model,
        gpu_count,
        gpu_memory,
        os_disk,
        disk,
        nvlink,
        network
):
    """
    设置sku数据
    :param aipods_type: 规格类型
    :param aipods_scope: 产品范围
    :param aipods_usage: 资源类型
    :param cpu_count: CPU核数
    :param memory: 内存
    :param cpu_model: CPU型号
    :param gpu_model: GPU型号
    :param gpu_count: GPU数量
    :param gpu_memory: GPU显存
    :param os_disk: 系统盘
    :param disk: 数据盘
    :param nvlink: 是否存在nvlink
    :param network: ib网卡数量
    :return:
    """
    result = [
        {
            "attr_id": "aipods_type",
            "operator": "==",
            "attr_value": aipods_type
        },
        {
            "attr_id": "aipods_scope",
            "operator": "==",
            "attr_value": aipods_scope
        },
        {
            "attr_id": "aipods_usage",
            "operator": "==",
            "attr_value": aipods_usage
        },
        {
            "attr_id": "cpu_count",
            "operator": "==",
            "attr_value": cpu_count
        },
        {
            "attr_id": "memory",
            "operator": "==",
            "attr_value": memory
        },
        {
            "attr_id": "cpu_model",
            "operator": "==",
            "attr_value": cpu_model
        },
        {
            "attr_id": "gpu_model",
            "operator": "==",
            "attr_value": gpu_model
        },
        {
            "attr_id": "gpu_count",
            "operator": "==",
            "attr_value": gpu_count
        },
        {
            "attr_id": "gpu_memory",
            "operator": "==",
            "attr_value": gpu_memory
        },
        {
            "attr_id": "os_disk",
            "operator": "==",
            "attr_value": os_disk
        },
        {
            "attr_id": "nvlink",
            "operator": "==",
            "attr_value": nvlink
        }
    ]
    if disk != "0":
        result.append({
            "attr_id": "disk",
            "operator": "==",
            "attr_value": disk
        })
    if network != "0":
        result.append({
            "attr_id": "network",
            "operator": "==",
            "attr_value": network
        })
    return result


def boss_add_sku_attributes(product_spec_attribute, sku_info):
    for item in sku_info:
        if item['attr_id'] == 'aipods_usage':
            # 资源类型
            result = add_sku_attribute(product_spec_attribute, "资源类型", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加资源类型：{item['attr_value']}，成功。")
        if item['attr_id'] == 'cpu_count':
            # CPU核数
            result = add_sku_attribute(product_spec_attribute, "CPU核数", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加CPU核数：{item['attr_value']}，成功。")
        if item['attr_id'] == 'memory':
            # 内存
            result = add_sku_attribute(product_spec_attribute, "内存", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加内存：{item['attr_value']}，成功。")
        if item['attr_id'] == 'cpu_model':
            # CPU型号
            result = add_sku_attribute(product_spec_attribute, "CPU型号", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加CPU型号：{item['attr_value']}，成功。")
        if item['attr_id'] == 'gpu_model':
            # GPU型号
            result = add_sku_attribute(product_spec_attribute, "GPU型号", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加GPU型号：{item['attr_value']}，成功。")
        if item['attr_id'] == 'gpu_count':
            # GPU数量
            result = add_sku_attribute(product_spec_attribute, "GPU数量", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加GPU数量：{item['attr_value']}，成功。")
        if item['attr_id'] == 'gpu_memory':
            # GPU显存
            result = add_sku_attribute(product_spec_attribute, "GPU显存", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加GPU显存：{item['attr_value']}，成功。")
        if item['attr_id'] == 'os_disk':
            # 系统盘
            result = add_sku_attribute(product_spec_attribute, "系统盘", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加系统盘：{item['attr_value']}，成功。")
        if item['attr_id'] == 'nvlink':
            # nvlink
            result = add_sku_attribute(product_spec_attribute, "nvlink", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加nvlink：{item['attr_value']}，成功。")
        if item['attr_id'] == 'network':
            # IB网卡
            result = add_sku_attribute(product_spec_attribute, "IB网络", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加IB网卡：{item['attr_value']}，成功。")
        if item['attr_id'] == 'disk':
            # 数据盘
            result = add_sku_attribute(product_spec_attribute, "数据盘", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加数据盘：{item['attr_value']}，成功。")


def add_sku_attribute(product_spec_attribute, sku_attr_type, value):
    prod_id = product_spec_attribute[sku_attr_type].split('-')[0]
    attr_id = product_spec_attribute[sku_attr_type].split('-')[1]
    attr_infos = [
        {
            "name": value,
            "attr_value": value,
            "description": value,
            "operator": "=="
        }
    ]
    return product_aipod_type_add_attribute(attr_id, prod_id, attr_infos)
