import datetime
from statistics import mean
from typing import Dict, List

from fastapi import APIRouter, Request, Depends
from kubernetes.client import V1Node, V1NodeList

from app.cruds.resource_pool import ResourcePoolCrud
from app.apps.resource_pool.dependencies import get_pool_crud
from app.models.resource_pool import ResourcePool, ResourcePoolCreateReq, ResourcePoolModifyReq, \
    ResourcePoolAddReq, ResourcePoolStatisticsResponse, ResourcePoolRemoveReq
from app.core.kube.api import check_taints, check_taints_by_whitelist, list_nodes
from app.core.prometheus.client import PromethusClient
from app.core.prometheus.monitoring_operator import MonitoringOperator, prometheusQL_meter_templates_node_GPU
from app.core.prometheus.query_params import MonitoringQueryParams
from app.core.qingcloud.interface import product_aipod_type_query_request
from app.core.response import GenericSingleResponse, GenericMultipleResponse, BaseGenericResponse
from app.core.utils import GPUManager

router = APIRouter(prefix="/resource_pool", tags=["resource-pool"])


@router.post("",
             summary="创建资源池",
             response_model=BaseGenericResponse)
def resource_pool_add(
        request: Request,
        req: ResourcePoolCreateReq,
        db: ResourcePoolCrud = Depends(get_pool_crud)
):
    """
    创建资源池
    """
    db.add(req)
    return BaseGenericResponse()


@router.put("",
            summary="修改资源池",
            response_model=GenericSingleResponse[ResourcePool])
def resource_pool_modify(
        request: Request,
        req: ResourcePoolModifyReq,
        db: ResourcePoolCrud = Depends(get_pool_crud)
):
    """
    创建资源池
    """
    data = db.update(req)
    return GenericSingleResponse[ResourcePool](data=data)


@router.get("",
            summary="查询资源池",
            response_model=GenericMultipleResponse[ResourcePoolStatisticsResponse])
def resource_pool_list(
        request: Request,
        db: ResourcePoolCrud = Depends(get_pool_crud)
):
    """
    查询资源池
    """
    resp: List[ResourcePoolStatisticsResponse] = []
    data = db.list()

    if not data:
        return GenericMultipleResponse[ResourcePoolStatisticsResponse](data=[], counts=0)

    pc = PromethusClient()
    pcm = MonitoringOperator()
    resource_used_node: Dict[str, int] = pc.aicp_resource_used_node()

    for resource_pool in data:
        pool_id = resource_pool.pool_id
        label_selector = f"aicp.group/compute_group={pool_id}"

        nodes: V1NodeList = list_nodes(label_selector=label_selector)
        nodes_name = [node.metadata.name for node in nodes.items]
        resource_pool.node_count = len(nodes_name)
        resource_pool.gpu_count = db.get_node_gpu_count_by_names(nodes_name)
        resource_pool.updated_at = datetime.datetime.now()

        resource_pool_response = ResourcePoolStatisticsResponse.from_orm(resource_pool)
        resp.append(resource_pool_response)

        if not nodes_name:
            continue

        node: V1Node = nodes.items[0]
        gpu_vendor = ""
        if gpu_matcher := GPUManager().get_node_gpu_type(node.status.allocatable):
            gpu_vendor = gpu_matcher.vendor

        # 获取资源组统计信息
        resource_pool_response.node_used_count = len(set(nodes_name) & set(resource_used_node.keys()))
        resource_pool_response.unschedulable_node_count = len(
            list(filter(lambda x: not check_taints_by_whitelist(x.spec.taints), nodes.items))
        )

        if not gpu_vendor:
            continue

        resource_pool_response.unschedulable_gpu_count = db.get_unschedulable_gpu_count(nodes_name)
        gpu_metrics = pcm.get_named_metrics(
            list(prometheusQL_meter_templates_node_GPU.keys()),
            MonitoringQueryParams(resource_filer=nodes_name, gpu_vendor=gpu_vendor)
        )
        for gm in gpu_metrics:
            if gm["metric_name"] == "node_gpu_util":
                resource_pool_response.gpu_ava_util = mean([float(x["value"][1]) for x in gm["result"]])
            elif gm["metric_name"] == "node_gpu_mem_usage":
                resource_pool_response.gpu_used_mem = sum([int(x["value"][1]) for x in gm["result"]])
            elif gm["metric_name"] == "node_gpu_mem_total":
                resource_pool_response.gpu_sum_mem = sum([int(x["value"][1]) for x in gm["result"]])
            elif gm["metric_name"] == "node_gpu_used_count":
                resource_pool_response.gpu_used_count = sum([int(x["value"][1]) for x in gm["result"]])

    return GenericMultipleResponse[ResourcePoolStatisticsResponse](data=resp, counts=len(data))


@router.delete("",
               summary="删除资源池",
               response_model=BaseGenericResponse)
def resource_pool_delete(
        request: Request,
        pool_id: str,
        db: ResourcePoolCrud = Depends(get_pool_crud)
):
    """
    删除资源池
    """
    return db.delete(pool_id)


@router.post("/add_node",
             summary="向资源池中添加节点",
             response_model=BaseGenericResponse)
def resource_pool_add_node(
        request: Request,
        req: ResourcePoolAddReq,
        db: ResourcePoolCrud = Depends(get_pool_crud)
):
    """
    向资源池中添加节点，节点来源可以是其他资源池，或者不属于任何资源池的节点
    """
    return db.add_node(req)


@router.post("/remove_node",
             summary="从资源池中移除节点",
             response_model=BaseGenericResponse)
def resource_pool_remove_node(
        request: Request,
        req: ResourcePoolRemoveReq,
        db: ResourcePoolCrud = Depends(get_pool_crud)
):
    """
    从资源池移除节点
    """
    return db.remove_node(req)


@router.get("/aipod_type",
            summary="查询规格属性",
            response_model=dict)
def get_aipod_type(
):
    """
    查询规格属性
    """
    return product_aipod_type_query_request()
