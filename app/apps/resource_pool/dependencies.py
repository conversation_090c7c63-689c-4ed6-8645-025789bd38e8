from fastapi import Depends, Request
from sqlmodel import Session

from app.cruds.resource_pool import ResourcePoolCrud
from ...core.db import get_db_session


async def get_pool_crud(
        request: Request,
        session: Session = Depends(get_db_session)
) -> ResourcePoolCrud:
    # if request.user.role != USER_ROLE_ADMIN:
    #     raise HTTPException(status_code=401, detail="Unauthorized")
    return ResourcePoolCrud(session=session, user=request.user)
