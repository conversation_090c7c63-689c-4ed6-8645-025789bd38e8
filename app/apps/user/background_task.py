from app import logger
from app.core.kube.api.kfam import workgroup_cache
from app.core.rlock import RedisClient
from app.core.utils import get_namespaces_cache_key
from app.cruds.user import SharedNamespace
from app.models.user import SharedPermissionEnum
from app.core.models import <PERSON><PERSON><PERSON><PERSON><PERSON>


def refresh_sub_user_permission(user: <PERSON><PERSON><PERSON><PERSON><PERSON>):
    if not user.is_subuser():
        logger.info(f"User {user.user_id} is not a subuser, skip refreshing permission.")
        return
    try:
        changed = SharedNamespace(user.user_id, user.root_user_id, SharedPermissionEnum.Edit).create()
        if changed:
            workgroup_cache.remove_work_group_cache(user.root_user_id)
    except Exception as e:
        logger.exception(f"Failed to create shared namespace for user {user.user_id} with root user {user.root_user_id}.")
