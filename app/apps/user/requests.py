from typing import List, Dict

from pydantic import Required, BaseModel, Field

from fastapi import Query

from app.core.models import CommonSearchQueryParams


class AicpUserQueryParams(CommonSearchQueryParams):
    def __init__(
            self,
            offset: int = 0,
            limit: int = 10,
            type: str = Query(default=None, description="id,email,phone"),
            input: str = Query(default=None, description="用户ID，邮箱，手机号模糊查询"),
    ):
        super().__init__(offset, limit)
        self.offset = offset
        self.limit = limit
        self.type = type
        self.input = input


class UserIdModel(BaseModel):
    user_ids: List[str] = Field(..., min_length=1, description="user_ids")


class InitUserModel(BaseModel):
    init_user_id: str = Field(..., min_length=1, description="init_user_id")
    root_user_id: str = Field(..., min_length=1, description="root_user_id")


class AicpUserQuotaParams(BaseModel):
    user_id: str = Field(..., description="user_id")
    containers: int = Field(default=0, description="容器实例数量")
    jobs: int = Field(default=0, description="分布式训练任务数量")
    rg_nodes: int = Field(default=0, description="资源组节点数量")
    maas_server: int = Field(default=0, description="推理服务数量")
    priority: int = Field(default=1, description="分布式训练优先级")
    dir_number: int = Field(default=0, description="目录数量")
    capacity: int = Field(default=0, description="存储容量")
    gpu_number: int = Field(default=0, description="gpu数量")
    vgpu_number: int = Field(default=0, description="vgpu数量")
    gpu_model_quotas: Dict = Field({"all": 0}, description="GPU 配额")
    vgpu_model_quotas: Dict = Field({"all": 0}, description="Vgpu 配额")
    docker_quota: int = Field(default=0, description="镜像仓库配额")
    project_number: int = Field(default=0, description="项目配额")
