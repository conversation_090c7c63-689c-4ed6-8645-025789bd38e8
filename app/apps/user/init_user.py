from kr8s.objects import Namespace
from sqlalchemy import text
from sqlmodel import Session

import app
from app import logger
from app.cruds.user import UserCRUD
from app.models.user import UserInfo
from app.core.db import get_db_context_session


def initialization_user():
    with get_db_context_session() as session:
        user_crud = UserCRUD(session=session)
        count = user_crud.get_user_count()
        if count == 0:
            logger.info("Start synchronizing IaaS and AICP user information...")
            # kubeflow ns label
            selector = {'app.kubernetes.io/part-of': 'kubeflow-profile'}
            namespaces = Namespace.list(label_selector=selector)
            if len(namespaces) == 0:
                logger.info("Synchronization ends.")
                return
            # Get ths ns of kubernetes.
            aicp_users = []
            user_ids = []
            for ns in namespaces:
                user_ids.append(ns.metadata.annotations.get("owner"))
                user_info = UserInfo()
                user_info.namespace = ns.name
                user_info.user_id = ns.metadata.annotations.get("owner")
                user_info.containers_number = app.settings.POD_NUMBER
                user_info.jobs_number = app.settings.JOB_NUMBER
                user_info.rg_nodes = app.settings.RG_NODES
                user_info.status = "active"
                aicp_users.append(user_info)
            user_crud.batch_insert_user(aicp_users)
        logger.info("Synchronization ends.")


def table_exists() -> bool:
    sql = "select distinct table_name from information_schema.columns where table_name='user_info';"
    tables = Session().execute(text(sql)).fetchall()
    if len(tables) == 0:
        return False
    return True