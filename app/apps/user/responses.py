from app.core.response import BaseGenericResponse
from fastapi import FastAPI, Response


class NoBindsResponse(BaseGenericResponse):
    ret_code = 404
    ret_msg = 'You do not have permission to perform this action.'


class AicpUserInfo:
    namespace: str
    user_id: str
    email: str
    phone: str
    # active -> 正常
    status: str
    # parent -> 主账号 child -> 子账号
    type: int
    containers: int
    jobs: int
    resource_nodes: int
    quota_containers: int
    quota_jobs: int
    quota_resource_nodes: int
    notify_email: str
    priority: int
    dir_number: int
    capacity: int
    maas_server: int
    quota_maas_server: int

    def __init__(self, namespace, user_id, email=None, phone=None, status=None, type=None, containers=None, jobs=None, resource_nodes=None, quota_containers=None, quota_jobs=None, quota_resource_nodes=None, notify_email=None, priority=None, dir_number=None, capacity=None, maas_server=None, quota_maas_server=None,
                 quota_gpu_number=None,quota_gpu_model_quotas=None, quota_vgpu_number=None, quota_vgpu_model_quotas=None, docker_quota=None, gpu_count=None, vgpu_count=None, docker_used=None, dir_used=None, stor_used=None, project_number=None, project_used=None):

        self.namespace = namespace
        self.user_id = user_id
        self.email = email
        self.phone = phone
        self.status = status
        self.type = type
        self.containers = containers
        self.jobs = jobs
        self.resource_nodes = resource_nodes
        self.quota_containers = quota_containers
        self.quota_jobs = quota_jobs
        self.quota_resource_nodes = quota_resource_nodes
        self.notify_email = notify_email
        self.priority = priority
        self.dir_number = dir_number
        self.capacity = capacity
        self.maas_server = maas_server
        self.quota_maas_server = quota_maas_server
        self.gpu_number = quota_gpu_number
        self.gpu_model_quotas = quota_gpu_model_quotas
        self.vgpu_number = quota_vgpu_number
        self.vgpu_model_quotas = quota_vgpu_model_quotas
        self.docker_quota = docker_quota
        self.gpu_count = gpu_count
        self.vgpu_count = vgpu_count
        self.docker_used = docker_used
        self.dir_used = dir_used
        self.stor_used = stor_used
        self.project_number = project_number
        self.project_used = project_used
