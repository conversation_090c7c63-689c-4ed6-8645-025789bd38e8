import traceback
from typing import Dict

from fastapi import APIRouter, BackgroundTasks, Depends, Request, Query

import app
from app.apps.user.background_task import refresh_sub_user_permission
from app.apps.user.exceptions import UserHasNotYetInitializedTheResources, UserPriorityException
from app.core.exceptions import PermissionDeniedException
from app.core.global_server.exceptions import UserHasNotPermissionException, GetSubUserInfoException
from app.core.qingcloud.docker_api import QingcloudDockerApiClient
from app.core.qingcloud.maas import MaasClient
from app.core.rlock import RedisClient
from app.core.utils import get_namespaces_cache_key
from app.core.qingcloud.resource import ProductCenterResource
from app.cruds.user import UserCRUD
from app.apps.user.dependencies import get_user_crud
from app.models.user import ProjectCategory, ProjectCategoryBase, UserBaseInfo, UserInfo
from app.apps.user.requests import AicpUserQueryParams, AicpUserQuotaParams, UserIdModel, InitUserModel
from app.apps.user.responses import AicpUserInfo
from app.core.kube.api.kfam import KfamClient
from app.core.kube.models.kfam import WorkGroupInfo, Namespace
from app.core.loggers import logger
from app.core.models import AicpUser
from app.core.qingcloud.interface import describe_user_by_type, restore_user, delete_user, \
    describe_user_without_redis, describe_sub_users, describe_sub_users_without_ak_sk, describe_user, \
    product_center_query_request
from app.core.ufm.crud import UFMInfoCrud
from app.core.ufm.dependencies import get_ufm_crud
from app.core.response import BaseErrorResponse, GenericSingleResponse, GenericMultipleResponse, BaseGenericResponse

# api prefix: /aicp
router = APIRouter(prefix="", tags=["users"])

USER_ID_LENGTH = 12

def init_pkey(user: AicpUser, ufm_db: UFMInfoCrud):
    ufm_db.add_ufm_pkey(user.user_id)


@router.get("/user/namespaces",
            summary="获取当前账号可以看到的用户列表",
            response_model=GenericSingleResponse[WorkGroupInfo])
def get_users(
        request: Request,
        kind: str,
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取各个账号可以看到的用户列表
    """
    # 容器实例和分布式训练需要在Kfam判断逻辑
    if kind in ["TN", "NB"]:
        # 获取主账号的所有绑定信息
        kfam_client: KfamClient = KfamClient(request.user.root_user_id)
        workgroup: WorkGroupInfo = kfam_client.get_all_profile(user_db)
        # 主账号返回所有的绑定信息
        if not request.user.is_subuser():
            return GenericSingleResponse[WorkGroupInfo](data=workgroup)
        # 子账号根据权限返回
        permission = next(item["permission"] for item in request.user.permissions if item["module"] == kind)
        if permission == "NO":
            raise UserHasNotPermissionException()
        if permission == "OWN":
            for item in workgroup.namespaces:
                if item.namespace_user_id == request.user.user_id:
                    ns_data = Namespace(
                        user=item.user,
                        namespace=item.namespace,
                        namespace_user_id=item.namespace_user_id,
                        role=item.role,
                        email=item.email
                    )
                    res_data = WorkGroupInfo(
                        user=request.user.user_id,
                        namespaces=[ns_data],
                        isClusterAdmin=workgroup.is_cluster_admin
                    )
                    return GenericSingleResponse[WorkGroupInfo](data=res_data)
            raise UserHasNotYetInitializedTheResources()
        if permission in ["READALL", "ALL"]:
            return GenericSingleResponse[WorkGroupInfo](data=workgroup)
    elif kind == "EPFS":
        # 主账号返回所有的用户信息
        if not request.user.is_subuser():
            data = describe_sub_users_without_ak_sk(request.user.root_user_id)
            if not data.get("user_set"):
                logger.info(f"当前主账号{request.user.root_user_id}，不存在子账号。")
                res_data = WorkGroupInfo(
                    user=request.user.user_id,
                    namespaces=[
                        Namespace(
                            user=request.user.user_id,
                            namespace=request.user.user_id.lower(),
                            namespace_user_id=request.user.user_id,
                            role="",
                            email=request.user.email
                        )
                    ],
                    isClusterAdmin=False
                )
                return GenericSingleResponse[WorkGroupInfo](data=res_data)
            namespaces = []
            # 追加主账号信息
            namespaces.append(Namespace(
                user=request.user.user_id,
                namespace=request.user.user_id.lower(),
                namespace_user_id=request.user.user_id,
                role="",
                email=request.user.email
            ))
            for item in data.get("user_set"):
                # 只获取active状态的
                if item["status"] == "active":
                    ns_data = Namespace(
                        user=item["user_id"],
                        namespace=item["user_id"].lower(),
                        namespace_user_id=item["user_id"],
                        role="",
                        email=item["email"]
                    )
                    namespaces.append(ns_data)
            res_data = WorkGroupInfo(
                user=request.user.user_id,
                namespaces=namespaces,
                isClusterAdmin=False
            )
            return GenericSingleResponse[WorkGroupInfo](data=res_data)
        permission = next(item["permission"] for item in request.user.permissions if item["module"] == kind)
        if permission == "NO":
            raise UserHasNotPermissionException()
        if permission == "OWN":
            ns_data = Namespace(
                user=request.user.user_id,
                namespace=request.user.user_id.lower(),
                namespace_user_id=request.user.user_id,
                role="",
                email=request.user.email
            )
            res_data = WorkGroupInfo(
                user=request.user.user_id,
                namespaces=[ns_data],
                isClusterAdmin=False
            )
            return GenericSingleResponse[WorkGroupInfo](data=res_data)
        # 获取全部子账号和主账号信息
        data = describe_sub_users_without_ak_sk(request.user.root_user_id)
        if not data.get("user_set"):
            logger.error(f"当前主账号{request.user.root_user_id}，不存在子账号。")
            raise GetSubUserInfoException()
        namespaces = []
        for item in data.get("user_set"):
            # 只获取active状态的
            if item["status"] == "active":
                ns_data = Namespace(
                    user=item["user_id"],
                    namespace=item["user_id"].lower(),
                    namespace_user_id=item["user_id"],
                    role="",
                    email=item["email"]
                )
                namespaces.append(ns_data)
        # 追加主账号信息
        main_data = describe_user(request.user.root_user_id)
        if not main_data.get("user_set"):
            raise PermissionDeniedException(request.user.root_user_id)
        namespaces.append(Namespace(
            user=request.user.root_user_id,
            namespace=request.user.root_user_id.lower(),
            namespace_user_id=request.user.root_user_id,
            role="",
            email=main_data.get("user_set")[0]["email"]
        ))
        res_data = WorkGroupInfo(
            user=request.user.user_id,
            namespaces=namespaces,
            isClusterAdmin=False
        )
        return GenericSingleResponse[WorkGroupInfo](data=res_data)




@router.get("/user/workgroups",
            summary="获取用户的工作空间",
            response_model=GenericSingleResponse[WorkGroupInfo])
def get_workgroups(
        request: Request,
        background_tasks: BackgroundTasks,
        ufm_db: UFMInfoCrud = Depends(get_ufm_crud),
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取用户的工作组信息, 如果用户没有绑定工作组, 则创建一个工作组, 默认名称为用户的user_id.lower()
    :return:
    """
    kfam_client: KfamClient = KfamClient(request.user.user_id)
    try:
        workgroup: WorkGroupInfo = kfam_client.get_or_create_profile(user_db)
        # background_tasks.add_task(create_default_bucket, request.user)
        background_tasks.add_task(refresh_sub_user_permission, request.user)
        background_tasks.add_task(QingcloudDockerApiClient(request.user).create_user_and_repo)
        # ufm pkey init
        if app.settings.UFM_ENABLE:
            background_tasks.add_task(init_pkey, request.user, ufm_db)
        return GenericSingleResponse[WorkGroupInfo](data=workgroup)
    except Exception as e:
        logger.error(f"Failed to get workgroup info for user {request.user.user_id}.")
        logger.error(traceback.format_exc())
        return BaseErrorResponse()


@router.post("/user/init_resource",
            summary="初始化用户资源",
             response_model=GenericSingleResponse[WorkGroupInfo])
def init_resource(
        req: InitUserModel,
        background_tasks: BackgroundTasks,
        ufm_db: UFMInfoCrud = Depends(get_ufm_crud),
        user_db: UserCRUD = Depends(get_user_crud)
):
    aicp_user = AicpUser(user_id=req.init_user_id, user_name="", email="", role="")
    kfam_client: KfamClient = KfamClient(req.init_user_id)
    try:
        workgroup: WorkGroupInfo = kfam_client.get_or_create_profile(user_db)
        # background_tasks.add_task(create_default_bucket, aicp_user)
        background_tasks.add_task(refresh_sub_user_permission, aicp_user)
        # ufm pkey init
        if app.settings.UFM_ENABLE:
            background_tasks.add_task(init_pkey, aicp_user, ufm_db)
        return GenericSingleResponse[WorkGroupInfo](data=workgroup)
    except Exception as e:
        logger.error(f"Failed to get workgroup info for user {req.init_user_id}.")
        logger.error(traceback.format_exc())
        return BaseErrorResponse()


@router.get("/user/{user_id}",
            summary="获取用户信息",
            response_model=GenericSingleResponse[UserInfo])
def get_user_info(
        user_id: str,
        request: Request,
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取用户的详细信息, 仅kse aicp extension使用
    :param request:
    :param user_db:
    :return:
    """
    user: UserInfo = user_db.get_user_info_by_user_id(user_id)
    return GenericSingleResponse[UserInfo](data=user)


@router.get("/users",
            summary="获取用户列表",
            response_model=GenericMultipleResponse[Dict])
def get_aicp_users(
        query: AicpUserQueryParams = Depends(AicpUserQueryParams),
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    获取用户列表, 仅kse aicp extension使用
    """
    res_data = []
    count = 0
    maas_client = MaasClient()
    vgpu_model_infos = {}
    gpu_model_infos = {}
    sku = product_center_query_request(values=["container_instance", "inference_compute", "resource_group"], limit=500)
    for x in sku["skus"]:
        sku_id = x['sku_id']
        production = ProductCenterResource(sku_id)
        gpu_type = production.aipods_type.value
        if production.gpu_memory:
            gpu_memory = production.gpu_memory.name.rstrip("G")
            gpu_model = f"{production.gpu_model.attr_value} {gpu_memory}"
            if gpu_type == 'vGPU':
                vgpu_model_infos[gpu_model] = vgpu_model_infos.get(gpu_model, -1)
            elif gpu_type == 'only_gpu':
                gpu_model_infos[gpu_model] = gpu_model_infos.get(gpu_model, -1)
    if query.type is not None and query.type != "id":
        if query.input is None or query.input == "":
            return GenericMultipleResponse[Dict](data=res_data, counts=0)
        iaas_users = describe_user_by_type(query.type, query.input)
        if len(iaas_users["user_set"]) != 0:
            # Get db user.
            for iaas_user in iaas_users["user_set"]:
                aicp_user = user_db.get_user_info_by_user_id(iaas_user.get("user_id"))
                resource = user_db.get_user_resource_num(iaas_user.get("user_id"))
                if aicp_user is not None:
                    maas_data = maas_client.get_maas_server_num(iaas_user.get("user_id"))
                    maas_server_num = 0
                    gpu_num = 0
                    vgpu_num = 0
                    mass_gpu_models = {}
                    mass_vgpu_models = {}
                    if maas_data is not None:
                        maas_server_num = maas_data.get(iaas_user.get("user_id"), {}).get('inference_service_count', 0)
                        mass_gpu_models = maas_data.get(iaas_user.get("user_id"), {}).get('gpu', {})
                        mass_vgpu_models = maas_data.get(iaas_user.get("user_id"), {}).get('vgpu', {})
                    for key in aicp_user.gpu_model_quotas:
                        original_value = aicp_user.gpu_model_quotas[key]
                        resource_value = resource[5].get(key, 0)
                        mass_value = mass_gpu_models.get(key, 0)
                        resource_value = resource_value + mass_value
                        if isinstance(original_value, list) and len(original_value) == 2:
                            original_value[1] = resource_value
                        else:
                            aicp_user.gpu_model_quotas[key] = [original_value, resource_value]
                        gpu_num += resource_value

                    # 处理 vgpu 用量
                    for key in aicp_user.vgpu_model_quotas:
                        original_value = aicp_user.vgpu_model_quotas[key]
                        resource_value = resource[6].get(key, 0)
                        mass_value = mass_vgpu_models.get(key, 0)
                        resource_value = resource_value + mass_value
                        if isinstance(original_value, list) and len(original_value) == 2:
                            original_value[1] = resource_value
                        else:
                            aicp_user.vgpu_model_quotas[key] = [original_value, resource_value]
                        vgpu_num += resource_value
                    docker_used, dir_used, stor_used, project_used = 0, 0, 0, 0
                    if resource:
                        if 'docker_used' in resource[7]:
                            docker_used = resource[7]['docker_used']
                        if 'dir_number' in resource[7]:
                            dir_used = resource[7]['dir_number']
                        if 'stor_used' in resource[7]:
                            stor_used = resource[7]['stor_used']
                        if 'project_used' in resource[7]:
                            project_used = resource[7]['project_used']
                    logger.info(f"当前数据为{resource}, {maas_data}")
                    item = AicpUserInfo(
                        aicp_user.namespace,
                        aicp_user.user_id,
                        email=iaas_user.get("email"),
                        phone=iaas_user.get("phone"),
                        status=iaas_user.get("status"),
                        type=iaas_user.get("user_type"),
                        notify_email=iaas_user.get("notify_email"),
                        containers=resource[0],
                        jobs=resource[1],
                        resource_nodes=resource[2],
                        quota_containers=aicp_user.containers_number,
                        quota_jobs=aicp_user.jobs_number,
                        quota_resource_nodes=aicp_user.rg_nodes,
                        quota_maas_server=aicp_user.maas_server,
                        priority=aicp_user.priority,
                        dir_number=aicp_user.dir_number,
                        capacity=aicp_user.capacity,
                        maas_server=maas_server_num,
                        quota_gpu_number=aicp_user.gpu_number,
                        quota_gpu_model_quotas=aicp_user.gpu_model_quotas,
                        quota_vgpu_number=aicp_user.vgpu_number,
                        quota_vgpu_model_quotas=aicp_user.vgpu_model_quotas,
                        docker_quota=aicp_user.docker_quota,
                        gpu_count=resource[3],
                        vgpu_count=vgpu_num,
                        docker_used=docker_used,
                        dir_used=dir_used,
                        stor_used=stor_used,
                        project_number=aicp_user.project_number,
                        project_used=project_used
                    )

                    count = count + 1
                    res_data.append(item.__dict__)
        return GenericMultipleResponse[Dict](data=res_data, counts=count)
    logger.info("List kse aicp extension list users.")
    count, aicp_users = user_db.page_users(query)
    for aicp_user in aicp_users:
        iaas_user = describe_user_without_redis(aicp_user.user_id)
        resource = user_db.get_user_resource_num(aicp_user.user_id)
        logger.info(f"当前数据库数据为{aicp_user.gpu_model_quotas}, {aicp_user.vgpu_model_quotas}")
        if aicp_user.gpu_model_quotas is None:
            aicp_user.gpu_model_quotas = gpu_model_infos
        if aicp_user.vgpu_model_quotas is None:
            aicp_user.vgpu_model_quotas = vgpu_model_infos

        maas_data = maas_client.get_maas_server_num(aicp_user.user_id)
        maas_server_num = 0
        gpu_num = 0
        vgpu_num = 0
        mass_gpu_models = {}
        mass_vgpu_models = {}
        if maas_data is not None:
            maas_server_num = maas_data.get(aicp_user.user_id, {}).get('inference_service_count', 0)
            mass_gpu_models = maas_data.get(aicp_user.user_id, {}).get('gpu', {})
            mass_vgpu_models = maas_data.get(aicp_user.user_id, {}).get('vgpu', {})
        logger.info(f"当前数据库数据为{aicp_user.gpu_model_quotas}, {aicp_user.vgpu_model_quotas},{mass_gpu_models},{mass_vgpu_models},{maas_server_num}")

        # 处理gpu 用量
        for key in aicp_user.gpu_model_quotas:
            original_value = aicp_user.gpu_model_quotas[key]
            resource_value = resource[5].get(key, 0)
            mass_value = mass_gpu_models.get(key, 0)
            resource_value = resource_value + mass_value
            if isinstance(original_value, list) and len(original_value) == 2:
                original_value[1] = resource_value
            else:
                aicp_user.gpu_model_quotas[key] = [original_value, resource_value]
            gpu_num += resource_value

        # 处理 vgpu 用量
        for key in aicp_user.vgpu_model_quotas:
            original_value = aicp_user.vgpu_model_quotas[key]
            resource_value = resource[6].get(key, 0)
            mass_value = mass_vgpu_models.get(key, 0)
            resource_value = resource_value + mass_value
            if isinstance(original_value, list) and len(original_value) == 2:
                original_value[1] = resource_value
            else:
                aicp_user.vgpu_model_quotas[key] = [original_value, resource_value]
            vgpu_num += resource_value
        docker_used, dir_used, stor_used,project_used = 0, 0, 0, 0
        if resource:
            if 'docker_used' in resource[7]:
                docker_used = resource[7]['docker_used']
            if 'dir_number' in resource[7]:
                dir_used = resource[7]['dir_number']
            if 'stor_used' in resource[7]:
                stor_used = resource[7]['stor_used']
            if 'project_used' in resource[7]:
                project_used = resource[7]['project_used']
        item = AicpUserInfo(
            aicp_user.namespace,
            aicp_user.user_id,
            containers=resource[0],
            jobs=resource[1],
            resource_nodes=resource[2],
            quota_containers=aicp_user.containers_number,
            quota_jobs=aicp_user.jobs_number,
            quota_resource_nodes=aicp_user.rg_nodes,
            quota_maas_server=aicp_user.maas_server,
            priority=aicp_user.priority,
            dir_number=aicp_user.dir_number,
            capacity=aicp_user.capacity,
            maas_server=maas_server_num,
            quota_gpu_number=aicp_user.gpu_number,
            quota_gpu_model_quotas=aicp_user.gpu_model_quotas,
            quota_vgpu_number=aicp_user.vgpu_number,
            quota_vgpu_model_quotas=aicp_user.vgpu_model_quotas,
            docker_quota=aicp_user.docker_quota,
            gpu_count=gpu_num,
            vgpu_count=vgpu_num,
            docker_used=docker_used,
            dir_used=dir_used,
            stor_used=stor_used,
            project_number=aicp_user.project_number,
            project_used=project_used
        )
        # 防止测试数据获取不到iaas信息
        item.type = 0
        item.email = "DEBUG User."
        item.phone = "DEBUG User."
        item.status = "active"
        if len(iaas_user["user_set"]) != 0:
            item.email = iaas_user["user_set"][0].get("email")
            item.phone = iaas_user["user_set"][0].get("phone")
            item.status = iaas_user["user_set"][0].get("status")
            item.type = iaas_user["user_set"][0].get("user_type")
            item.notify_email = iaas_user["user_set"][0].get("notify_email")
        res_data.append(item.__dict__)
    return GenericMultipleResponse[Dict](data=res_data, counts=count)


@router.post("/user/quota",
             summary="修改用户配额",
             response_model=BaseGenericResponse)
def modify_user_quota(
        query: AicpUserQuotaParams,
        user_db: UserCRUD = Depends(get_user_crud)
):
    """
    修改用户配额, 仅管理端使用
    """
    logger.info("修改用户配额")
    if not (1 <= query.priority <= 10):
        logger.warning(f"优先级值未在规定范围内")
        raise UserPriorityException()
    current_quota = user_db.get_user_info_by_user_id(query.user_id)
    # 处理 GPU 和 vGPU 配额,数量与型号互斥
    query.gpu_number, query.gpu_model_quotas = user_db.reconcile_quota_numbers(
        query.gpu_number,
        query.gpu_model_quotas,
        current_quota.gpu_number,
        current_quota.gpu_model_quotas
    )

    query.vgpu_number, query.vgpu_model_quotas = user_db.reconcile_quota_numbers(
        query.vgpu_number,
        query.vgpu_model_quotas,
        current_quota.vgpu_number,
        current_quota.vgpu_model_quotas
    )
    user_db.update_quota(query.user_id, query.containers, query.jobs, query.rg_nodes, query.priority, query.dir_number,
                         query.capacity, query.maas_server, query.gpu_number, query.vgpu_number, query.docker_quota,
                         query.gpu_model_quotas, query.vgpu_model_quotas, query.project_number)
    return BaseGenericResponse()


@router.delete("/user/freeze",
               summary="删除用户",
               response_model=BaseGenericResponse)
def freeze_user(
        req: UserIdModel
):
    """
    冻结用户, 仅管理端使用
    """
    logger.info("冻结用户")
    delete_user(req.user_ids)
    return BaseGenericResponse()


@router.post("/user/freeze",
             summary="解禁用户",
             response_model=BaseGenericResponse)
def unfreeze_user(
        req: UserIdModel
):
    """
    解禁用户, 仅管理端使用
    """
    logger.info("解禁用户")
    restore_user(req.user_ids)
    return BaseGenericResponse()


@router.get("/subuser",
            summary="查询子账户",
            response_model=GenericMultipleResponse[UserBaseInfo])
def get_subuser(
        request: Request,
        offset: int = 0,
        limit: int = 0
):
    '''
    查询子账户
    '''
    subusers = describe_sub_users(request.user.user_id,
                                  request.user.access_key_id,
                                  request.user.secret_access_key,
                                  offset, limit)
    data = []
    for user in subusers.get("user_set", []):
        data.append(UserBaseInfo(**user))
    counts = subusers["total_count"]
    return GenericMultipleResponse[UserBaseInfo](data=data, counts=counts)


@router.get("/project/category",
            summary="查询项目分类",
            response_model=GenericMultipleResponse[ProjectCategory])
def get_project_category(
        request: Request,
        user_db: UserCRUD = Depends(get_user_crud)
):
    '''
    查询项目分类
    :param request:
    :return:
    '''
    categories = user_db.get_project_category()
    if not categories:
        categories.append(ProjectCategory(id=-1, name="其他", key="other", owner='system'))
    return GenericMultipleResponse[ProjectCategory](data=categories, counts=len(categories))


@router.delete("/project/category",
               summary="删除项目分类",
               response_model=BaseGenericResponse)
def delete_project_category(
        category_id: int,
        user_db: UserCRUD = Depends(get_user_crud)
):
    '''
    删除项目分类
    :param category_id:
    :param user_db:
    :return:
    '''
    user_db.delete_project_category(category_id)
    return BaseGenericResponse()


@router.post("/project/category",
             summary="创建项目分类",
             response_model=BaseGenericResponse)
def create_project_category(
        body: ProjectCategoryBase,
        user_db: UserCRUD = Depends(get_user_crud)
):
    '''
    创建项目分类
    :param category:
    :param user_db:
    :return:
    '''
    user_db.create_project_category(body)
    return BaseGenericResponse()


@router.get("/team/quota",
            summary="查询团队资源配额",
            response_model=GenericMultipleResponse[Dict])
def get_team_quota(user_ids: str = Query(''), user_db: UserCRUD = Depends(get_user_crud)):
    """
    查询团队资源配额
    """
    res_data = user_db.get_team_quota(user_ids.split(','))
    return GenericMultipleResponse[Dict](data=res_data, counts=len(res_data))


@router.get("/team/quota/detail",
            summary="查询资源配额使用详情",
            response_model=GenericMultipleResponse[Dict])
def get_team_quota_detail(user_ids: str = Query(''),
                          resource_type: str = None,
                          user_db: UserCRUD = Depends(get_user_crud)):
    """
    查询团队资源配额详情
    """
    res_data = user_db.get_team_quota_detail(user_ids.split(','), resource_type)
    return GenericMultipleResponse[Dict](data=res_data, counts=len(res_data))