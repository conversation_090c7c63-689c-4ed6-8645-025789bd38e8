import asyncio
import logging

logger = logging.getLogger(__name__)


async def k8s_stream_thread(websocket, container_stream, close_callback=None):
    try:
        while container_stream.is_open():
            if container_stream.peek_stdout():
                stdout = container_stream.read_stdout()
                await websocket.send_text(stdout)
            if container_stream.peek_stderr():
                stderr = container_stream.read_stderr()
                await websocket.send_text(stderr)
    except Exception as e:
        logger.error(f"Stream thread error: {e}")
    finally:
        if close_callback:
            close_callback()
        try:
            await websocket.close()
        except Exception:
            pass


def between_callback(*args, **kwargs):
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)

    try:
        loop.run_until_complete(k8s_stream_thread(*args, **kwargs))
    finally:
        loop.close()
