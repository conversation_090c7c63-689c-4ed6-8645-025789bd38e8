import re
import threading
from typing import Dict

import kr8s
from fastapi import APIRouter, Depends, WebSocket, status as http_status
from kr8s import NotFoundError
from kr8s.objects import PersistentVolumeClaim
from kubernetes.stream import stream
from kubernetes.stream.ws_client import WSClient

from app.core.kube.api.apis import v1_core
from app.core.loggers import logger
from app.core.response import GenericMultipleResponse, GenericSingleResponse
import app
from .exceptions import ResourceNotFoundException
from .utils import between_callback
from ...core.exceptions import PermissionDeniedForNamespaceException
from ...depends.authz import PermissionLevel
from ...depends.nb_group import notebook_has_permission, notebook_namespace_permission
from ...depends.socket_group import get_namespace

# api prefix: /aicp/user
router = APIRouter(prefix="/kapi/v1", tags=["kubernetes"])


@router.get(
    "/namespaces/{namespace}/persistentvolumeclaims/{pvc_name}",
    response_model=GenericSingleResponse[Dict],
    status_code=http_status.HTTP_200_OK,
    dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))]
)
def get_persistent_volume_claim(
        pvc_name: str,
        namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ))
):
    """
    获取作业的pod, 仅限于未终止的作业
    """
    try:
        pvc = PersistentVolumeClaim.get(pvc_name, namespace=namespace)
        return GenericSingleResponse[Dict](data=pvc.raw)
    except NotFoundError as e:
        logger.error(f"pvc {pvc_name} not found in namespace {namespace}")
        raise ResourceNotFoundException(pvc_name)


mpf = re.compile(app.settings.MANUAL_PVC_FILTER)


# get manu pvcs
@router.get("/namespaces/{namespace}/persistentvolumeclaims",
            response_model=GenericMultipleResponse[str],
            status_code=http_status.HTTP_200_OK,
            dependencies=[Depends(notebook_has_permission(permission_level=PermissionLevel.READ))])
def get_namespace_persistent_volume_claims(namespace: str = Depends(notebook_namespace_permission(permission_level=PermissionLevel.READ))):
    """
    获取namespace下的PVC, 仅支持手动创建的PVC.
    一般仅私有化环境使用, 其他环境返回空列表.
    """
    if not app.settings.MANUAL_PVC:
        return GenericMultipleResponse[str](data=[])
    persistent_volume_claims = PersistentVolumeClaim.list(namespace=namespace)
    if not persistent_volume_claims:
        return GenericMultipleResponse[str](data=[])

    pvc_names = [x.raw["metadata"]["name"] for x in persistent_volume_claims if
                 not mpf.match(x.raw["metadata"]["name"])]
    return GenericMultipleResponse[str](data=pvc_names)


@router.websocket("/ws/namespaces/{namespace}/pods/{pod_name}/container/{container_name}/exec")
async def exec_command(
        pod_name: str,
        container_name: str,
        websocket: WebSocket,
        namespace: str
):
    """
    websocket 执行命令
    """
    # 权限判断
    namespace = get_namespace(websocket, pod_name, namespace)
    logger.debug(f"connect to pod {pod_name} container {container_name} in namespace {namespace}")
    await websocket.accept()

    terminal_stream: WSClient = stream(v1_core.connect_get_namespaced_pod_exec,
                                       pod_name,
                                       namespace,
                                       command=[
                                           "sh",
                                           "-c",
                                           "if [ -x /bin/bash ]; then /bin/bash; else /bin/sh; fi"
                                       ],
                                       container=container_name,
                                       stderr=True,
                                       stdin=True,
                                       stdout=True,
                                       tty=True, _preload_content=False)

    websocket_closed = False

    def close_websocket_safely():
        nonlocal websocket_closed
        if not websocket_closed:
            websocket_closed = True
            try:
                terminal_stream.write_stdin('exit\r')
                terminal_stream.close()
            except Exception:
                pass

    threading.Thread(target=between_callback, args=(websocket, terminal_stream, close_websocket_safely)).start()

    try:
        while not websocket_closed:
            data = await websocket.receive_text()
            terminal_stream.write_stdin(data)
    except Exception as e:
        if e.code in [1000, 1001, 1005]:
            logger.info(f"user closed the web terminal of container {container_name}.")
        else:
            logger.error(f"websocket error: {e}")
            close_websocket_safely()
            if not websocket_closed:
                try:
                    await websocket.close()
                except Exception:
                    pass
