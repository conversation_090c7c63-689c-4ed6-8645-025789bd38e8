from typing import List, Dict
from datetime import  datetime
from pydantic import BaseModel
from sqlmodel import SQLModel, Field

from app.core.models import UserIdModel

class DataSetListBase(SQLModel, table=False):
    da_id: str = Field(default=None, primary_key=True, description='数据集ID')
    data_name: str = Field('',description='数据集名称')
    data_size: str = Field('', description='数据集大小')
    data_type: int = Field(1, description='数据集类型')
    storage_path: str = Field('', description='存储路径')
    notebook_path: str = Field('', description='实例路径')
    publisher: str = Field('', description='发布方')
    owner: str = Field('', description='创建者')
    description: str = Field('', description='描述')
    create_time: datetime = Field('', description="创建时间")
    user_id: str = Field(default=None, description="用户")
    root_user_id: str = Field(default=None, description="用户")


class DataSet(DataSetListBase, UserIdModel, table=True):
    __tablename__ = 'data_set'



class DataSetCreate(BaseModel):
    data_name: str = ''
    data_size: str = ''
    data_type: int = 1
    storage_path: str = ''
    notebook_path: str = ''
    publisher: str = ''
    description: str = ''
    da_id: str = None


class DataSetListRep(DataSetListBase):
    total: int = 0


class DataSetResponse(BaseModel):
    data: Dict[str, List[Dict]]