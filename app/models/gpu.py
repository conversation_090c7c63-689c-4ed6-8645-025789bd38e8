from typing import Optional

from pydantic import BaseModel
from sqlmodel import SQLModel, Field
from app.core.models import ActiveRecordMixin, TimestampModel


class GpuMaintainLogBase(SQLModel, table=False):
    ml_id: str = Field(primary_key=True)
    gpu_node_id: str = Field('', description="被记录GPU节点的ID")
    maintainer_id: str = Field('', description="记录人ID")
    maintainer_name: str = Field('', description="记录人姓名")
    description: str = Field('', description="事件描述")


class GpuMaintainLog(GpuMaintainLogBase, TimestampModel, table=True):
    __tablename__ = "gpu_maintain_log"


class NodeStaticInfo(ActiveRecordMixin, table=True):
    __tablename__ = "node_static_info"
    hostname: str = Field(primary_key=True)
    ip: str = Field('', nullable=True, description="管理网络ip")
    cpu: int = Field(0, nullable=True, description="cpu核心数")
    cpu_model: str = Field("intel", nullable=True, description="cpu型号")
    memory: int = Field(0, description="内存")
    gpu_model: str = Field('nvidia', description="gpu型号")
    gpu_memory: int = Field(0, nullable=True, description="显存")
    gpu: int = Field(0, nullable=True, description="gpu数量")
    ib_bw_compute: int = Field(0, nullable=True, description="计算ib带宽")
    ib_count_compute: int = Field(0, nullable=True, description="计算ib卡数量")
    ib_bw_storage: int = Field(0, nullable=True, description="存储ib网络带宽")
    ib_count_storage: int = Field(0, nullable=True, description="存储ib网卡个数")
    ib_bw_manager: int = Field(0, nullable=True, description="管理ib网络带宽")
    ib_count_manager: int = Field(0, nullable=True, description="管理ib网卡个数")
    cluster: str = Field('', nullable=True, description="所属集群")
    compute_group: str = Field('', nullable=True, description="所属计算池")
    ipmi: str = Field('', nullable=True, description="ipmi地址")
    ipmi_username: str = Field('', nullable=True, description="ipmi用户名")
    ipmi_password: str = Field('', nullable=True, description="ipmi密码")
    product_company: str = Field('', nullable=True, description="服务器厂商")
    hardware_model: str = Field('', nullable=True, description="服务器型号")
    serial_number: str = Field('', nullable=True, description="服务器序列号")
    zone: str = Field('', nullable=True, description="所属区域")
    location_number: str = Field('', nullable=True, description="机柜或机架编号")
    role: str = Field('', nullable=True, description="角色")
    nvlink: int = Field(0, nullable=True, description="NVLINK 0-没有，1-有")
    overlay2_size: int = Field(0, nullable=True, description="overlay2存储大小", sa_column_kwargs={"server_default": "0"})


class GpuStaticInfo(ActiveRecordMixin, table=True):
    __tablename__ = "gpu_static_info"
    gpu_uuid: str = Field(primary_key=True, description="gpu uuid全局唯一")
    hostname: str = Field(index=True)
    index: int = Field(0, nullable=True, description="gpu在节点上的索引")
    gpu_model: str = Field('', nullable=True, description="gpu型号")
    compute_group: str = Field('', nullable=True, description="所属计算池")
    gpu_memory: int = Field(0, nullable=True, description="显存")


class IbDevInfo(SQLModel, table=True):
    __tablename__ = "ib_dev_info"
    guid: str = Field(primary_key=True, description="guid全局唯一")
    hostname: str = Field(index=True)
    dev_name: str = Field('', nullable=True, description="网卡名称")
    ib_bw: int = Field(0, nullable=True, description="网卡带宽，单位Gbit/s")
    pkey: str = Field('', nullable=True, description="pkey")
    status: str = Field('', nullable=True, description="状态")

GpuUnschedulableXids = ["79", "94"]

#  关联 xid_codes 表和 static_info 表
class GpuFaultRecordsBase(SQLModel, table=False):
    records_id: str = Field(primary_key=True)
    gpu_node_id: str = Field('', description="故障 GPU 节点的 ID")
    gpu_uuid: str = Field('', description="故障 GPU 网卡的 UUID")
    gpu_xid: str = Field('', description="GPU 故障 xid")
    gpu_model_name: str = Field('', description="GPU 型号")
    gpu_nvml_version: str = Field('', description="gpu nvml 版本号")
    gpu_cuda_driver_version: str = Field('', description="GPU 的 cuda 驱动版本号")
    gpu_device: str = Field('', description="GPU 的设备号")
    gpu_alert_summary: str = Field('', description="告警内容")
    fault_status: str = Field("0", description="0-未处理，1-已处理")
    fault_treatment: str = Field('', description="实际故障处理记录")
    fault_maintainer_id: str = Field('', description="故障处理人ID")
    fault_maintainer_name: str = Field('', description="故障处理人姓名")


class GpuFaultRecords(GpuFaultRecordsBase, TimestampModel, table=True):
    __tablename__ = "gpu_fault_records"


#  gpu_error_codes 错误字典表
class GpuErrorCodesBase(SQLModel, table=False):
    code_id: str = Field(primary_key=True)
    gpu_err_id: str = Field('0', description="GPU 错误统一码")
    gpu_xid: str = Field('0', description="GPU 故障 xid")
    gpu_err_priority: str = Field('normal', description="故障紧急程度，Info|Warning|Critical|Error")
    gpu_err_desc: str = Field('', description="GPU 故障错误描述")
    gpu_suggestions: str = Field('', description="GPU 故障处理建议")
    gpu_err_strategy: str = Field('0', sa_column_kwargs={
        "server_default": "0"
    }, description="GPU 故障时策略， "
                   "0 - 不做处理|"
                   "1 - 禁止调度、排干、标签污点|"
                   "2 - 禁止调度、排干、标签污点，重启故障节点")
    gpu_product: Optional[str] = Field('nvidia', nullable=True, sa_column_kwargs={
        "server_default": "nvidia"
    }, description="GPU 产商")


class GpuErrorCodes(GpuErrorCodesBase, TimestampModel, table=True):
    __tablename__ = "gpu_error_codes"


class GpuDashboardConfigBase(SQLModel, table=False):
    dashboard_id: str = Field(primary_key=True)
    dashboard_name: str = Field('', description="Grafana Dashboard 名称")
    dashboard_url_path: str = Field('', description="Grafana Dashboard uid")
    web_router: str = Field('', description="智算前端路由地址和 Dashboard 名称")
    grafana_address: str = Field('', description="Grafana 地址，格式为 node_ip:nodeport")
    enable_dashboard: str = Field('0', description="是否启用 Dashboard 0 - 不启用| 1 - 启用")
    enable_table_view: str = Field('0', description="是否启用 Dashboard 关联的 tab 页签 0 - 不启用| 1 - 启用")
    grafana_params: str = Field('', sa_column_kwargs={
        "server_default": ""
    }, description="Grafana Dashboard url 参数")

class GpuDashboardConfig(GpuDashboardConfigBase, TimestampModel, table=True):
    __tablename__ = "gpu_dashboard_config"