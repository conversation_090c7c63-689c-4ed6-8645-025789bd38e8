from typing import List

from sqlmodel import SQLModel, Field

from app.core.models import TimestampModel, StatusModel, ActiveRecordMixin, ReplicaSpecCreate


class FinetuningBase(SQLModel, table=False):
    name: str = Field('', nullable=True, description="模型精调任务名称")
    model_name_or_path: str =  Field('', nullable=True, description="模型文件路径，用户私有模型")
    model_id: str = Field('', nullable=True, description="模型广场中的模型id")
    stage: str  =  Field('sft', nullable=True, description="pt, sft(Supervised Fine-Tuning),PPO, DPO, KTO, ORPO")
    do_train: str  =  Field('true', nullable=True, description="true用于训练, false用于评估")
    finetuning_type: str  =  Field('full', nullable=True, description="训练方式 lora ,full")
    dataset: str =  Field('', nullable=True, description="使用的数据集”,”分隔多个数据集")
    eval_dataset: str =  Field('', nullable=True, description="验证数据集”,”分隔多个数据集")
    val_size: str =  Field('0', nullable=True, description="验证数据集切分比例”,”分隔多个数据集")
    lora_target: str = Field('all', nullable=True, description="采取LoRA方法的目标模块，默认值为 all")
    template: str = Field('qwen2', nullable=True, description="数据集模板，请保证数据集模板与模型相对应,llama3 qwen deepseek")
    output_dir: str =  Field('', nullable=True, description="输出路径")
    logging_steps: int = Field(10, nullable=True, description="日志输出步数间隔")
    save_steps: int = Field(10000, nullable=True, description="模型断点保存间隔")
    eval_steps: int = Field(500, nullable=True, description="验证步数")
    cutoff_len: int = Field(4096, nullable=True, description="序列长度")
    overwrite_output_dir: bool = Field('true', nullable=True, description="是否允许覆盖输出目录")
    per_device_train_batch_size: int = Field(16, nullable=True, description="每个设备上训练的批次大小")
    gradient_accumulation_steps: int = Field(10, nullable=True, description="梯度积累步数")
    max_grad_norm: int = Field(0, nullable=True, description="梯度裁剪阈值")
    learning_rate: str = Field('1.0e-5', nullable=True, description="学习率")
    lr_scheduler_type: str = Field('cosine', nullable=True, description="学习率曲线，可选 linear, cosine, polynomial, constant 等。")
    num_train_epochs: int = Field(3, nullable=True, description="训练周期数")
    bf16: bool = Field('true', nullable=True, description="是否使用 bf16 格式")
    warmup_ratio: str = Field('', nullable=True, description="学习率预热比例")
    warmup_steps: str = Field('', nullable=True, description="学习率预热步数")
    sku_id: str = Field('', nullable=True, description="微调使用规格id")
    node_count: str = Field('', nullable=True, description="节点个数")
    fileset: str = Field("", nullable=True, description="共享存储目录")
    deepspeed: str = Field("ZeRO-3", nullable=True, description="deepspeed加速模式, ZeRO-0,ZeRO-2,ZeRO-2+offload,ZeRO-3,ZeRO-3+offload")
    acceleration: str = Field("", nullable=True, description="加速算法,flash_attn,use_unsloth,enable_liger_kernel")
    command: str = Field("", nullable=True)
    save_model:  bool = Field('false', nullable=True, description="是否保存到模型管理")


class FinetuningParams(FinetuningBase):
    replica_specs: List[ReplicaSpecCreate] = Field(..., description="副本配置列表")

class ReqFinetuningParams(FinetuningBase, table=False):
    cpu:  int = Field('', nullable=True, description="cpu个数")
    memory: int = Field('', nullable=True, description="memory")
    gpu: int = Field('', nullable=True, description="gpu个数")
    gpu_model: str = Field('', nullable=True, description="gpu型号")





class Finetuning(ReqFinetuningParams, TimestampModel, StatusModel, ActiveRecordMixin, table=True):
    __tablename__ = "finetuning"
    ft_task_id: str = Field(primary_key=True)



class Dataset(SQLModel, table=False):
    dataset: List[str] = []
    model_id: str = ''
    model_name_or_path: str = ''