import enum
import uuid
from enum import Enum
from typing import List, Optional, Set
import pydantic
from fastapi import Query
from pydantic import BaseModel, validator
from sqlalchemy import Column, String, text, ARRAY
from sqlmodel import SQLModel, Field

from app.core.models import ReplicaSpecBase, TimestampModel, UserIdModel, StatusModel, ActiveRecordMixin, \
    UserPatchInfoModel
from app.core.response import BaseGenericResponse
from app.core.utils import gpu_manager


class PodTypeEnum(enum.Enum):
    JOB = "job"
    INSTANCE = "instance"
    INFER = "infer"


pod_type_emu = {"instance", "job", "infer"}


class ResourceGroupBase(SQLModel, table=False):
    rg_id: str = Field(primary_key=True)
    name: str = Field('', description="资源名称")
    resource_type: str = Field('', description="资源类型")
    description: str = Field('', description="资源概述")
    pod_type: Optional[str] = Field('', description="任务类型", nullable=True)
    customize_enable: Optional[int] = Field(1, description="是否开启自定义规格", nullable=True)

    # @validator('pod_type')
    # def make_pod_type(cls, v: str, values: dict) -> List[str]:
    #     return values["pod_type"].split(",")


class ResourceNodeBase(SQLModel, table=False):
    rg_node_id: str = Field(primary_key=True)
    hostname: str = Field(nullable=False, description="节点hostname")
    name: str = Field('', description="节点名称")
    rg_id: str = Field(nullable=False, description="节点所在资源组", index=True)
    cpu: int = Field(description="节点cpu")
    memory: int = Field(description="节点内存")
    cpu_model: str = Field('', description="cpu型号")
    gpu: int = Field(0, description="节点gpu")
    gpu_memory: int = Field(0, description="节点显存")
    gpu_model: str = Field('', description="gpu型号")
    gpu_name: Optional[str] = Field("", nullable=True, description="gpu名称, 实际显示在前端页面的")
    nvlink: bool = Field('', description="是否支持nvlink")
    network: int = Field(0, description="网络带宽")
    os_disk: int = Field(nullable=True, description="系统盘数容量")
    disk: int = Field(0, description="数据盘容量")
    node_type: str = Field('', description="节点类型")
    during: int = Field(nullable=False, description="包年包月时长")
    auto_renewal: int = Field(nullable=False, description="是否自动续约")
    sku_id: str = Field(nullable=False, description="产品类型id")
    user_id: str = Field('', nullable=True, index=True)
    root_user_id: str = Field("", nullable=True, index=True)
    billing_order_id: Optional[str] = Field("", nullable=True, description="计费订单id")
    is_reused_gpu_node: bool = Field(False, description="是否是复用的GPU节点", sa_column_kwargs={
        "server_default": text("false")
    })
    is_vgpu_node: bool = Field(False, description="是否是vgpu节点", sa_column_kwargs={
        "server_default": text("false")
    })


class ResourceNodeStatusBase(SQLModel, table=False):
    node_id: str = Field(nullable=False, primary_key=True, description="节点hostname")
    total_cpu: int = Field(description="节点总cpu")
    ava_cpu: int = Field(description="节点当前可用cpu")
    total_gpu: int = Field(0, nullable=True, description="节点总gpu")
    ava_gpu: int = Field(0, nullable=True, description="节点可用gpu")
    total_memory: int = Field(0, nullable=True, description="节点总内存")
    ava_memory: int = Field(0, nullable=True, description="节点可用内存")
    total_os_disk: int = Field(0, nullable=True, description="节点总系统盘")
    ava_os_disk: int = Field(0, nullable=True, description="节点可用系统盘")
    total_data_disk: int = Field(0, nullable=True, description="节点总数据盘")
    ava_data_disk: int = Field(0, nullable=True, description="节点可用数据盘")
    worker_node_type: str = Field("cpu", nullable=True, description="节点类型,CPU或者GPU")
    uid: str = Field("-", nullable=True, description="节点uuid,由K8S生成")
    ib_enabled: int = Field(0, nullable=True, description="是否启用 IB 网络，0 - 未启用，1 - 启用")
    gpu_product: str = Field("nvidia", nullable=True, description="GPU产品所属厂商")
    vendor: str = Field("", nullable=True, description="节点类型")


class ResourceGroupShare(TimestampModel, StatusModel, ActiveRecordMixin, table=True):
    __tablename__ = "resource_group_share"
    id: int = Field(primary_key=True)
    rg_id: str = Field(nullable=False, description="节点所在资源组", index=True)
    owner: str
    share_user_id: str


class TemplateSpecType(str, Enum):
    """
    资源模板类型
    """
    common = "common"
    share_gpu = "share_gpu"
    vGPU = "vGPU"
    only_cpu = "only_cpu"


class ResourceTemplateBase(SQLModel, table=False):
    name: Optional[str] = Field('', description="名称", nullable=True)
    cpu: int
    memory: int
    gpu: int = Field(0, description="gpu数量")
    gpu_memory: int = Field(0, description="gpu显存")
    rg_id: str
    ib: int = Field(0, description="是否支持infiniband")
    rg_node_id: Optional[str] = Field('', description="节点id", nullable=True)
    pod_type: Optional[str] = Field('', description="任务类型", nullable=True)
    gpu_list: Optional[Set[str]] = Field(default=None, sa_column=Column(ARRAY(String())),
                                         description="限制使用的, gpu列表")
    gpu_name: Optional[str] = Field(None, description="gpu型号名称, 对应节点的gpu_name", nullable=True)
    show_user: Optional[Set[str]] = Field(default=None, sa_column=Column(ARRAY(String())),
                                          description="可以查看的用户列表")
    hashrate_allocation: Optional[int] = Field(0, description="算力分配")
    data_disk_size: Optional[int] = Field(0, description="数据盘大小", nullable=True,
                                          sa_column_kwargs={"server_default": "0"})
    cpu_manufacturer: Optional[str] = Field(None, description="cpu厂商", nullable=True)
    cpu_model: Optional[str] = Field(None, description="cpu型号", nullable=True)

    # common / share_gpu / vgpu / only_cpu
    # 上线更新数据库后需要更新该字段,
    # update resource_template set spec_type = 'share_gpu' where gpu_list is not null and array_length(gpu_list, 1) > 0;
    spec_type: Optional[str] = Field("common", description="资源类型", nullable=True,
                                     sa_column_kwargs={"server_default": "common"})

    def to_spec(self, **kwargs) -> dict:
        return dict(
            custom_cpu=self.cpu, custom_memory=self.memory, custom_gpu=self.gpu, custom_gpu_list=self.gpu_list,
            custom_infiniband=self.ib, custom_gpu_memory=self.gpu_memory, rg_id=self.rg_id,
            specs=self.rg_node_id if self.rg_node_id else kwargs.get("specs", ""), custom_gpu_name=self.gpu_name,
            custom_aipods_type=self.spec_type, custom_hashrate_allocation=self.hashrate_allocation,
            custom_data_disk_size=self.data_disk_size, cpu_manufacturer=self.cpu_manufacturer,
            cpu_model=self.cpu_model
        )



class ResourceTemplate(ResourceTemplateBase, TimestampModel, StatusModel, UserIdModel, ActiveRecordMixin, table=True):
    tmp_id: int = Field(primary_key=True)
    __tablename__ = "resource_template"

    def to_sku(self, gpu_map: dict[str, str]) -> dict:
        return {
            "sku_id": f"{self.tmp_id}-{self.name}",
            "spec_type": self.spec_type,
            "filters": [
                {
                    "attr_id": "cpu_count",
                    "attr_value": self.cpu
                },
                {
                    "attr_id": "memory",
                    "attr_value": self.memory
                },
                {
                    "attr_id": "gpu_memory",
                    "attr_value": self.gpu_memory
                },
                {
                    "attr_id": "gpu_model",
                    "attr_value": gpu_map.get(self.gpu_name, "")
                },
                {
                    "attr_id": "aipods_type",
                    "attr_value": self.rg_id
                },
                {
                    "attr_id": "gpu_count",
                    "attr_value": self.gpu
                },
                {
                    "attr_id": "resource_group",
                    "attr_value": self.rg_id
                },
                {
                    "attr_id": "resource_group_node",
                    "attr_value": self.rg_node_id
                },
                {
                    "attr_id": "aipods_scope",
                    "attr_value": "resource_group_template"
                },
                {
                    "attr_id": "cpu_manufacturer",
                    "attr_value": self.cpu_manufacturer
                },
                {
                    "attr_id": "cpu_model",
                    "attr_value": self.cpu_model
                }
            ]
        }


resource_group_prefix = "rg-"
resource_node_prefix = "rgn-"


class ResourceNodeStatus(ResourceNodeStatusBase, TimestampModel, StatusModel, table=True):
    __tablename__ = "node_status"


class ResourceGroup(ResourceGroupBase, TimestampModel, UserIdModel, StatusModel, ActiveRecordMixin, table=True):
    __tablename__ = "resource_group"


class ResourceNode(ResourceNodeBase, TimestampModel, StatusModel, ActiveRecordMixin, table=True):
    __tablename__ = "resource_node"

    def get_gpu_vendor(self):
        """
        get gpu type
        :return:
        """
        if gpu := self.get_gpu_matcher():
            return gpu.vendor
        return None

    def get_gpu_matcher(self):
        """
        get gpu matcher
        :return:
        """
        return gpu_manager.get_gpu_matcher(self.gpu_model)


class ResourceGroupCreate(BaseModel):
    name: str
    description: str = ''
    sku_id: str = ''
    pod_type: List[PodTypeEnum] = Field(['infer', 'instance', 'job'],
                                        description="instance:容器实例, job:训练作业, infer：推理服务")
    customize_enable: int = 1


class ResourceGroupDel(BaseModel):
    rg_ids: list


class ResourceGroupUpdate(BaseModel):
    name: str = ''
    description: str = ''
    rg_id: str
    pod_type: List[PodTypeEnum] = Field(['infer', 'instance', 'job'],
                                        description="instance:容器实例, job:训练作业, infer：推理服务")
    customize_enable: int = 1


class ResourceGroupNode(BaseModel):
    name: str
    cpu: int
    memory: int
    cpu_model: str
    gpu: int
    gpu_memory: int
    gpu_model: str
    node_type: str


class ResourceGroupDetailRep(BaseModel):
    resource_group: ResourceGroup
    resource_nodes: List[ResourceNode] = []


class ResourceGroupRep(ResourceGroupBase, TimestampModel, UserIdModel, StatusModel, UserPatchInfoModel):
    total_gpu: int = 0
    ava_gpu: int = 0
    total_node_count: int = 0
    used_node_count: int = 0
    used_gpu_count: int = 0
    sku_id: str = ''
    gpu_model: str = ''
    unschedulable_gpu_count: int = 0
    unschedulable_node_count: int = 0
    gpu_ava_util: float = 0.0
    gpu_used_mem: int = 0
    gpu_sum_mem: int = 0


class ProductType(str, Enum):
    resource_group = "resource_group"
    sharing_compute = "sharing_compute"
    container_instance = "container_instance"
    inference_compute = "inference_compute"


class OrderByField(str, Enum):
    ava_memory = "ava_memory"
    created_at = "created_at"


class ReqGetProduct(BaseModel):
    filters: List[dict]
    offset: int = 0
    limit: int = 20


class ReqResourceGroupAddNode(BaseModel):
    sku_id: str
    rg_id: str = None
    count: int = 1
    duration: int = 1
    auto_renewal: int = 1
    charge_mode: str = "monthly"
    next_charge_mode: str = "monthly"


class RepResourceGroupAddNode(BaseGenericResponse):
    rgn_ids: list = []


class RepResourceTemplateAdd(BaseGenericResponse):
    tmp_ids: list = []


class ReqMoveNode2ResourceGroup(BaseModel):
    rg_id: str = None
    rg_node_ids: list = []

class ResumeNodeReq(BaseModel):
    rg_node_ids: list = []


class AddResourceGroupShareUser(BaseModel):
    rg_id: str = Field(description="资源组id")
    is_all: int = Field(0, description="是否添加所有子账户")
    share_user_ids: List = Field([], description="指定账户id,当is_all为1的时候此参数无效")


class RemoveResourceGroupShareUser(BaseModel):
    rg_id: str = Field(description="资源组id")
    is_all: int = Field(0, description="是否移除账户")
    share_user_ids: List = Field([], description="指定账户id,当is_all为1的时候此参数无效")


class ResourceNodeRep(ResourceNodeBase, UserIdModel, TimestampModel, StatusModel, UserPatchInfoModel):
    node_status: ResourceNodeStatus = None
    resource_group: ResourceGroup = None


class BaseCreateResourceTemplateReq(ResourceTemplateBase):
    pod_type: List[PodTypeEnum] = Field(description="instance:容器实例, job:训练作业, infer：推理服务")


class CreateResourceTemplateReq(BaseModel):
    data: List[BaseCreateResourceTemplateReq]


class UpdateResourceTemplateReqBase(ResourceTemplateBase):
    tmp_id: int
    pod_type: List[PodTypeEnum] = Field(description="instance:容器实例, job:训练作业, infer：推理服务")


class UpdateResourceTemplateReq(BaseModel):
    data: List[UpdateResourceTemplateReqBase]


class ResourceGroupNodeReq(BaseModel):
    rg_id: str = Field(None, description="资源组id")
    rg_node_id: str = Field(..., description="资源节点id")


class ResourceGroupNodeGpuReuseReq(ResourceGroupNodeReq):
    is_reused_gpu_node: bool


class ResourceGroupNodeVgpuReq(ResourceGroupNodeReq):
    is_vgpu_node: bool


class ShowUserInfo(BaseModel):
    user_id: str
    user_name: str
    email: str
    phone: str


class ResourceTemplateRep(ResourceTemplateBase, table=False):
    tmp_id: int
    pod_type: List[str] = Field([], description="任务类型")
    resource_group: Optional[ResourceGroup] = Field(None, description="资源组信息")
    rg_node_name: Optional[str] = Field(None, description="节点名称")
    show_users_info: Optional[List[ShowUserInfo]] = Field(None, description="可以查看的用户信息")
    salable: bool = Field(False, description="是否可售卖")
    max_applicable_number: int = Field(0, description="最大适用数量")


class UserShareGroupInfo(BaseModel):
    user_id: str = None
    root_user_id: str = None
    email: str = None
    notify_email: str = None
    phone: str = None
    user_name: str = None
    containers: int = 0
    jobs: int = 0
    quota_containers: int = 0
    quota_jobs: int = 0
    priority: int = 0
    resource_group: List[ResourceGroup] = []


class ResourceTemplateQuery(BaseModel):
    """
    资源模板查询参数
    """
    pod_type: List[PodTypeEnum] = Field([], description="任务类型")
    rg_id: str = Field(default=None, description="资源组id")
    rg_node_id: str = Field(default=None, description="节点id")
    order_by: str = Field(default="created_at", description="排序字段")
    reverse: bool = Field(default=True, description="是否倒序")
    offset: int = Field(default=0, description="偏移量")
    limit: int = Field(default=10, description="数量")
    gpu_name: str = Field(default=None, description="gpu名称")
    template_ids: List[int] = Field(default=[], description="模板id列表")
    spec_type: List[str] = Field(default=[], description="资源类型")
