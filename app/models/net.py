from typing import Optional, List

from sqlalchemy import Column, ARRAY, String
from sqlmodel import SQLModel, Field

from app.core.models import TimestampModel, UserIdModel, StatusModel, ActiveRecordMixin, AutomaticIdModel


class SubnetBase(AutomaticIdModel, table=False):
    name: str = Field('', description="subnet名称")
    namespaces: List[str] = Field(sa_column=Column(ARRAY(String)))
    cidrBlock: str = Field('', description="ip段", index=True)
    vpc: Optional[str] = Field('', description="vpc", nullable=True)



class Subnet(SubnetBase, TimestampModel, UserIdModel, StatusModel, ActiveRecordMixin, table=True):
    __tablename__ = "subnet"