from pydantic import BaseModel
from sqlmodel import Field, SQLModel

from app.core.models import TimestampModel, StatusModel


class ResourcePoolBase(SQLModel, table=False):
    pool_id: str = Field(primary_key=True)
    pool_name: str = Field(default=None, description="资源池名称")
    aipods_type: str = Field(nullable=False, description="规格类型")
    node_count: int = Field(default=0, nullable=True, description="节点数量")
    gpu_count: int = Field(default=0, nullable=True, description="GPU数量")
    extra_info: str = Field(default=None, nullable=True, description="其他信息")
    description: str = Field(default=None, nullable=True, description="描述")


class ResourcePool(ResourcePoolBase, TimestampModel, StatusModel, table=True):
    __tablename__ = "resource_pool"


class ResourcePoolStatisticsResponse(ResourcePool):
    unschedulable_node_count: int = Field(default=0, nullable=True, description="不可调度节点数量")
    unschedulable_gpu_count: int = Field(default=0, nullable=True, description="不可调度GPU数量")
    node_used_count: int = Field(default=0, nullable=True, description="已使用节点数量")
    gpu_used_count: int = Field(default=0, nullable=True, description="已使用GPU数量")
    gpu_ava_util: float = Field(default=0, nullable=True, description="GPU平均利用率")
    gpu_used_mem: int = Field(default=0, nullable=True, description="GPU内存使用量")
    gpu_sum_mem: int = Field(default=0, nullable=True, description="GPU总内存")


class ResourcePoolCreateReq(BaseModel):
    pool_name: str = None
    aipods_type: str
    node: list = Field([], description="节点hostname")
    description: str = None


class ResourcePoolModifyReq(BaseModel):
    pool_id: str
    pool_name: str = None
    description: str = None


class ResourcePoolAddReq(BaseModel):
    pool_id: str = Field(description="资源池id")
    nodes: list = Field(description="节点hostname")


class ResourcePoolRemoveReq(BaseModel):
    nodes: list = Field([], description="节点hostname")
