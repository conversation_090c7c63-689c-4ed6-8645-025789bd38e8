import json
from enum import Enum

from fastapi import Request
import app
from app import logger
from app.core.exceptions import ParameterException, PermissionDeniedException, PermissionDeniedForNamespaceException, \
    ResourceNotFoundException, NotSupportingAccessToAllResources
from app.core.global_server.exceptions import GetSubUserInfoException, UserHasNotPermissionException
from app.core.kube.api.kfam import KfamClient
from app.core.kube.models.kfam import WorkGroupInfo
from app.core.models import ActiveRecordMixin
from app.core.rlock import RedisClient
from app.core.qingcloud.interface import describe_sub_users_without_ak_sk
from app.core.utils import get_namespaces_cache_key
from app.models.notebooks import Notebook


def has_resource_permission_namespace(biz_id_field: str, resource_type: ActiveRecordMixin):
    def depend(request: Request):
        """
        检查用户是否有权限访问指定的namespace
        :param namespace:
        :param request:
        :return:
        """
        # 获取资源类型名称
        model = "NB" if resource_type.__tablename__ == "notebook" else "TN"
        # Debug模式直接放行
        if app.settings.debug:
            logger.info("Debug mode, skip permission check")
            return
        # 管理员放行
        if request.user.is_super_user():
            logger.info("Super user access all resources")
            return

        namespace = request.path_params.get("namespace")
        # # 用于检验的namespace
        namespaces = []
        # 主账号只允许访问自己和自己的子账号
        if not request.user.is_subuser():
            logger.info("Subuser start to check permission.")
            namespaces = get_user_namespace(request.user.user_id)
            if namespace != "ALL" and namespace not in namespaces:
                logger.warning(f"User has not permission to access namespace:{namespace}")
                raise PermissionDeniedException(f"namespace:{namespace}")
        # 子账号根据权限只能访问自己、其它子账号、自己的主账号
        else:
            permission = next(item["permission"] for item in request.user.permissions if item["module"] == model)
            if permission == "NO":
                logger.warning(f"Subuser has not permission to access namespace:{namespace}")
                raise UserHasNotPermissionException(f"namespace:{namespace}")
            # OWN只允许访问自己namespace资源
            if permission == "OWN":
                if namespace == "ALL":
                    namespaces.append(request.user.user_id.lower())
                else:
                    if namespace != request.user.user_id.lower():
                        logger.warning(f"Subuser has not permission to access namespace:{namespace}")
                        raise UserHasNotPermissionException(f"namespace:{namespace}")
                    else:
                        namespaces.append(request.user.user_id.lower())
            if permission in ["READALL", "ALL"]:
                # 获取主账号能访问的所有namespaces
                namespaces = get_user_namespace(request.user.root_user_id)
                if namespace != "ALL" and namespace not in namespaces:
                    logger.warning(f"User has not permission to access namespace:{namespace}")
                    raise PermissionDeniedException(f"namespace:{namespace}")
        # 校验id是否在namespaces列表内
        # get resource id from path params or query params or body
        id_val = request.path_params.get(biz_id_field) or request.query_params.get(biz_id_field)
        if not id_val:
            data = request._json
            id_val = data.get(biz_id_field) if data else None
        if not id_val:
            raise ParameterException(biz_id_field)

        if not isinstance(id_val, list):
            id_val = id_val.split(",")
        logger.debug(f"id_val: {id_val}, namespaces: {namespaces}")
        resources = resource_type.all_by_ids_in_namespaces(id_val, namespaces)
        logger.debug(f"resources: {resources}")
        if len(resources) != len(id_val):
            logger.warning(f"Resource not found: {id_val}, in namespace: {namespaces}")
            raise PermissionDeniedException(id_val)
        return
    return depend


def has_permission(namespace: str, request: Request):
    """
    检查用户是否有权限访问指定的namespace
    :param namespace:
    :param request:
    :return:
    """
    if app.settings.debug:
        return namespace

    # for superuser, no need to check namespace
    if request.user.is_super_user():
        return namespace

    # for normal user, check namespace, if namespace is ALL, get all namespaces has permission
    if namespace == "ALL":
        if app.settings.SUPPORT_SUBUSER_GET_ALL_OF_ROOT_USER:
            return ",".join(get_user_namespace(request.user.root_user_id))
        return ",".join(get_user_namespace(request.user.user_id))

    if not user_has_namespace_permission(request.user.user_id, namespace):
        raise PermissionDeniedForNamespaceException(namespace)

    return namespace


def user_has_namespace_permission(user_id, namespace: str) -> bool:
    namespaces = get_user_namespace(user_id)
    if namespace not in namespaces:
        return False
    return True


def get_user_namespace(user_id) -> list:
    kfam_client = KfamClient(user_id)
    workgroup: WorkGroupInfo = kfam_client.get_workgroup_info()
    namespaces = [x.namespace for x in workgroup.namespaces]
    return namespaces


def is_admin(request: Request) -> bool:
    """
    检查用户是否是管理员
    :param request:
    :return:
    """
    if app.settings.debug:
        return True

    admin_roles = {"global_admin", "console_admin"}
    if request.auth and set(request.auth) & admin_roles:
        return True

    raise PermissionDeniedException(request.url.path)


class PermissionLevel(str, Enum):
    READ = "READ"
    WRITE = "WRITE"


def interface_auth_check(request: Request, check_type: str, namespace):
    if namespace in ["ALL", ""]:
        raise NotSupportingAccessToAllResources()
    if app.settings.debug or request.user.is_super_user() or not request.user.is_subuser():
        return namespace
    # 获取用户的权限
    permission = next(item["permission"] for item in request.user.permissions if item["module"] == check_type)
    # 仅操作自己的资源
    if permission == "OWN":
        if namespace != request.user.user_id.lower():
            raise PermissionDeniedException()
        return namespace
    if permission in ["READALL", "ALL"]:
        # 获取使用主账号id获取全部的子账号列表
        data = describe_sub_users_without_ak_sk(request.user.root_user_id)
        if not data.get("user_set"):
            raise GetSubUserInfoException()
        namespaces = []
        for item in data["user_set"]:
            namespaces.append(item['user_id'].lower())
        # 追加主账号的ns
        namespaces.append(request.user.root_user_id.lower())
        if namespace not in namespaces:
            raise PermissionDeniedForNamespaceException(namespace)
        return namespace