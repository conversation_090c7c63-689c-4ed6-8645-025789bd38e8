from websocket import WebSocket

import app

from app.core.exceptions import PermissionDeniedForNamespaceException, NotSupportingAccessToAllResources, \
    PermissionDeniedException
from app.core.global_server.exceptions import GetSubUserInfoException
from app.core.global_server.global_server import GlobalServerClient
from app.core.models import Qingcloud<PERSON>ser
from app.core.qingcloud.interface import describe_sub_users_without_ak_sk, describe_user, describe_access_key_by_user_id


def get_namespace(websocket: WebSocket, pod_name: str, namespace: str):
    if namespace in ["ALL", ""]:
        raise NotSupportingAccessToAllResources()
    if app.settings.debug or (websocket.headers.get("X-REMOTE-GROUP") == "system:authenticated"
                              and websocket.headers.get("X-REMOTE-USER") == "admin"):
        return namespace
    user_id = websocket.headers.get("AICP-USERID")
    if user_id is None:
        raise PermissionDeniedException("user_id is None")
    users = describe_user(user_id)
    if not users.get("user_set"):
        raise PermissionDeniedException(user_id)
    ak = describe_access_key_by_user_id(user_id)
    if not ak.get("access_key_set"):
        raise PermissionDeniedException(user_id)
    user = users.get("user_set")[0]
    user["access_key_id"] = ak.get("access_key_set")[0]["access_key_id"]
    user["secret_access_key"] = ak.get("access_key_set")[0]["secret_access_key"]
    # 调用global服务获取权限
    permissions = GlobalServerClient().get_group_auth_info(user)["data"]["permissions"]
    user = QingcloudUser(**user)
    # 如果是管理员，主账号，需要全部放行
    if user.is_super_user() or not user.is_subuser():
        return namespace
    # 获取资源类型
    resource_type = get_resource_type(pod_name)
    permission = next(item["permission"] for item in permissions if item["module"] == resource_type)
    # 仅操作自己的资源
    if permission == "OWN":
        if namespace != user.user_id.lower():
            raise PermissionDeniedException()
        return namespace
    if permission in ["READALL", "ALL"]:
        # 获取使用主账号id获取全部的子账号列表
        data = describe_sub_users_without_ak_sk(user.root_user_id)
        if not data.get("user_set"):
            raise GetSubUserInfoException()
        namespaces = []
        for item in data["user_set"]:
            namespaces.append(item['user_id'].lower())
        # 追加主账号的ns
        namespaces.append(user.root_user_id.lower())
        if namespace not in namespaces:
            raise PermissionDeniedForNamespaceException(namespace)
        return namespace


def get_resource_type(pod_name):
    return "NB" if pod_name.split('-')[0] == "nb" else "TN"