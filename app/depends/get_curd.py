from functools import partial

from fastapi import Depends, Request
from sqlmodel import Session

from app.core.db import get_db_session
from app.cruds.base import BaseCrud
from app.cruds.trains import TrainCRUD
from app.models.trains import Train
from app.depends.authz import has_resource_permission_namespace


def get_curd(curd: BaseCrud):
    """
    获取CRUD
    :param curd: curd类型
    :return:
    """

    def warp(
            request: Request,
            session: Session = Depends(get_db_session)
    ) -> TrainCRUD:
        return TrainCRUD(session=session, user=request.user)

    return warp


has_train_resource_permission_namespace = partial(has_resource_permission_namespace, resource_type=Train)
