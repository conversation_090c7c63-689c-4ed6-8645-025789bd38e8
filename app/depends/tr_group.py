import app
from app import logger
from app.core.exceptions import PermissionDeniedForNamespaceException
from app.core.global_server.exceptions import UserHasNotPermissionException, UserHasNotWritePermissionException, \
    GetSubUserInfoException
from app.core.qingcloud.interface import describe_sub_users_without_ak_sk
from app.depends.authz import PermissionLevel, user_has_namespace_permission
from fastapi import Request


def train_has_permission(
        permission_level: PermissionLevel = PermissionLevel.READ,
        valid_consume: bool = False
):
    """
    分布式训练子账号权限判断
    :param permission_level: 接口等级(READ 读、WRITE 写)
    :param valid_consume: 校验消费权限
    :return:
    """
    def validate_permission(request: Request):
        if not request.user.is_subuser() or app.settings.debug:
            return True
        # 获取子账号关于容器实例的权限
        permissions = request.user.permissions
        # NO-无功能权限；
        # OWN-仅操作自己数据；
        # READALL-查看全部子账号数据；
        # ALL-操作全部子账号数据；
        # 默认：OWN
        permission = next(item["permission"] for item in permissions if item["module"] == "TN")
        if permission == "NO":
            raise UserHasNotPermissionException()
        # if permission == "READALL" and permission_level == PermissionLevel.WRITE:
        #     raise UserHasNotWritePermissionException()
        return True

    return validate_permission


def train_namespace_permission(permission_level: PermissionLevel = PermissionLevel.READ):
    """
    返回可以访问的namespace列表
    :param permission_level: 接口等级(READ 读、WRITE 写)
    """
    def get_ns(request: Request, namespace: str):
        if app.settings.debug:
            logger.debug("Dubug mode.")
            return request.user.user_id.lower()
        # 如果是管理员，需要全部放行
        if request.user.is_super_user():
            logger.info("Super user.")
            return namespace
        # 主账号判断逻辑
        if not request.user.is_subuser():
            # for normal user, check namespace, if namespace is ALL, get all namespaces has permission
            if namespace == "ALL":
                # 获取使用主账号id获取全部的子账号列表
                data = describe_sub_users_without_ak_sk(request.user.root_user_id)
                namespaces = []
                if data.get("user_set"):
                    for item in data["user_set"]:
                        namespaces.append(item['user_id'].lower())
                # 追加自己的ns
                namespaces.append(request.user.user_id.lower())
                return ','.join(namespaces)
            if not user_has_namespace_permission(request.user.user_id, namespace):
                logger.warning("Not have permission to get namespace.")
                raise PermissionDeniedForNamespaceException(namespace)
            return namespace
        # 获取子账号关于容器实例的权限
        permissions = request.user.permissions
        # NO-无功能权限；
        # OWN-仅操作自己数据；
        # READALL-查看全部子账号数据；
        # ALL-操作全部子账号数据；
        # 默认：OWN
        permission = next(item["permission"] for item in permissions if item["module"] == "TN")
        if permission == "OWN":
            logger.info("Subuser have permission OWN.")
            return request.user.user_id.lower()
        if permission == "READALL":
            logger.info("Subuser have permission READALL.")
            # 当写权限时，仅允许操作自己ns的资源
            if permission_level == PermissionLevel.WRITE and request.user.user_id.lower() != namespace:
                raise PermissionDeniedForNamespaceException(namespace)
            # 写权限，自己的ns资源放行
            if permission_level == PermissionLevel.WRITE and request.user.user_id.lower() == namespace:
                return namespace
            # 读权限，全部放行，如果ns没有值或者为ALL，返回所有的子账号lower集合
            if permission_level == PermissionLevel.READ and namespace in ["", "ALL"]:
                # 获取使用主账号id获取全部的子账号列表
                data = describe_sub_users_without_ak_sk(request.user.root_user_id)
                if not data.get("user_set"):
                    logger.warning("Get user_set error.")
                    raise GetSubUserInfoException()
                namespaces = []
                for item in data["user_set"]:
                    namespaces.append(item['user_id'].lower())
                # 追加主账号的ns
                namespaces.append(request.user.root_user_id.lower())
                return ','.join(namespaces)
            # 读权限，全部放行，如果ns有值，需要判断ns是否是同一个主账号下的
            if permission_level == PermissionLevel.READ and namespace not in ["", "ALL"]:
                # 获取使用主账号id获取全部的子账号列表
                data = describe_sub_users_without_ak_sk(request.user.root_user_id)
                if not data.get("user_set"):
                    logger.warning("Get user_set error.")
                    raise GetSubUserInfoException()
                namespaces = []
                for item in data["user_set"]:
                    namespaces.append(item['user_id'].lower())
                # 追加主账号的ns
                namespaces.append(request.user.root_user_id.lower())
                # namespace没有在集合下，直接抛出异常
                if namespace not in namespaces:
                    raise PermissionDeniedForNamespaceException(namespace)
                return namespace
        if permission == "ALL":
            logger.info("Subuser have permission ALL.")
            # 全部放行，如果ns有值，需要判断ns是否是同一个主账号下的
            if namespace not in ["", "ALL"]:
                # 获取使用主账号id获取全部的子账号列表
                data = describe_sub_users_without_ak_sk(request.user.root_user_id)
                if not data.get("user_set"):
                    logger.warning("Get user_set error.")
                    raise GetSubUserInfoException()
                namespaces = []
                for item in data["user_set"]:
                    namespaces.append(item['user_id'].lower())
                # 追加主账号的ns
                namespaces.append(request.user.root_user_id.lower())
                # namespace没有在集合下，直接抛出异常
                if namespace not in namespaces:
                    raise PermissionDeniedForNamespaceException(namespace)
                return namespace
            # 全部放行，如果ns没有值或者为ALL，返回所有的账号lower集合
            if namespace == "" or namespace == "ALL":
                # 获取使用主账号id获取全部的子账号列表
                data = describe_sub_users_without_ak_sk(request.user.root_user_id)
                if not data.get("user_set"):
                    logger.warning("Get user_set error.")
                    raise GetSubUserInfoException()
                namespaces = []
                for item in data["user_set"]:
                    namespaces.append(item['user_id'].lower())
                # 追加主账号的ns
                namespaces.append(request.user.root_user_id.lower())
                return ','.join(namespaces)
    return get_ns
