from fastapi import Request

import app
from app.apps.resource_group.exceptions import SubUserHasNotResourceGroupPermissions


def resource_group_sub_user_permission():
    """
    资源组不允许子账号进行任何操作
    :return:
    """
    def validate_permission(request: Request):
        if not request.user.is_subuser() or app.settings.debug:
            return True
        raise SubUserHasNotResourceGroupPermissions()

    return validate_permission