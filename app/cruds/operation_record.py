from sqlmodel import Session

from app import logger
from app.models.operation_record import OperationR<PERSON>ord
from app.core.utils import generate_random_string


class OperationRecordCrud:
    """
    Train CRUD
    """

    def __init__(self, session: Session):
        self.session: Session = session

    def generate_id(self, exclude_ids=None):
        while True:
            data_id = "op-" + generate_random_string()
            logger.info("get data_id [%s]", data_id)
            data = self.get_operation_record(data_id)
            logger.info(data)
            if data:
                continue
            if exclude_ids and data_id in exclude_ids:
                continue
            return data_id

    def get_operation_record(self, operation_id):
        return self.session.query(OperationRecord).get(operation_id)

    def add_operation_record(self, operation_record):
        self.session.add(operation_record)
