from datetime import datetime
from typing import List, Sequence
from uuid import UUID

from sqlmodel import Session, select

from app.models.trains import Train


class AdminCRUD:
    """
    Train CRUD
    """

    def __init__(self, session: Session, user: str = None):
        super().__init__()
        self.session = session

    def get_trains_by_uuids(self, uuids: List[str]) -> Sequence[tuple[str, UUID, datetime]]:
        """
        根据uuids获取作业
        :param uuids:
        :return:
        """
        return self.session.exec(select(Train.status, Train.uuid, Train.updated_at).where(Train.uuid.in_(uuids))).all()
