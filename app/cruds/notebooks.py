import json
import math
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Sequence, Tuple, Union
import asyncio

import kr8s
from kr8s import NotFoundError, ExecError
from kr8s.objects import Service
from kr8s.asyncio.objects import Pod
from sqlalchemy import <PERSON>alar<PERSON><PERSON>ult, text
from sqlalchemy.orm import selectinload
from sqlmodel import Session, asc, col, desc, or_, select

from app.apps.notebooks.background_task import start_notebooks_in_background_by_billing_action, \
    stop_notebooks_in_background_by_billing_action
import app
from app.apps.notebooks.background_task import start_notebooks_in_background_by_billing_action, \
    stop_notebooks_in_background_by_billing_action
from app.apps.notebooks.kr8s_objects.notebook import NoteBook as K8SNoteBook, NoteBookCrOperator, \
    generate_notebook_init_password, generate_notebook_init_password_v2, generate_notebook_init_password_v3
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.core.utils import aicp_json_default_serializer, log_background_task_exception
from app.models.gpu import NodeStaticInfo
from app.core.prometheus.monitoring_operator import MonitoringOperator
from app.core.prometheus.query_params import MonitoringQueryParams
from app.core.qingcloud.common import get_qingcloud_user_info_by_user_id
from app.models.notebooks import ALLNotebookStatus, Notebook, NotebookCreate, NotebookRead, \
    NotebookReason, NotebookReplicaSpec, NotebookServices, \
    NotebookSearchQueryParams, \
    NotebookStatus, SearchableNotebookStatus
from app.cruds.resource_group import ResourceGroupCrud
from app.apps.resource_group.exceptions import NotGpuReuseNodeException
from app.models.operation_record import OperationRecord
from app.models.resource_group import ResourceNode, ResourceTemplate
from app.core.constant import SOURCE_COMMAND
from app.core.db import get_async_db_session
from app.core.exceptions import PermissionDeniedException, ResourceNotFoundException
from app.core.kube.api import STATUS_PHASE
from app.core.loggers import logger
from app.core.models import AicpServers, QingcloudUser
from app.core.opensearch.client import OpenSearchClient, es_client
from app.core.prometheus.client import PromethusClient
from app.core.qingcloud.interface import ZONE_INFO, INSTANCE_ERROR, send_message_request, RELEASE_RESOURCE


PROBE_PORT_MAP = {
    "jupyter": 80,
    "vscode": 81,
    "ssh": 22,
    "coder": 81,
}

def add_ip(notebookreads: List[NotebookRead]):
    """

    :param notebookreads:
    :return:
    """
    if not notebookreads:
        return

    notebook_ids = [x.uuid for x in notebookreads]
    notebook_object = OpenSearchClient().get_pods_by_lables_app(notebook_ids)
    ip_maps = {x["metadata"]["labels"]["app"]: x.get("status", {}).get("podIPs", []) 
               for x in notebook_object 
               if x.get("status", {}).get("podIPs")}

    for notebook in notebookreads:
        notebook.pod_ips = ip_maps.get(notebook.uuid, [])

def add_ssh_info(namespace: str, notebookreads: List[NotebookRead]):
    """
    :param namespace:
    :param notebookreads:
    :return:
    """
    if not notebookreads:
        return

    logger.debug(f"add_ssh_info for notebooks: {notebookreads}")
    for notebook in notebookreads:

        nb_uuid = notebook.uuid
        if notebook.status == NotebookStatus.Running:
            probe_success = PromethusClient().get_namespace_notebook_probe_success(namespace, nb_uuid)
        else:
            probe_success = {}

        ssh_service_id = f"{nb_uuid}-ssh"
        ssh_service = NotebookServices.one_by_id(ssh_service_id)
        if ssh_service:
            server_ports = json.loads(ssh_service.server)
        else:
            try:
                node_service = Service.get(name=f"{nb_uuid}-ssh", namespace=namespace).raw
                server_ports = node_service['spec']['ports']
                NotebookServices(uuid=ssh_service_id, server=json.dumps(server_ports)).save()
            except NotFoundError:
                logger.warning(f"node service ssh {nb_uuid}-ssh not found")
                continue

        ssh_nodeport = 0
        for port in server_ports:
            if port['name'].split('-')[0] == "ssh":
                ssh_nodeport = port['nodePort']

        ssh_version_id = f"{nb_uuid}-ssh-version"
        ssh_version = NotebookServices.one_by_id(ssh_version_id)
        if ssh_version:
            password_version = ssh_version.server
        else:
            k8s_notebook: K8SNoteBook = K8SNoteBook.get(nb_uuid, namespace=namespace)
            password_version = k8s_notebook.annotations.get("notebooks.kubeflow.org/password-version", "v1")
            NotebookServices(uuid=ssh_version_id, server=password_version).save()

        if password_version == "v2":
            password = generate_notebook_init_password_v2(nb_uuid)
        elif password_version == "v3":
            password = generate_notebook_init_password_v3(nb_uuid)
        else:
            password = generate_notebook_init_password(nb_uuid)

        notebook.servers = [
            AicpServers(
                url=app.settings.NOTEBOOK_HOST.format(server_type="jupyter", namespace=namespace, name=nb_uuid),
                target_port=8888, server_type="jupyter", server_name="jupyter", status=bool(probe_success.get(str(PROBE_PORT_MAP["jupyter"]), False))
            ),
            AicpServers(
                url=app.settings.NOTEBOOK_HOST.format(server_type="coder", namespace=namespace, name=nb_uuid),
                target_port=8889, server_type="vscode", server_name="vscode", status=bool(probe_success.get(str(PROBE_PORT_MAP["vscode"]), False))
            ),
            AicpServers(
                url=f"ssh root@{app.settings.SSH_HOST} -p {ssh_nodeport}",
                target_port=22, server_type="ssh", server_name="ssh", status=bool(probe_success.get(str(PROBE_PORT_MAP["ssh"]), False)),
                password=password
            )
        ]


class NotebookCRUD:
    """
    Notebook CRUD
    """

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session = session
        self.user = user

    def get_by_uuid(self, uuid: str) -> Notebook:
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(Notebook).where(Notebook.uuid == uuid)
        notebook_res: ScalarResult[Notebook] = self.session.scalars(stmt)
        notebook: Notebook = notebook_res.first()
        if not notebook:
            raise ResourceNotFoundException(message=uuid)
        return notebook

    async def get_by_uuid_async(self, uuid: str) -> Notebook:
        async with get_async_db_session() as session:
            stmt = select(Notebook).where(Notebook.uuid == uuid).options(selectinload(Notebook.replica_specs))
            sr: ScalarResult[Notebook] = await session.scalars(stmt)
            notebook: Notebook = sr.first()
            return notebook

    def get_by_uuids(self, uuids: List[str]) -> Sequence[Notebook]:
        """
        通过uuid获取作业
        :param uuids:
        :return:
        """
        stmt = select(Notebook).where(Notebook.uuid.in_(uuids))
        notebooks: ScalarResult[Notebook] = self.session.scalars(stmt)
        return notebooks.fetchall()

    def get_by_namespace(self, namespace: str, status=SearchableNotebookStatus) -> Sequence[Notebook]:
        """
        通过命名空间获取作业
        :param status:
        :param namespace:
        :return:
        """
        stmt = select(Notebook).where(Notebook.namespace == namespace, Notebook.status.in_(status))
        notebooks: ScalarResult[Notebook] = self.session.scalars(stmt)
        return notebooks.fetchall()

    def get_by_uuid_with_permission(self, uuid: str) -> Notebook:
        """
        通过uuid获取作业
        :param uuid:
        :return:
        """
        stmt = select(Notebook).where(Notebook.uuid == uuid)
        if not self.user.is_super_user():
            stmt = stmt.where(Notebook.user_id == self.user.user_id)
        notebook_res: ScalarResult[Notebook] = self.session.scalars(stmt)
        notebook: Notebook = notebook_res.first()
        if not notebook:
            raise PermissionDeniedException(message=uuid)
        return notebook

    def get_by_uuids_with_permission(self, uuids: List[str]) -> Sequence[Notebook]:
        """
        通过uuid获取作业
        :param uuids:
        :return:
        """
        stmt = select(Notebook).where(Notebook.uuid.in_(uuids))
        if not self.user.is_super_user():
            stmt = stmt.where(Notebook.user_id == self.user.user_id)
        notebooks: ScalarResult[Notebook] = self.session.scalars(stmt)
        return notebooks.fetchall()

    def check_balance(self, data: NotebookCreate) -> bool:
        """
        检查余额
        :param user_id:
        :param charge_mode:
        :param duration:
        :return:
        """
        replica_spec = NotebookReplicaSpec(**data.replica_specs.dict())
        return replica_spec.resource.check_resource_balance(data.user_id, data.charge_mode, data.duration)

    def create(self, data: NotebookCreate) -> list[Notebook]:
        """
        创建作业
        :param data:
        :return:
        """
        notebooks = []
        # 如果使用获取资源规则
        if data.replica_specs.template_id:
            template = ResourceTemplate.one_by_id(data.replica_specs.template_id, session_=self.session)
            data.replica_specs.sqlmodel_update(template.to_spec(**data.replica_specs.dict()))
            logger.info(data.replica_specs.dict())

        if data.replica_specs.rg_id is not None:
            # 检查专属资源组权限
            rgc = ResourceGroupCrud(self.session)
            resource: ResourceNode = rgc.get_resource_group_node_by_user_id(
                data.replica_specs.rg_id, data.replica_specs.specs, self.user.user_id,
                gpu_name=data.replica_specs.custom_gpu_name)
            logger.info("Resource Group Node: {}".format(resource))

            if data.replica_specs.custom_gpu_list and not resource.is_reused_gpu_node:
                logger.info(f"resource {resource.rg_node_id} is not reused gpu node")
                raise NotGpuReuseNodeException()

            if data.replica_specs.custom_gpu:
                data.replica_specs.custom_gpu_type = resource.gpu_model
                data.replica_specs.custom_gpu_name = resource.gpu_name

                if data.replica_specs.custom_gpu == resource.gpu:
                    node_static: NodeStaticInfo = NodeStaticInfo.one_by_id(resource.hostname, session_=self.session)
                    data.replica_specs.custom_infiniband = node_static.ib_count_compute

        for index in range(data.replica_specs.replicas):
            notebook = Notebook(
                **data.dict(exclude={"volume_specs", "replica_specs"}),
                status=STATUS_PHASE.Pending,
            )

            # 生成资源规则
            replica_spec = NotebookReplicaSpec(**data.replica_specs.dict(exclude={"replicas"}), replicas=1)
            replica_spec.update_custom_specs()
            notebook.replica_specs = replica_spec
            notebooks.append(notebook)
            OperationRecord.create_by_resource(
                notebook, params=json.dumps(data.dict(), default=aicp_json_default_serializer)) \
                .save(session_=self.session)

        self.session.add_all(notebooks)
        self.session.commit()

        return notebooks

    async def update_notebook_sources(self, uuid: str, source_key: str, source_val: str):
        try:
            notebook: Notebook
            async with get_async_db_session() as session:
                stmt = select(Notebook) \
                    .where(Notebook.uuid == uuid)
                sr: ScalarResult[Notebook] = await session.scalars(stmt)
                notebook = sr.first()
                # 修改源文件
                pod = await Pod.get(uuid + "-0", namespace=notebook.namespace)
                command = SOURCE_COMMAND.get(f"{source_key}-{source_val}")
                await pod.exec(command, stdout=sys.stdout.buffer, stderr=sys.stderr.buffer)
                if source_key == "pip":
                    notebook.pip = source_val
                elif source_key == "conda":
                    notebook.conda = source_val
                elif source_key == "apt":
                    notebook.apt = source_val
                session.add(notebook)
        except NotFoundError as e:
            logger.warning(f"notebook {uuid} not found in k8s, then delete it in db directly")
            raise NotFoundError
        except ExecError as e:
            logger.exception(f"{uuid} change source failed.")
            raise e

    def search(
            self, namespace: str, query: NotebookSearchQueryParams
    ) -> Tuple[int, Sequence[Notebook]]:
        """
        :param query:
        :param namespace: 命名空间
        :return:
        获取作业
        """
        statement = select(Notebook) \
            .options(selectinload(Notebook.volume_specs)) \
            .options(selectinload(Notebook.replica_specs))

        if self.user.is_kse_admin_user():
            if query.owner:
                statement = statement.where(Notebook.user_id == query.owner)
            if not query.status:
                query.status = ALLNotebookStatus
        else:
            if query.status:
                query.status = list(set(query.status) & set(SearchableNotebookStatus))
            else:
                query.status = SearchableNotebookStatus

        if namespace != "ALL":
            statement = statement.where(Notebook.namespace.in_(namespace.split(",")))

        if query.name:
            name_query = or_(Notebook.uuid == query.name, col(Notebook.name).contains(f"%{query.name}%"))
            statement = statement.where(name_query)

        if query.status:
            statement = statement.where(col(Notebook.status).in_(query.status))

        if query.start_at is not None:
            statement = statement.where(Notebook.created_at >= query.start_at)

        if query.end_at is not None:
            statement = statement.where(Notebook.created_at <= query.end_at)

        count: int = Notebook.count_over_all_by_stmt(statement, session_=self.session)

        if query.order_by is not None:
            order_by = desc(text(query.order_by)) if query.reverse else asc(text(query.order_by))
            statement = statement.order_by(order_by)

        statement = statement.offset(query.offset).limit(query.limit)

        notebooks: ScalarResult[Notebook] = self.session.scalars(statement)
        return count, notebooks.fetchall()

    def get_by_user_id_and_uuid(self, user_id: str, uuid: str) -> Notebook:
        """
        获取作业
        :param uuid:
        :param user_id:
        :return:
        """

        statement = select(Notebook).where(Notebook.uuid == uuid, Notebook.user_id == user_id).options(
            selectinload(Notebook.replica_specs))
        notebooks: ScalarResult[Notebook] = self.session.exec(statement)
        notebook: Notebook = notebooks.first()
        return notebook

    def terminated(self, notebook_uuid: str, reason="", force=False) -> None:
        """
        删除容器实例
        :param uuid:
        :return:
        """
        notebook = Notebook.one_by_id(notebook_uuid, session_=self.session)
        # 记录操作日志
        OperationRecord.create_by_resource(
            notebook, status=STATUS_PHASE.Terminated,
            reason=reason, action=STATUS_PHASE.Terminated
        ).save()

        if notebook.status in STATUS_PHASE.Terminated:
            return
        pre_status = notebook.status
        notebook.status = STATUS_PHASE.Terminating
        notebook.operation_user_id = self.user.user_id
        notebook.reason = reason
        self.session.commit()
        self.session.refresh(notebook)
        try:
            # only running notebook should be dumped
            # if save image failed, raise image build failed exception and stop delete notebook
            # if pre_status == STATUS_PHASE.Running:
            #     notebook_cr = NoteBookCrOperator(notebook.uuid, notebook.namespace, notebook.user_id)
            #     image_builder = notebook_cr.dumps_image()
            #     image_builder.wait_for_committed()

            notebooks = kr8s.get("notebooks", notebook.uuid, namespace=notebook.namespace)
            logger.debug(f"fetched uid {notebook.uuid} notebooks: {notebooks}")
            if not notebooks:
                raise NotFoundError()
            notebooks[0].delete()
            # IAAS send message.
            user_info = get_qingcloud_user_info_by_user_id(notebook.user_id)
            send_data = {
                "username": user_info.get("user_name"),
                "zone": ZONE_INFO,
                "resource": "容器实例",
                "name": notebook.name,
                "id": notebook.uuid
            }
            send_message_request(RELEASE_RESOURCE, notebook.user_id, json.dumps(send_data))
        except NotFoundError as e:
            logger.warning(f"notebook {notebook.name} not found in k8s, then delete it in db directly")
            notebook.status = STATUS_PHASE.Terminated
        except Exception as e:
            logger.exception(f"delete {notebook.uuid} failed.")
            notebook.reason = "delete failed"
            notebook.status = pre_status
            # Send message to IAAS.
            send_data = {
                "zone": ZONE_INFO,
                "id": notebook.uuid,
                "spec": f"{notebook.replica_specs.custom_cpu}核 {notebook.replica_specs.custom_memory}G {notebook.replica_specs.custom_gpu_type} {notebook.replica_specs.custom_gpu_memory}G * {notebook.replica_specs.custom_gpu}" if notebook.replica_specs.custom_aipods_type != "only_cpu" else f"{notebook.replica_specs.custom_cpu}核 {notebook.replica_specs.custom_memory}G",
                "action": "容器实例删除失败"
            }
            send_message_request(INSTANCE_ERROR, notebook.user_id, json.dumps(send_data))
        notebook.auto_delete_time = None
        self.session.commit()

        notebook.unlease()

    def get_status_by_uuids(self, uuids: List[str]) -> list[tuple[str, str, datetime, str, str]]:
        """
        获取作业状态
        :param uuids:
        :return:
        """
        stmt = select(Notebook.uuid, Notebook.status, Notebook.updated_at, Notebook.name, Notebook.reason).where(
            # noqa
            Notebook.uuid.in_(uuids))
        notebooks_status: Sequence[tuple[str, str, datetime, str, str]] = self.session.exec(stmt).all()

        return notebooks_status

    def update_notebook_name(self, user_id: str, uuid: str,
                             notebook_name: str) -> None:
        """

        :param user_id:
        :param uuid:
        :return:
        """
        notebook: Notebook = self.get_by_uuid(uuid)
        OperationRecord.create_by_resource(notebook, action="UpdateName", reason=None).save(session_=self.session)

        if notebook.name != notebook_name:
            notebook.name = notebook_name
            self.session.commit()

        return notebook

    def check_all_in_status(self, notebooks: Sequence[Notebook], status: List[str]) -> bool:
        return all([notebook.status in status for notebook in notebooks])

    def check_all_running(self, notebooks: Sequence[Notebook]):
        """
        检查所有作业是否运行
        :return:resource_group
        """
        return self.check_all_in_status(notebooks, [STATUS_PHASE.Running])

    def check_all_suspended(self, notebooks: Sequence[Notebook]):
        """
        检查所有作业是否暂停中
        :return:
        """
        return self.check_all_in_status(notebooks, [STATUS_PHASE.Suspended])

    def get_services(self, notebooks: List[Notebook]):
        services_name = [f"{x.uuid}-ssh" for x in notebooks]
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "kind": "Service"
                            }
                        },
                        {
                            "terms": {
                                "metadata.name.keyword": services_name
                            }
                        }
                    ]
                }
            },
            "size": len(services_name) * 3,
        }
        resulte = es_client.search_kube_resource(query)
        services = {}
        for service in resulte.get("hits", {}).get("hits", []):
            name = service["_source"]["metadata"]["name"]
            if name in services and int(service["_source"]["metadata"]["resourceVersion"]) < int(
                    services[name]["metadata"]["resourceVersion"]):
                continue

            services[name] = service["_source"]
        return services

    def get_servers(self, notebook: Notebook, service, probe_success: Dict) -> List[AicpServers]:
        if notebook.status != NotebookStatus.Running:
            return []
        servers = [
            AicpServers(
                url=app.settings.NOTEBOOK_HOST.format(server_type="jupyter", namespace=notebook.namespace,
                                                      name=notebook.uuid),
                target_port=8888, server_type="jupyter", server_name="jupyter",
                status=probe_success.get(notebook.uuid, {}).get("80", False)
            ),
            AicpServers(
                url=app.settings.NOTEBOOK_HOST.format(server_type="coder", namespace=notebook.namespace,
                                                      name=notebook.uuid),
                target_port=8889, server_type="vscode", server_name="vscode",
                status=probe_success.get(notebook.uuid, {}).get("81", False)
            ),
        ]
        if f"{notebook.uuid}-ssh" not in service:
            return servers
        for node_port in service[f'{notebook.uuid}-ssh']['spec']['ports']:
            server_type = server_name = node_port['name'].split('-')[0]
            server = AicpServers(
                url="",
                target_port=node_port['targetPort'],
                server_type=server_type,
                server_name=server_name
            )
            if server_type == "ssh":
                server.url = f"ssh root@{app.settings.SSH_HOST} -p {node_port['nodePort']}"
                server.password = generate_notebook_init_password(notebook.uuid)
                server.status = probe_success.get(notebook.uuid, {}).get("22", False)
            else:
                server.url = f"{app.settings.SSH_HOST}:{node_port['nodePort']}"

            servers.append(server)
        return servers

    def add_servers(self, notebookreads: List[NotebookRead]):
        if not notebookreads:
            return

        notebook_ids = [x.uuid for x in notebookreads]
        notebook_object = OpenSearchClient().get_pods_by_lables_app(notebook_ids)
        ip_maps = {x["metadata"]["labels"]["app"]: x.get("status", {}).get("podIPs", []) 
                   for x in notebook_object 
                   if x.get("status", {}).get("podIPs")}

        probes_success = PromethusClient().get_notebook_probe_success(notebook_ids)

        try:
            service_map = self.get_services(notebookreads)
        except Exception as e:
            logger.error("get services error: %s", e)
            service_map = {}

        for notebook in notebookreads:
            notebook.ws_url = "/aicp/kapi/v1/ws/namespaces/{namespace}/pods/{pod_name}-0/container/{container_name}/exec".format(
                namespace=notebook.namespace, pod_name=notebook.uuid, container_name=notebook.uuid
            )
            notebook.pod_ips = ip_maps.get(notebook.uuid, [])
            notebook.servers.extend(self.get_servers(notebook, service_map, probes_success))

    @log_background_task_exception
    def suspended_by_billing(self, uuid: str):
        """
        暂停容器实例
        :param uuid:
        :return:
        """
        stop_notebooks_in_background_by_billing_action(uuid)

    @log_background_task_exception
    def resume_by_billing(self, uuid: str):
        """
        恢复作业, 异步后台作业, 此时的session已经关闭
        :param uuid:
        :return:
        """
        start_notebooks_in_background_by_billing_action(uuid)

    def patch_os_disk(self, notenooks: Sequence[Notebook]):
        """
        :param Seq:
        :return:
        """
        notebooks_without_os_disk = [notebook for notebook in notenooks
                                     if not notebook.replica_specs.custom_system_disk_size]
        if not notebooks_without_os_disk:
            return

        notebooks_without_os_disk_pod_ids = [notebook.uuid + "-0" for notebook in notebooks_without_os_disk]
        metrics = {k[:-2]: v for k, v in PromethusClient().get_pod_node(notebooks_without_os_disk_pod_ids).items()}
        if not metrics:
            return

        nodes_static = NodeStaticInfo.all_by_ids(list(set(metrics.values())), session_=self.session)
        nodes_static_m = {node.hostname: node for node in nodes_static}
        for notebook in notebooks_without_os_disk:
            if node_static := nodes_static_m.get(metrics.get(notebook.uuid)):
                notebook.replica_specs.custom_system_disk_size = node_static.overlay2_size

    def patch_delete_time(self, notebooks: Sequence[Notebook]):
        """"""
        if app.settings.NOTEBOOK_RETENTION_TIME <= 0:
            return
        for notebook in notebooks:
            if notebook.status == NotebookStatus.Suspended and notebook.auto_delete_time is None:
                notebook.auto_delete_time = notebook.updated_at + timedelta(days=15)
