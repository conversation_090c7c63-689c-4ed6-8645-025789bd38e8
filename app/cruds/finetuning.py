import json
import os
import traceback
import time
from typing import List

from fastapi import BackgroundTasks
from sqlmodel import Session, select

import app
from app import logger
from app.apps.finetuning.exceptions import ModelNotFound, DataNotFound, ModelNotIdentify
from app.core.db import get_db_session_local
from app.core.kube.api.cm import create_cm
from app.core.models import QingcloudUser, BillingBase, ReplicaSpecCreate, VolumeSpecCreate
from app.core.qingcloud.maas import MaasClient
from app.core.utils import generate_random_string
from app.cruds.trains import TrainCRUD
from app.models.finetuning import Finetuning, FinetuningParams
from app.models.trains import TrainCreate, Train, TrainStatus

model_template = {
    "qwen3": "qwen3",
    "qwen2": "qwen",
    "qwen2_5_vl": "qwen2_vl",
    "deepseek_v3": "deepseek3",
    "deepseek_v2": "deepseek",
    "llama2": "llama2",
    "llama3": "llama3",
    "llama4": "llama4",
    "baichuan2": "baichuan2",
}

acceleration = {
    "flash_attn": "fa2",
    "use_unsloth": "True",
    "enable_liger_kernel": "True"
}

support_model = ["baichuan2","chatglm3","cohere","deepseek","deepseek3","deepseekr1",
                 "falcon","falcon_h1","gemma/gemma2","gemma3/gemma3n","glm4/glmz1","glm4v",
                 "granite3","hunyuan","index","intern2","intern_vl","kimi_vl",
                 "llama2","llama3","llama4","mllama","llava","llava_next","llava_next_video",
                 "mimo","cpm/cpm3/cpm4","minicpm_o/minicpm_v","ministral","mistral","mistral_small",
                 "paligemma","phi","phi_small","phi4","pixtral","qwen","qwen2","qwen3","qwen2_audio",
                 "qwen2_omni","qwen2_vl","seed_coder","skywork_o1","telechat2","xverse","yi","yi_vl",
                 "yuan"]



def get_model_template(model_name_or_path):
    with open(f"{model_name_or_path}/config.json") as f:
        model_config = json.loads(f.read())
        model_type = model_config["model_type"]
        if model_type == "llama":
            if int(model_config["bos_token_id"]) == 1:
                result = "llama2"
            elif int(model_config["bos_token_id"]) == 128000:
                result = "llama3"
            elif int(model_config["bos_token_id"]) == 200000:
                result = "llama4"
            else:
                raise ModelNotIdentify()
        else:
            result = model_template.get(model_type,"")
        if result not in support_model:
            raise ModelNotIdentify()
        return result


def parse_dataset(dataset):
    data_files = []
    data_info = dict()
    for data_path in dataset:
        host_data_path = os.path.join(app.settings.QINGCLOUD_GPSE_HOSTPATH_BACKEND_HOSTPATH, data_path.strip("/"))
        if not os.path.exists(host_data_path):
            raise DataNotFound(message=f"{data_path} 数据集不存在")
        data_path = "/" + data_path.strip("/")
        if os.path.isdir(host_data_path):
            sub_files = os.listdir(host_data_path)
            for filename in sub_files:
                if filename.endswith(".json") or filename.endswith(".jsonl"):
                    data_info[os.path.join(data_path, filename)] = {"file_name": os.path.join(data_path, filename)}
                    data_files.append(os.path.join(data_path, filename))
        else:
            if data_path.endswith(".json") or data_path.endswith(".jsonl"):
                data_info[data_path] = {"file_name": data_path}
                data_files.append(data_path)
    return data_files, data_info


FinetuningUUIDPrefix = 'ft-'
deepspeed = {
    "ZeRO-0": "examples/deepspeed/ds_z0_config.json",
    "ZeRO-2": "examples/deepspeed/ds_z2_config.json",
    "ZeRO-2+offload": "examples/deepspeed/ds_z2_offload_config.json",
    "ZeRO-3": "examples/deepspeed/ds_z3_config.json",
    "ZeRO-3+offload": "examples/deepspeed/ds_z3_offload_config.json"
}


class FinetuningCrud():
    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session: Session = session
        self.user = user

    def generate_ft_id(self, exclude_ids=None):
        while True:
            ft_task_id = "ft-" + generate_random_string()
            logger.info("get ft_id [%s]", ft_task_id)
            finetuning = self.session.exec(select(Finetuning).where(Finetuning.ft_task_id==ft_task_id)).all()
            if finetuning:
                continue
            if exclude_ids and ft_task_id in exclude_ids:
                continue
            return ft_task_id

    def get(self, ft_task_id):
        finetuning = self.session.exec(select(Finetuning).where(Finetuning.ft_task_id==ft_task_id)).one_or_none()
        return finetuning

    def create_finetuning(self, finetuning_params: FinetuningParams, train: TrainCRUD, background_tasks: BackgroundTasks):
        namespace = self.user.user_id.lower()
        ft_id = self.generate_ft_id()
        user_model_name_or_path = finetuning_params.model_name_or_path
        finetuning_params.output_dir = "/" + finetuning_params.output_dir.strip("/") + "/" + ft_id
        try:
            finetuning_params.deepspeed = deepspeed[finetuning_params.deepspeed]
            if finetuning_params.model_id:
                model_info = MaasClient().get_public_model_detail(finetuning_params.model_id)
                if model_info:
                    finetuning_params.model_name_or_path = f"{model_info['provider']}/{model_info['name']}"
                else:
                    raise ModelNotFound()
                finetuning_params.model_name_or_path = os.path.join(app.settings.POD_MODEL_NAME_MOUNT_PATH,
                                                                    finetuning_params.model_name_or_path)
                full_model_name_or_path = finetuning_params.model_name_or_path
            else:
                full_model_name_or_path = os.path.join(app.settings.QINGCLOUD_GPSE_HOSTPATH_BACKEND_HOSTPATH,
                                                                    finetuning_params.model_name_or_path.strip("/") )
                finetuning_params.model_name_or_path = full_model_name_or_path
            finetuning_params.template = get_model_template(full_model_name_or_path)
        except Exception as _:
            logger.error(traceback.format_exc())
            raise ModelNotFound()
        dataset = finetuning_params.dataset.split(",")

        data_files, data_info = parse_dataset(dataset)
        if not data_files:
            raise DataNotFound(message=f"{dataset} 无可用数据集")
        finetuning_params.dataset = ",".join(data_files)

        if finetuning_params.eval_dataset:
            evl_data_files, evl_data_info = parse_dataset(dataset)
            data_info.update(evl_data_info)
            finetuning_params.val_size = "0"
            finetuning_params.eval_dataset = ",".join(evl_data_files)
        if finetuning_params.finetuning_type == "lora":
            with open("/code/app/apps/finetuning/lora_sft.yaml") as f:
                finetuning_template = f.read()
        else:
            with open("/code/app/apps/finetuning/full_sft.yaml") as f:
                finetuning_template = f.read()

        cm = finetuning_template.format(**finetuning_params.model_dump())
        if finetuning_params.acceleration and finetuning_params.acceleration in acceleration:
            cm = cm + "\n" + f"{finetuning_params.acceleration}: {acceleration.get(finetuning_params.acceleration)}"

        if not create_cm(namespace=namespace, name=ft_id, data=cm):
            logger.error("create cm [%s]", ft_id)
            raise RuntimeError("create cm [%s]" % ft_id)
        if not create_cm(namespace=namespace,name=f"{ft_id}-datainfo", data=json.dumps(data_info), filename="dataset_info.json"):
            logger.error("create cm [%s]", ft_id)
            raise RuntimeError("create cm [%s]" % f"{ft_id}-datainfo")

        harbor_host = app.settings.DOCKER_REGISTRY
        image = f"{harbor_host}/{app.settings.ft_image}"
        command = "cd /app;FORCE_TORCHRUN=1 llamafactory-cli train /root/conf/config.yaml"
        if finetuning_params.command:
            command = finetuning_params.command
        finetuning_params.replica_specs[0].replica_type = "Worker"
        data = TrainCreate(user_id=self.user.user_id,namespace = namespace, image=image, image_type="custom",
                           endpoint="pytorchjobs",
                           command=command,
                           replica_specs=finetuning_params.replica_specs)
        data.name = finetuning_params.name

        data.active_deadline_seconds = 86400 * 7
        data.job_type = "FT"
        data.auto_renew = False
        data.duration = 3600
        data.next_charge_mode = 'elastic'
        filesets = set()
        if user_model_name_or_path:
            filesets.add(user_model_name_or_path.strip("/").split("/")[0])
        if finetuning_params.eval_dataset:
            filesets.add(finetuning_params.eval_dataset.strip("/").split("/")[0])
        if finetuning_params.output_dir:
            filesets.add(finetuning_params.output_dir.strip("/").split("/")[0])
        if finetuning_params.dataset:
            filesets.add(finetuning_params.dataset.strip("/").split("/")[0])
        logger.info(f"will be mount filesets: {filesets}")
        for fileset in filesets:
            if fileset == "maasfile":
                continue
            if fileset:
                fileset_mount_path = fileset if fileset.startswith("/") else f"/{fileset}"
                data.volume_specs = [VolumeSpecCreate(file_set=fileset,volume_type='GPFS', mount_path=fileset_mount_path)]
        data.volume_specs.append(VolumeSpecCreate(file_set=ft_id,volume_type='CM', mount_path="/root/conf"))
        data.volume_specs.append(VolumeSpecCreate(file_set=f"{ft_id}-datainfo", volume_type='CM', mount_path="/app/data"))
        data.volume_specs.append(VolumeSpecCreate(file_set=app.settings.POD_MODEL_NAME_MOUNT_PATH,
                                                  mount_path=app.settings.POD_MODEL_NAME_MOUNT_PATH,
                                                  volume_type="LOCAL_PATH", quota=1,
                                                  permission='ro'))


        data.root_user_id = self.user.root_user_id

        train: Train = train.create(data, uuid=ft_id)
        finetuning: Finetuning = Finetuning(**finetuning_params.model_dump(),ft_task_id=ft_id)
        finetuning.created_at = train.created_at
        finetuning.updated_at = train.updated_at
        finetuning.cpu = train.replica_specs[0].custom_cpu
        finetuning.memory = train.replica_specs[0].custom_memory
        finetuning.gpu = train.replica_specs[0].custom_gpu
        finetuning.gpu_model = train.replica_specs[0].custom_gpu_type
        self.session.add(finetuning)
        self.session.commit()
        if finetuning_params.save_model:
            background_tasks.add_task(self.save_model, ft_id, ["txt2txt"], finetuning_params.output_dir, self.user.user_id)
        return finetuning

    @staticmethod
    def save_model(name: str, tags: list[str], host_path:str, user_id:str):
        session = get_db_session_local()
        try:
            while True:
                train: Train = session.exec(select(Train).where(Train.uuid == name)).one_or_none()
                session.refresh(train)
                logger.debug(f"get train {train.status}")
                if train.status in [TrainStatus.Succeeded, TrainStatus.Completed]:
                    MaasClient().create_user_model(name, tags, host_path, user_id)
                    logger.info(f"create user model success: {name}")
                    break
                elif train.status in [TrainStatus.Running, TrainStatus.Creating, TrainStatus.Created, TrainStatus.Pending,TrainStatus.Inqueuing]:
                    time.sleep(5)
                else:
                    logger.info("finetuning failed not save model")
                    break
        except Exception as _:
            logger.error(traceback.format_exc())
            logger.error("save model [%s] failed", name)
        finally:
            session.close()




