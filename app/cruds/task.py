import time

from app import logger
from app.core.exceptions import TaskNotFoundException
from app.core.models import TaskInfo
from app.core.rlock import RedisClient


class TaskService:
    """
    A task object, saved in the redis cache.

    {
        name: str,
        description: str,
        status: bool,
        progress: int
        result: {
            key: value
        }
    }


    """

    redis = RedisClient()
    expire_time = 60 * 60 * 24

    def __init__(self, task_info: TaskInfo):
        self.task_info = task_info

    @staticmethod
    def generate_task_id(id_: str):
        """
        generate the task id
        :return:
        """
        return f"aicp:task:{id_}"

    @property
    def redis_task_id(self):
        """
        generate the task id
        :return:
        """
        return self.generate_task_id(self.task_info.id)

    @classmethod
    def get_redis_task_id_by_id(cls, id_: str):
        """
        get the redis task id by the task id
        :param id_:
        :return:
        """
        return cls.generate_task_id(id_)

    @classmethod
    def create(cls, name: str, description: str):
        """
        create a new task
        :param name:
        :param description:

        :return: TaskService
        """
        task_info = TaskInfo(name=name, description=description)
        task = cls(task_info)
        cls.redis.set(task.redis_task_id, task_info.dumps(), ex=cls.expire_time)
        return task

    @classmethod
    def get(cls, id_: str):
        """
        get the task by id, raise TaskNotFoundException if not found
        :param id_:

        :return: TaskService
        """
        if task_info := cls.redis.get(cls.get_redis_task_id_by_id(id_)):
            return cls(TaskInfo.loads(data=task_info))
        raise TaskNotFoundException(id_)

    def remove(self):
        """
        remove the task from the redis
        """
        self.redis.delete(self.redis_task_id)

    def update(self):
        """
        update the task info
        """
        logger.debug(f"update task: {self}")
        self.task_info.update_at = int(time.time())
        self.redis.set(self.redis_task_id, self.task_info.dumps(), ex=self.expire_time, nx=False, xx=True)

    def update_progress(self, progress: int = 0):
        """
        update the progress of the task
        :param progress:
        """
        self.task_info.progress = progress
        self.update()

    def finish(self, status: str = "Success", result: object = None):
        """
        finish the task
        :param status: the status of the task
        :param result: the result of the task
        """
        self.task_info.finished = True
        self.task_info.progress = 100
        self.task_info.result = result
        self.task_info.status = status
        self.update()

    def __str__(self):
        return f"{self.task_info.id} - {self.task_info.name} - {self.task_info.status} - {self.task_info.progress}"
