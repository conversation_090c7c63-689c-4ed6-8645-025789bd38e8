from datetime import datetime
from typing import Any

from sqlmodel import select, or_, Session

from app import logger
from app.core.middlewares.auth.qingcloud_auth import QingcloudUser
from app.core.utils import generate_random_string
from app.models.data_set import DataSet, DataSetCreate


class DataSetCrud:
    """
    dataset CRUD
    """

    def __init__(self, session: Session):
        self.session: Session = session


    def generate_da_id(self, exclude_ids=None):
        while True:
            da_id = "ds-" + generate_random_string()
            logger.info(f"get da_id {da_id}")
            if exclude_ids and da_id and exclude_ids:
                continue
            return da_id

    def create_data_set(self, data_set_create: DataSetCreate, user: QingcloudUser) -> Any:
       data_set = DataSet(**data_set_create.dict())
       data_set.owner = user.user_id
       data_set.da_id = self.generate_da_id()
       data_set.create_time = datetime.now()
       self.session.add(data_set)
       self.session.commit()
       self.session.refresh(data_set)
       return data_set



    def delete_da(self, da_id: str, user: QingcloudUser) -> Any:
        results = self.session.exec(select(DataSet).where(DataSet.da_id == da_id,
                                                          DataSet.owner == user.user_id))
        da = results.one()
        self.session.delete(da)
        self.session.commit()
        return  True


    def get_da_by_user(self, user: str, order_by="create_time", reverse=False,
                       offset=0, limit=20, search_word=None):

        sql = select(DataSet)
        if search_word:
            sql = sql.where(or_(DataSet.data_name.like(f"%{search_word}%"),
                                DataSet.da_id.like(f"%{search_word}%")))
        if reverse:
            order_by = getattr(DataSet, order_by).desc()
        else:
            order_by = getattr(DataSet, order_by).asc()

        sql = sql.order_by(order_by)
        sql = sql.offset(offset).limit(limit)
        result = self.session.exec(sql)
        data_set = result.fetchall()
        logger.info(f"get data_set{data_set}")
        return data_set


    def update_data(self, update_da: DataSetCreate, user: QingcloudUser) -> Any:
        results = self.session.exec(select(
            DataSet).where(DataSet.da_id == update_da.da_id,
                           DataSet.owner == user.user_id))
        rg = results.one()
        if update_da.data_name:
            rg.data_name = update_da.data_name
        if update_da.data_size:
            rg.data_size = update_da.data_size
        if update_da.data_type:
            rg.data_type = update_da.data_type
        if update_da.storage_path:
            rg.storage_path = update_da.storage_path
        if update_da.notebook_path:
            rg.notebook_path = update_da.notebook_path
        if update_da.publisher:
            rg.publisher = update_da.publisher
        if update_da.description:
            rg.description = update_da.description
        if rg:
            self.session.add(rg)
            self.session.commit()
            self.session.refresh(rg)
        return rg

    def get_data_count(self, search_word=None):
        cur = self.session.query(DataSet)
        if search_word:
            result = cur.where(
                or_(DataSet.data_name.like(f"%{search_word}%"),
                    DataSet.da_id.like(f"%{search_word}%"))).count()
        else:
            result = cur.count()
        return result
