from typing import Type

from sqlmodel import SQLMode<PERSON>, Session, select

from app.core.models import QingcloudUser


class BaseCrud:
    model: Type[SQLModel]

    def __init__(self, session: Session, user: QingcloudUser = None):
        self.session = session
        self.user = user

    def get(self, id: int):
        return self.session.get(self.model, id)

    def get_multi(self, skip=0, limit=10):
        return self.session.exec(select(self.model).offset(skip).limit(limit)).all()

    def create(self, obj: SQLModel):
        self.session.add(obj)
        self.session.commit()
        self.session.refresh(obj)
        return obj

    def update(self, obj: SQLModel):
        self.session.add(obj)
        self.session.commit()
        self.session.refresh(obj)
        return obj

    def delete(self, obj: SQLModel):
        self.session.delete(obj)
        self.session.commit()
        return True
