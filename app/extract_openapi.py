import argparse
import json
import os
import sys
import yaml

parser = argparse.ArgumentParser(prog="extract_openapi.py")
parser.add_argument("--app-dir", help="Directory containing the app", default="/code")
parser.add_argument("--out", help="Output file ending in .json or .yaml", default="openapi.json")
parser.add_argument("--branch", help="Branch to use for versioning", default="master")
if __name__ == "__main__":
    os.environ.setdefault("export_swagger_mode", "true")
    core_path = os.path.join(os.path.dirname(__file__), "core")
    sys.path.append(core_path)
    from config import Settings
    for k, v in Settings.schema()["properties"].items():
        if "default" not in v:
            if v.get("type") == "string":
                os.environ.setdefault(list(v['env_names'])[0], "")
            elif v.get("type") == "integer":
                os.environ.setdefault(list(v['env_names'])[0], "0")
            else:
                print(v)
    sys.path.remove(core_path)

    args = parser.parse_args()
    sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

    from app.main import application as app
    openapi = app.openapi()
    version = openapi.get("openapi", "unknown version")

    print(f"writing openapi spec v{version}")
    with open(args.out, "w") as f:
        if args.out.endswith(".json"):
            json.dump(openapi, f, indent=2)
        else:
            yaml.dump(openapi, f, sort_keys=False)

    print(f"spec written to {args.out}")
    os._exit(0)
