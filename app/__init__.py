import os
from datetime import date, datetime
from functools import partial

import yaml
from app.core.config import Settings
from app.core.loggers import logger


def update_config(config_data):
    global settings
    dict_data = yaml.safe_load(config_data)
    settings = Settings(**dict_data)


from app.notify_config import watch_config_file


config_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "conf/api_server.yaml")
if os.getenv("ENV_CONF_PATH"):
    config_path = os.environ["ENV_CONF_PATH"]
settings = Settings.from_yaml(config_path)

logger.debug(settings)
