RESOURCE_NOT_FOUND = 11
RESOURCE_NOT_ENOUGH = 21
PERMISSION_DENIED = 31
INTERNAL_ERROR = 41
SOURCE_NOT_EXIST = 0



RESOURCE_GROUP_NOT_FOUND = "资源组不存在"
RESOURCE_NODE_NOT_FOUND = "资源节点不存在"
BM_NODE_NOT_FOUND = "裸金属实例不存在"
NOT_ALLWORD_TO_DO = "当前状态【%s】不支持删除"
PRODUCT_NOT_FOUND = "产品不存在"
INSTANCE_NOT_FOUND = "容器实例不存在"
SOURCE_NOT_EXIST_MASSAGE = "当前环境走代理服务，无法换源"
CREATE_RESOURCE_FAILED = "添加专属资源组节点失败"

NOT_ENOUGH_NODE = "没有足够的节点资源可用"
RESUME_PERMISSION_DENIED =  "当前节点不支持恢复"

ERROR_REMOVE_NODE = "删除资源组节点失败"
ERROR_DELETE_RESOURCE = "删除资源组失败"

GPU_RESOURCE_NOT_FOUND = 100
ERROR_GPU_NODE_NOT_FOUND = "GPU资源节点不存在"
RESOURCE_IS_RUNNING = "专属节点上有资源[%s]运行，请先删除资源"

RESOURCE_MUST_BE_HAS_NDOE = "如果需要清空资源组的节点请直接删除资源组"

ERROR_GPU_FAULT_RECORDS_NOT_FOUND = "GPU 故障记录不存在"

NOT_SUPPORT_DELETE_NODE_WITH_GROUP = "当前不支持删除资源组节点，请直接删除资源组"
