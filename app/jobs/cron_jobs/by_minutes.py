import sys

sys.path.append("/code")
from contextlib import asynccontextmanager
from zoneinfo import ZoneInfo

from sqlalchemy import text

from app.apps.gpu.utils import k8s_remote_shell, k8s_list_namespaced_pod, filtered_dcgm_util, send_k8s_event, \
    k8s_get_node_name, get_k8s_node_scheduling, set_k8s_node_scheduling, filtered_schedule_util

from app.core.prometheus.client import PrometheusClient


from app.core.utils import generate_random_string

from sqlalchemy.ext.asyncio import create_async_engine

import asyncio

from sqlalchemy.orm import sessionmaker
from sqlmodel.ext.asyncio.session import AsyncSession

from app import settings, logger

DEFAULT_TZINFO = ZoneInfo("Asia/Shanghai")

async_engine = create_async_engine(
    settings.DB_ASYNC_CONNECTION_STR,
    echo=False,
    future=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600,
    pool_pre_ping=True,
)


# Demo
@asynccontextmanager
async def get_async_session() -> AsyncSession:
    async_session = sessionmaker(bind=async_engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session() as session:
        yield session
        await session.commit()


# # it's an example for insert a record to database by minutes
# async def insert_to_timer_log_db():
#     # write a record to the database using get_async_session
#     async with get_async_session() as session:
#         # do something
#         # 使用原生sql 插入一条数据
#         a = await session.exec(text("select * from gpu_static_info"))
#         for i in a:
#             print(i)

async def get_k8s_nodes() -> AsyncSession:
    async with get_async_session() as session:
        namespace = "gpu-operator"
        pods = k8s_list_namespaced_pod(namespace)

        for pod in pods:
            node_id = k8s_get_node_name(pod, namespace)
            node_scheduling = get_k8s_node_scheduling(node_id)

            # 测试用
            # if node_scheduling is True:
            #     set_k8s_node_scheduling(node_id, False, "remove")
            if node_scheduling is None:
                gpu_static_info = await session.exec(
                    text("select count(gpu_uuid) from gpu_static_info where hostname='" + node_id + "'"))
                cursor_gou_num = gpu_static_info.first()[0]

                # 确保能获取到有效 GPU 网卡和有效的静态卡信息后，再进行处理
                total_gpu_num = k8s_remote_shell(pod, namespace)
                if int(total_gpu_num) > 0 and int(cursor_gou_num) > 0:
                    if int(total_gpu_num) != int(cursor_gou_num):
                        records_id = "fault-" + generate_random_string()
                        gpu_node_id = node_id
                        gpu_xid = "79"
                        fault_status = "0"

                        gpu_fault_records = await session.exec(
                            text(
                                "select count(gpu_uuid) from gpu_fault_records where "
                                "gpu_node_id='" + node_id + "' and "
                                                            "gpu_xid='" + gpu_xid + "' and "
                                                                                    "fault_status = '" + fault_status + "'"))
                        fault_records = int(gpu_fault_records.first()[0])
                        if fault_records == 0:
                            logger.info(
                                "unable to determine the device handle for gpu %s <=> %s" % (total_gpu_num, cursor_gou_num))

                            reason = "DroppedCardsNeedToBeCheckedImmediately"
                            message = (
                                          "[nvidia-smi] Through the aicp backend detection in %s, it is found that nvidia-smi "
                                          "has a card dropped but it is not in the DCGM exporter ") % node_id
                            gpu_uuid = ""

                            # 开始通过 dcgm 来查到哪个 gpu 不能正常工作
                            gpu_docs = await session.exec(
                                text("select gpu_uuid from gpu_static_info where hostname='" + node_id + "'"))
                            for gpu in gpu_docs:
                                dcgm_fi_dev_fb_total = "DCGM_FI_DEV_FB_TOTAL"
                                fi_dev_fb_total = PrometheusClient().expression_execute(dcgm_fi_dev_fb_total)
                                if fi_dev_fb_total:
                                    uuid = gpu[0]
                                    dev_fb_total = filtered_dcgm_util(fi_dev_fb_total, node_id, uuid)
                                    if len(dev_fb_total) == 0:
                                        gpu_uuid = uuid
                                        message = (
                                            "[DCGM] Through aicp detection in %s, it was found that nvidia-smi "
                                            "has a card drop and the DCGM exporter %s ") % (node_id, gpu_uuid)

                            gpu_model_name = ""
                            gpu_nvml_version = ""
                            gpu_cuda_driver_version = ""
                            gpu_device = ""
                            fault_treatment = "该任务辅助巡检发现，暂未开始处理"
                            fault_maintainer_id = "admin"
                            fault_maintainer_name = "admin"

                            gpu_uuid = gpu_uuid
                            gpu_alert_summary = message

                            insert_fault = ("INSERT INTO gpu_fault_records(records_id, gpu_node_id, gpu_uuid, "
                                            "gpu_xid,gpu_model_name, gpu_nvml_version, gpu_cuda_driver_version, gpu_device, "
                                            "gpu_alert_summary, fault_status, fault_treatment, fault_maintainer_id, "
                                            "fault_maintainer_name) "
                                            "VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s');") % (
                                               records_id, gpu_node_id, gpu_uuid, gpu_xid, gpu_model_name, gpu_nvml_version,
                                               gpu_cuda_driver_version, gpu_device, gpu_alert_summary, fault_status,
                                               fault_treatment, fault_maintainer_id, fault_maintainer_name)
                            await session.exec(text(insert_fault))

                            # 无论有没有在 dcgm 查询到该故障设备，事件告警依然被触发
                            node_status = await session.exec(
                                text("select uid from node_status where node_id='" + node_id + "'"))
                            uid = node_status.first()[0]

                            if uid is not None:
                                logger.info("node_id: %s, node_uid: %s, message: %s" % (node_id, uid, message))
                                send_k8s_event(node_id, uid, reason, message)

                            # 设置禁止调度及污点
                            set_k8s_node_scheduling(node_id, True, "set")

        namespace = "npu-exporter"
        pods = k8s_list_namespaced_pod(namespace, "npu-exporter-*")

        for pod in pods:
            shell = "npu-smi info -l | grep 'Total Count' | awk -F':' '{print $2}' | tr -d ' '"
            node_id = k8s_get_node_name(pod, namespace)
            node_scheduling = get_k8s_node_scheduling(node_id)

            if node_scheduling is None:
                gpu_static_info = await session.exec(
                    text("select count(gpu_uuid) from gpu_static_info where hostname='" + node_id + "'"))
                cursor_gou_num = gpu_static_info.first()[0]

                # 确保能获取到有效 GPU 网卡和有效的静态卡信息后，再进行处理
                total_gpu_num = k8s_remote_shell(pod, namespace, shell)
                if int(total_gpu_num) > 0 and int(cursor_gou_num) > 0:
                    if int(total_gpu_num) != int(cursor_gou_num):
                        records_id = "fault-" + generate_random_string()
                        gpu_node_id = node_id
                        gpu_xid = "0x40F84E00"
                        fault_status = "0"

                        gpu_fault_records = await session.exec(
                            text(
                                "select count(gpu_uuid) from gpu_fault_records where "
                                "gpu_node_id='" + node_id + "' and "
                                                            "gpu_xid='" + gpu_xid + "' and "
                                                                                    "fault_status = '" + fault_status + "'"))
                        fault_records = int(gpu_fault_records.first()[0])
                        if fault_records == 0:
                            logger.info(
                                "unable to determine the device handle for npu %s <=> %s" % (
                                    total_gpu_num, cursor_gou_num))

                            reason = "DroppedCardsNeedToBeCheckedImmediately"
                            message = (
                                          "[npu-smi] device does not ready, the card may be dropped. node -> %s") % node_id
                            gpu_uuid = ""

                            # 开始通过 dcgm 来查到哪个 gpu 不能正常工作
                            gpu_docs = await session.exec(
                                text("select gpu_uuid from gpu_static_info where hostname='" + node_id + "'"))
                            for gpu in gpu_docs:
                                fi_dev_fb_total = PrometheusClient().expression_execute("npu_chip_info_error_code")
                                if fi_dev_fb_total:
                                    uuid = gpu[0]
                                    dev_fb_total = filtered_schedule_util(fi_dev_fb_total, node_id, uuid, 'node',
                                                                          'vdie_id')
                                    if len(dev_fb_total) == 0:
                                        gpu_uuid = uuid
                                        message = ("[npu-smi] device does not ready, the card may be dropped."
                                                   " node -> %s, gpu_uuid -> %s") % (node_id, gpu_uuid)

                            gpu_model_name = ""
                            gpu_nvml_version = ""
                            gpu_cuda_driver_version = ""
                            gpu_device = ""
                            fault_treatment = "该任务辅助巡检发现，暂未开始处理"
                            fault_maintainer_id = "admin"
                            fault_maintainer_name = "admin"

                            gpu_uuid = gpu_uuid
                            gpu_alert_summary = message

                            insert_fault = ("INSERT INTO gpu_fault_records(records_id, gpu_node_id, gpu_uuid, "
                                            "gpu_xid,gpu_model_name, gpu_nvml_version, gpu_cuda_driver_version, gpu_device, "
                                            "gpu_alert_summary, fault_status, fault_treatment, fault_maintainer_id, "
                                            "fault_maintainer_name) "
                                            "VALUES ('%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s','%s');") % (
                                               records_id, gpu_node_id, gpu_uuid, gpu_xid, gpu_model_name,
                                               gpu_nvml_version,
                                               gpu_cuda_driver_version, gpu_device, gpu_alert_summary, fault_status,
                                               fault_treatment, fault_maintainer_id, fault_maintainer_name)
                            await session.exec(text(insert_fault))

                            # 无论有没有在 dcgm 查询到该故障设备，事件告警依然被触发
                            node_status = await session.exec(
                                text("select uid from node_status where node_id='" + node_id + "'"))
                            uid = node_status.first()[0]

                            if uid is not None:
                                logger.info("node_id: %s, node_uid: %s, message: %s" % (node_id, uid, message))
                                send_k8s_event(node_id, uid, reason, message)

                            # 设置禁止调度及污点
                            set_k8s_node_scheduling(node_id, True, "set")
async def main():
    """
    Main function
    """
    coroutines = [
        asyncio.ensure_future(get_k8s_nodes())
        # asyncio.ensure_future(insert_to_timer_log_db()), # you should add your coroutine here
    ]
    await asyncio.gather(*coroutines)


if __name__ == '__main__':
    asyncio.run(main())
