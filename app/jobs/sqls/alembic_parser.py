import ast
import os
import sys


def is_empty_function(node):
    """
    检查函数体是否只有一个Pass节点
    :param node:
    :return:
    """
    return len(node.body) == 1 and isinstance(node.body[0], ast.Pass)


def analyze_migration_file(path):
    """
    检查迁移文件是否为空
    :param path:
    :return:
    """
    with open(path, "r", encoding="utf-8") as f:
        code = f.read()

    tree = ast.parse(code)

    upgrade_empty = False
    downgrade_empty = False

    for node in ast.walk(tree):
        if isinstance(node, ast.FunctionDef):
            if node.name == "upgrade":
                upgrade_empty = is_empty_function(node)
            elif node.name == "downgrade":
                downgrade_empty = is_empty_function(node)

    return upgrade_empty and downgrade_empty


if __name__ == "__main__":
    """
    exit code:
    0: empty migration, this means the migration file is not needed, delete it and skip the migration
    1: valid migration, keep the file and execute the migration
    2: file not found, exit with error
    
    other: error, keep the file and raise an error
    """

    if len(sys.argv) != 2:
        print("Usage: python alembic_parser.py <migration_file_path>")
        sys.exit(1)

    file_path = sys.argv[1]
    print(f"Analyzing migration file: {file_path}")

    if not os.path.exists(file_path):
        print(f"File not found: {file_path}")
        sys.exit(2)

    if analyze_migration_file(file_path):
        print("Empty migration detected")
        sys.exit(0)  # 返回0表示需要跳过迁移
    else:
        print("Valid migration detected")
        sys.exit(1)  # 返回1表示需要执行迁移
