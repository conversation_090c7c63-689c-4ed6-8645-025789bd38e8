
import os.path
import sys
from sqlalchemy import text

sys.path.append("/code")

from app.core.db import get_db_session, engine

BASE_PATH = os.path.dirname(os.path.abspath(__file__))


def init_sqls():
    INIT_SQL_PATH = os.path.join(BASE_PATH, "init.sql")
    with open(INIT_SQL_PATH, "r") as f:
        init_sql = text(f.read())
        with engine.connect() as conn:
            conn.execute(init_sql)
            conn.commit()


def upgrade_sqls():
    UPGRADE_SQL_PATH = os.path.join(BASE_PATH, "upgrade.sql")
    with open(UPGRADE_SQL_PATH, "r") as f:
        upgrade_sql = text(f.read())
        with engine.connect() as conn:
            conn.execute(upgrade_sql)
            conn.commit()


if __name__ == "__main__":
    if sys.argv[1] == "init":
        init_sqls()
    elif sys.argv[1] == "upgrade":
        upgrade_sqls()
    else:
        print("Usage: python sqls.py [init|upgrade]")
        sys.exit(1)
