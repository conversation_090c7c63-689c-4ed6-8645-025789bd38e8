/*
 用于每次升级aicp时更新数据,
 尽可能使用 insert into ... on duplicate key update ... 语句
 */
/*
--  INSERT INTO public.gpu_dashboard_config (created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
-- VALUES ('2024-11-22 13:19:56.000', '2024-06-28 14:38:38.000', '6', 'DCU 监控', 'LJSKH347J', 'dcu-exporter', 'grafana_ip:nodeport', '0', '0,0', '&from=now-1h&to=now')
-- ON CONFLICT (dashboard_id) DO UPDATE
-- SET
--   created_at = EXCLUDED.created_at,
--   updated_at = EXCLUDED.updated_at,
--   dashboard_name = EXCLUDED.dashboard_name,
--   dashboard_url_path = EXCLUDED.dashboard_url_path,
--   web_router = EXCLUDED.web_router,
--   grafana_address = EXCLUDED.grafana_address,
--   enable_dashboard = EXCLUDED.enable_dashboard,
--   enable_table_view = EXCLUDED.enable_table_view,
--   grafana_params = EXCLUDED.grafana_params;
*/