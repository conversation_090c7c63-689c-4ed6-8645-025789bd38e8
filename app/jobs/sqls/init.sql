/*
 用于第一次写入数据使用, 此时数据库已经创建好,
 */
 INSERT INTO public.gpu_error_codes (created_at,updated_at,code_id,gpu_err_id,gpu_xid,gpu_err_priority,gpu_err_desc,gpu_suggestions,gpu_err_strategy,gpu_product) VALUES
     ('2024-04-02 11:17:11.000','2024-04-02 11:17:11.000','13','13','13','Warning','Graphics Engine Exception. 通常是数组越界、指令错误，小概率是硬件问题','尝试重新提交负载并观察XID错误是否消失。若错误仍有发生，尝试自检代码或分析日志，确认是否由代码引入的XID错误。若确认代码无误且问题仍然发生，就需要联系技术支持人员解决。','0','nvidia'),
     ('2024-04-02 11:20:42.000','2024-04-02 11:20:42.000','31','31','31','Warning','GPU memory page fault. 通常是应用程序的非法地址访问，极小概率是驱动或者硬件问题。','尝试重新提交负载并观察XID错误是否消失。若错误仍有发生，尝试自检代码或分析日志，确认是否由代码引入的XID错误。若确认代码无误且问题仍然发生，就需要联系技术支持人员解决。','0','nvidia'),
     ('2024-04-02 11:30:04.000','2024-04-02 11:30:04.000','32','32','32','Error','Invalid or corrupted push buffer stream. 事件由PCIE总线上管理NVIDIA驱动和GPU之间通信的DMA控制器上报，通常是PCI质量问题导致，而非您的程序产生。','联系技术支持人员解决','0','nvidia'),
     ('2024-04-02 11:21:05.000','2024-04-02 11:21:05.000','43','43','43','Warning','GPU stopped processing. 通常是您应用自身错误，而非硬件问题。','尝试重新提交负载并观察XID错误是否消失。若错误仍有发生，尝试自检代码或分析日志，确认是否由代码引入的XID错误。若确认代码无误且问题仍然发生，就需要联系技术支持人员解决。','0','nvidia'),
     ('2024-04-02 11:21:51.000','2024-04-02 11:21:51.000','45','45','45','Warning','Preemptive cleanup, due to previous errors -- Most likely to see when running multiple cuda applications and hitting a DBE. 通常是您手动退出或者其他故障（硬件、资源限制等）导致的GPU应用退出，XID 45只提供一个结果，具体原因通常需要进一步分析日志。','尝试重新提交负载并观察XID错误是否消失。若错误仍有发生，尝试自检代码或分析日志，确认是否由代码引入的XID错误。若确认代码无误且问题仍然发生，就需要联系技术支持人员解决。','0','nvidia'),
     ('2024-04-02 11:33:28.000','2024-04-02 11:33:28.000','61','61','61','Error','Internal micro-controller breakpoint/warning. GPU内部引擎停止工作，您的业务已经受到影响。','联系技术支持人员解决','0','nvidia'),
     ('2024-04-02 11:35:43.000','2024-04-02 11:35:43.000','62','62','62','Error','Internal micro-controller halt. 与XID 16的触发场景类似。','联系技术支持人员解决','0','nvidia'),
     ('2024-04-02 11:22:23.000','2024-04-02 11:22:23.000','68','68','68','Warning','NVDEC0 Exception. 通常是硬件或驱动问题。','尝试重新提交负载并观察XID错误是否消失。若错误仍有发生，尝试自检代码或分析日志，确认是否由代码引入的XID错误。若确认代码无误且问题仍然发生，就需要联系技术支持人员解决。','0','nvidia'),
     ('2024-04-02 11:42:29.000','2024-04-02 11:42:29.000','74','74','74','Error','NVLINK Error. NVLink 硬件错误产生的XID，表明GPU已经出现严重硬件故障','下线维修','0','nvidia'),
     ('2024-04-02 11:44:05.000','2024-04-02 11:44:05.000','92','92','92','Error','High single-bit ECC error rate. 硬件或驱动故障。','尝试排除驱动故障，否则硬件可能出现问题，需要下线维修','0','nvidia'),
     ('2024-04-02 11:43:01.000','2024-05-15 19:41:13.000','79','79','79','Error','GPU has fallen off the bus。 GPU硬件检测到掉卡，总线上无法检测该GPU，表明该GPU已经出现严重硬件故障','下线维修','1','nvidia'),
     ('2024-04-02 11:45:53.000','2024-05-15 19:41:40.000','94','94','94','Critical','Contained ECC error. 当应用程序遭遇到GPU不可纠正的显存ECC错误时，NVIDIA错误抑制机制会尝试将错误抑制在发生硬件故障的应用程序，避免该错误影响GPU节点上运行的其他应用程序','当抑制机制成功抑制错误时，会产生该事件，仅出现不可纠正ECC错误的应用程序受到影响. 运行完成后，尝试重启恢复','1','nvidia'),
     ('2024-04-02 11:40:31.000','2024-05-15 19:42:10.000','63','63','63','Critical','ECC page retirement or row remapping recording event. 当应用程序遭遇到GPU显存硬件错误时，NVIDIA自纠错机制会将错误的内存区域retire或者 remap，retirement和remapped信息需记录到infoROM中才能永久生效。Volt架构：成功记录ECC page retirement事件到infoROM。Ampere架构：成功记录row remapping事件到infoROM。','自纠错机制会将错误恢复，如果情况未有改善，本次业务完成后，尝试重启实例恢复','0','nvidia'),
     ('2024-04-02 11:41:47.000','2024-05-15 19:42:15.000','64','64','64','Critical','ECC page retirement or row remapper recording failure. 与XID 63的触发场景类似。但XID 63代表retirement和remapped信息成功记录到了infoROM， XID 64代表该记录操作失败。','如果该情况未得到缓解，本次业务完成后，尝试重启实例恢复','0','nvidia'),
     ('2024-04-02 11:46:54.000','2024-05-15 19:42:21.000','95','95','95','Critical','Uncontained ECC error. 与XID 94的触发场景类似。但XID 94代表抑制成功，而XID 95代表抑制失败，表明运行在该GPU上的所有应用程序都已受到影响。','本次业务完成后，尝试重启实例恢复','0','nvidia'),
     ('2024-04-02 11:32:20.000','2024-05-15 19:42:25.000','48','48','48','Critical','Double Bit ECC Error（DBE）. 当GPU发生不可纠正的错误时，会上报此事件，该错误也会同时反馈给您的应用程序。','通常需要重置GPU或重启节点来清除这个错误。','0','nvidia'),
     ('2024-04-02 11:31:05.000','2024-05-15 19:44:08.000','38','38','38','Critical','Driver firmware error. 通常是驱动固件错误而非硬件问题。','联系平台支持人员解决，尝试解决驱动问题','0','nvidia'),
     ('2024-04-02 11:31:05.000','2024-05-15 19:44:08.000','0x40F84E00','0x40F84E00','0x40F84E00','Error','NPU has fallen off the bus。 NPU硬件检测到掉卡，总线上无法检测该NPU，表明该NPU已经出现严重硬件故障','下线维修','1','huawei');

INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 13:19:56.000', '2024-06-28 14:04:36.000', '3', 'NPU 监控', 'FSRKUBPIz', 'npu-exporter', 'grafana_ip:nodeport', '0', '0,0', '&from=now-1h&to=now');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 13:17:59.000', '2024-06-28 14:32:50.000', '1', 'GPU 监控', 'Oxed_c6Wz', 'nvidia-dcgm-exporter-dashboard', 'grafana_ip:nodeport', '0', '0,0', '&var-gpu=All');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 13:19:56.000', '2024-06-28 14:35:59.000', '5', 'Hexaflake 监控', 'vlvPlrgnk', 'hexaflake-gpu-metrics', 'grafana_ip:nodeport', '0', '0', '&var-gpu=0-0&var-node_name=hfk');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 13:19:56.000', '2024-06-28 14:38:20.000', '2', 'GPU 性能监控', 'wh1_1EfSk', 'nvidia-dcgm-performance-metric-dashboard', 'grafana_ip:nodeport', '0', '0', '&var-gpu=All');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 13:19:56.000', '2024-06-28 14:38:38.000', '4', '网卡监控', '5Zr9HhYSk', 'node-exporter-infiniband', 'grafana_ip:nodeport', '0', '0', '&from=now-1h&to=now');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 13:19:56.000', '2024-06-28 14:38:38.000', '6', 'DCU 监控', 'LJSKH347J', 'dcu-exporter', 'grafana_ip:nodeport', '0', '0,0', '&from=now-1h&to=now');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-28 14:41:31.000', '2024-06-28 14:41:31.000', '92', '自定义2', 'grafana dashboard uid', 'dashboard name', 'grafana_ip:nodeport', '0', '0', '');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 17:26:31.000', '2024-06-28 14:31:07.000', '91', '自定义1', 'grafana dashboard uid', 'dashboard name', 'grafana_ip:nodeport', '0', '0', '');
INSERT INTO public.gpu_dashboard_config
(created_at, updated_at, dashboard_id, dashboard_name, dashboard_url_path, web_router, grafana_address, enable_dashboard, enable_table_view, grafana_params)
VALUES('2024-06-27 17:26:31.000', '2024-06-28 14:36:05.000', '93', '自定义3', '暂未支持', '暂未支持', 'grafana_ip:nodeport', '0', '0', '&from=now-1h&to=now');
