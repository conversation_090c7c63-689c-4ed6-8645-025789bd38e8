from kr8s.asyncio.objects import APIObject


class NoteBookObj(APIObject):
    """NoteBook is the definition of a notebooks.
    """

    version = "kubeflow.org/v1beta1"
    endpoint = "notebooks"
    kind = "Notebook"
    plural = "notebooks"
    singular = "notebook"
    namespaced = True

    async def shut_down(self):
        """
        Shut down the notebook async.
        """
        await self.annotate({"kubeflow-resource-stopped": "true"})


class MpiJob(APIObject):
    """MpiJob is the definition of a mpijob.
    """

    version = "kubeflow.org/v1"
    endpoint = "mpijobs"
    kind = "MPIJob"
    plural = "mpijobs"
    singular = "mpijob"
    namespaced = True


class DeepspeedJob(APIObject):
    """MpiJob is the definition of a mpijob.
    """

    version = "kubeflow.org/v1"
    endpoint = "deepspeedjobs"
    kind = "DeepspeedJob"
    plural = "deepspeedjobs"
    singular = "deepspeedjob"
    namespaced = True

class MXJob(APIObject):
    """MXJob is the definition of a MXJob.
    """

    version = "kubeflow.org/v1"
    endpoint = "mxjobs"
    kind = "MXJob"
    plural = "mxjobs"
    singular = "mxjob"
    namespaced = True


class PaddleJob(APIObject):
    """PaddleJob is the definition of a PaddleJob.
    """

    version = "kubeflow.org/v1"
    endpoint = "paddlejobs"
    kind = "PaddleJob"
    plural = "paddlejobs"
    singular = "paddlejob"
    namespaced = True


class PyTorchJob(APIObject):
    """PyTorchJob is the definition of a PyTorchJob.
    """

    version = "kubeflow.org/v1"
    endpoint = "pytorchjobs"
    kind = "PyTorchJob"
    plural = "pytorchjobs"
    singular = "pytorchjob"
    namespaced = True


class TfJob(APIObject):
    """TfJob is the definition of a tfjob.
    """

    version = "kubeflow.org/v1"
    endpoint = "tfjobs"
    kind = "TFJob"
    plural = "tfjobs"
    singular = "tfjob"
    namespaced = True


class XGBoostJob(APIObject):
    """XGBoostJob is the definition of a XGBoostJob.
    """

    version = "kubeflow.org/v1"
    endpoint = "xgboostjobs"
    kind = "XGBoostJob"
    plural = "xgboostjobs"
    singular = "xgboostjob"
    namespaced = True
