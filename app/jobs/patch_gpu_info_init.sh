#!/bin/bash

#计算节点ib网卡个数
COMPUTE_IB_DEV_COUNT=0
#单个计算节点ib网卡带宽
COMPUTE_IB_DEV_TARE=0
#存储ib网卡个数
STORAGE_IB_DEV_COUNT=0
#存储ib网卡带宽
STORAGE_IB_DEV_RATE=0
#管理ib网卡个数
MANAGER_IB_DEV_COUNT=0
#管理ib网卡带宽
MANAGER_IB_DEV_RATE=0


NVLINK=1
annotate_name='qingcloud/node-nvidia-register'
annotate_ib='qingcloud/ib-register'
annotate_ib_info='qingcloud/ib-info'
k8s_nodes=`kubectl get node --no-headers=true -o custom-columns=NAME:.metadata.name`
delimiter=" "
k8s_nodes=(${k8s_nodes//$delimiter/})
flag="failed"
DEV_TYPE=NPU
NPU_INFO=910B
case "$DEV_TYPE" in
  "NVIDIA")
    echo "device is nvidia"
    for k8s_node in "${k8s_nodes[@]}"; do
    echo -e "\nprocess $k8s_node"
    output=`ssh -q -p 22 $k8s_node 'nvidia-smi --query-gpu=uuid,memory.total,name --format=csv,noheader  | paste -sd ":" ' `
    if echo "$output" | grep -q $flag
    then
        echo "no gpu"
        annotate_value="0,0,0"
        ib_annotate_value="0,0,0,0,0"
    else
        annotate_value=$output
        ib_annotate_value="$COMPUTE_IB_DEV_COUNT,$COMPUTE_IB_DEV_TARE,$STORAGE_IB_DEV_COUNT,$STORAGE_IB_DEV_RATE,$MANAGER_IB_DEV_COUNT,$MANAGER_IB_DEV_RATE,NVLINK"
    fi
    node_ip=`kubectl get node $k8s_node  -o wide | awk 'NR>1 {print $6}'`
    kubectl annotate node --overwrite $k8s_node "projectcalico.org/IPv4Address1=$node_ip"
    kubectl annotate node --overwrite $k8s_node $annotate_ib="$ib_annotate_value"
    kubectl annotate node --overwrite $k8s_node $annotate_name="$annotate_value"

    text=`ssh -q -p 22 $k8s_node  'ibstat'`
    if echo "$text" | grep -q mlx
    then
            devs=$(echo "$text" | grep mlx | grep -oP "CA '\K[^']+" | tr '\n' ',' | sed 's/,$//')
            guids=$(echo "$text"  | grep -oP 'Node GUID: \K\S+' | tr '\n' ',' | sed 's/,$//')
            rates=$(echo "$text" | grep -oP 'Rate: \K\S+' | tr '\n' ',' | sed 's/,$//')
    kubectl annotate node --overwrite $k8s_node $annotate_ib_info="$devs:$guids:$rates"
   fi
  done
    ;;
  "NPU")
    echo "device is npu"
    for k8s_node in "${k8s_nodes[@]}"; do
      echo -e "\nprocess $k8s_node"
      num=`ssh -q -p 22 $k8s_node 'npu-smi info -l | grep "NPU ID" | wc -l'`
      memory=`ssh -q -p 22 $k8s_node npu-smi info -t memory -i 0 | grep 'HBM Capacity(MB)' | awk -F' ' '{print $4}' `
      echo $memory
      for ((i=0; i<$num; i++));do
        echo $i
        uuid=`ssh -q -p 22 $k8s_node npu-smi info -t board -i $i -c 0 | grep VDie | awk -F':' '{print $2}' `
        new_uuid=$(echo "$uuid" | tr ' ' '-')
        new_uuid=${new_uuid#-}
        info="$new_uuid,$memory,$NPU_INFO"
        annotate_value="$annotate_value:$info"
      done
      value=$(echo "$annotate_value" | sed 's/^://')
      echo $value
      kubectl annotate node --overwrite $k8s_node $annotate_name="$value"
    done
    ;;
  *)
    echo "device type need to set"
    ;;
esac

