import argparse
import sys

sys.path.append("/code")
import app
from app.core.constant import AIPODS_TYPE, COMPUTE_GROUP
from app.core.kube.api import read_nodes
from app.core.utils import generate_random_string
from app.models.resource_pool import ResourcePool
import asyncio
from contextlib import asynccontextmanager
from kubernetes_asyncio import client, config
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.orm import sessionmaker
from app.models.gpu import NodeStaticInfo
from sqlalchemy import <PERSON>alarR<PERSON>ult
from sqlmodel import select
from app.core.qingcloud.interface import product_attr_query_request, product_aipod_type_add_attribute, qai_spec_info, \
    qai_add_sku, product_aipod_type_update_sku_and_price

async_engine = create_async_engine(
    app.settings.DB_ASYNC_CONNECTION_STR,
    echo=False,
    future=True,
    pool_size=10,
    max_overflow=20,
    pool_recycle=3600,
    pool_pre_ping=True,
)

product_spec_attribute = {}

# 规格类型只配置整卡
QAI_AIPODS_TYPE = "only_gpu"
# 专属计算节点
QAI_RESOURCE_GROUP = "resource_group"
# 容器实例 共享计算 推理服务
QAI_AIPODS_SCOPE = ["container_instance", "sharing_compute", "inference_compute"]


@asynccontextmanager
async def get_async_session() -> AsyncSession:
    async_session = sessionmaker(bind=async_engine, class_=AsyncSession, expire_on_commit=False)
    async with async_session() as session:
        yield session
        await session.commit()


def boss_add_sku_attributes(all_sku_infos):
    for sku_infos in all_sku_infos:
        for sku_info in sku_infos:
            for item in sku_info:
                if item['attr_id'] == 'aipods_usage':
                    # 资源类型
                    result = add_sku_attribute("资源类型", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加资源类型：{item['attr_value']}，成功。")
                if item['attr_id'] == 'cpu_count':
                    # CPU核数
                    result = add_sku_attribute("CPU核数", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加CPU核数：{item['attr_value']}，成功。")
                if item['attr_id'] == 'memory':
                    # 内存
                    result = add_sku_attribute("内存", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加内存：{item['attr_value']}，成功。")
                if item['attr_id'] == 'cpu_model':
                    # CPU型号
                    result = add_sku_attribute("CPU型号", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加CPU型号：{item['attr_value']}，成功。")
                if item['attr_id'] == 'gpu_model':
                    # GPU型号
                    result = add_sku_attribute("GPU型号", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加GPU型号：{item['attr_value']}，成功。")
                if item['attr_id'] == 'gpu_count':
                    # GPU数量
                    result = add_sku_attribute("GPU数量", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加GPU数量：{item['attr_value']}，成功。")
                if item['attr_id'] == 'gpu_memory':
                    # GPU显存
                    result = add_sku_attribute("GPU显存", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加GPU显存：{item['attr_value']}，成功。")
                if item['attr_id'] == 'os_disk':
                    # 系统盘
                    result = add_sku_attribute("系统盘", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加系统盘：{item['attr_value']}，成功。")
                if item['attr_id'] == 'nvlink':
                    # nvlink
                    result = add_sku_attribute("nvlink", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加nvlink：{item['attr_value']}，成功。")
                if item['attr_id'] == 'network':
                    # IB网卡
                    result = add_sku_attribute("IB网络", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加IB网卡：{item['attr_value']}，成功。")
                if item['attr_id'] == 'disk':
                    # 数据盘
                    result = add_sku_attribute("数据盘", item['attr_value'])
                    if result["ret_code"] == 0:
                        print(f"添加数据盘：{item['attr_value']}，成功。")


def add_sku_attribute(sku_attr_type, value):
    prod_id = product_spec_attribute[sku_attr_type].split('-')[0]
    attr_id = product_spec_attribute[sku_attr_type].split('-')[1]
    attr_infos = [
        {
            "name": value,
            "attr_value": value,
            "description": value,
            "operator": "=="
        }
    ]
    return product_aipod_type_add_attribute(attr_id, prod_id, attr_infos)


def boss_add_sku_infos(all_sku_infos, qai_spec_code, qai_prod_id, qai_spec_id):
    for sku_infos in all_sku_infos:
        for sku_info in sku_infos:
            result = qai_add_sku(qai_spec_code, sku_info, qai_prod_id, qai_spec_id)
            if result["ret_code"] == 0:
                print(f"boss添加规格信息完成")


def generate_all_type_sku_infos(
        gpu_num,
        allowcate_cpu,
        allowcate_memory,
        cpu_model,
        gpu_model,
        gpu_memory,
        os_disk,
        disk,
        nvlink,
        network
):
    all_sku_infos = []
    for aipods_scope in QAI_AIPODS_SCOPE:
        item = compute_sku_infos(gpu_num, allowcate_cpu, allowcate_memory, QAI_AIPODS_TYPE, aipods_scope,
                                 gpu_model, cpu_model, gpu_model, gpu_memory, os_disk, disk, nvlink, network)
        all_sku_infos.append(item)
    boss_add_sku_attributes(all_sku_infos)
    # 获取QAI规格项信息
    qai_spec = qai_spec_info()
    qai_prod_id = qai_spec["specs"][0]["prod_id"]
    qai_spec_id = qai_spec["specs"][0]["spec_id"]
    qai_spec_code = qai_spec["specs"][0]["spec_code"]
    boss_add_sku_infos(all_sku_infos, qai_spec_code, qai_prod_id, qai_spec_id)


def compute_sku_infos(
        gpu_num,
        allowcate_cpu,
        allowcate_memory,
        aipods_type,
        aipods_scope,
        aipods_usage,
        cpu_model,
        gpu_model,
        gpu_memory,
        os_disk,
        disk,
        nvlink,
        network
):
    """
    规格详细信息
    :param gpu_num: GPU卡数量
    :param allowcate_cpu: 可分配cpu核数
    :param allowcate_memory: 可分配内存
    :param aipods_type: 规格类型
    :param aipods_scope: 产品范围
    :param aipods_usage: 资源类型
    :param cpu_count: CPU核数
    :param memory: 内存
    :param cpu_model: CPU型号
    :param gpu_model: GPU型号
    :param gpu_count: GPU数量
    :param gpu_memory: GPU显存
    :param os_disk: 系统盘
    :param disk: 数据盘
    :param nvlink: 是否存在nvlink
    :param network: ib网卡数量
    :return:
    """
    sku_pool = []
    # 根据GPU数量进行规格划分
    if gpu_num == 8:
        # 添加四种规格
        for i in range(4):
            new_gpu_count = 2 ** i
            div = 2 ** (3 - i)
            new_cpu = str(allowcate_cpu // div)
            new_memory = str(allowcate_memory // div)
            # 8卡资源需要将配置IB网卡规格
            if i == 3:
                sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model, str(new_gpu_count),
                                         str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
                sku_pool.append(sku_item)
                break
            sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model, str(new_gpu_count),
                                     str(gpu_memory), str(os_disk), str(disk), str(nvlink), str("0"))
            sku_pool.append(sku_item)
        # 添加资源组规格
        sku_item = set_sku_infos(aipods_type, QAI_RESOURCE_GROUP, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, "8",
                                 str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item)
    elif gpu_num == 4:
        # 添加三种规格
        for i in range(3):
            new_gpu_count = 2 ** i
            div = 2 ** (2 - i)
            new_cpu = str(allowcate_cpu // div)
            new_memory = str(allowcate_memory // div)
            if i == 2:
                sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model, str(new_gpu_count),
                                         str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
                sku_pool.append(sku_item)
                break
            sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model, str(new_gpu_count),
                                     str(gpu_memory), str(os_disk), str(disk), str(nvlink), str("0"))
            sku_pool.append(sku_item)
        # 添加资源组规格
        sku_item = set_sku_infos(aipods_type, QAI_RESOURCE_GROUP, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, "8",
                                 str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item)
    elif gpu_num == 2:
        # 添加两种规格
        for i in range(2):
            new_gpu_count = 2 ** i
            div = 2 ** (1 - i)
            new_cpu = str(allowcate_cpu // div)
            new_memory = str(allowcate_memory // div)
            if i == 1:
                sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model, str(new_gpu_count),
                                         str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
                sku_pool.append(sku_item)
                break
            sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model, str(new_gpu_count),
                                     str(gpu_memory), str(os_disk), str(disk), str(nvlink), str("0"))
            sku_pool.append(sku_item)
        # 添加资源组规格
        sku_item = set_sku_infos(aipods_type, QAI_RESOURCE_GROUP, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, "8",
                                 str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item)
    elif gpu_num == 1:
        # 添加一种规格
        sku_item = set_sku_infos(aipods_type, aipods_scope, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, "1",
                                 str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item)
        # 添加资源组规格
        sku_item = set_sku_infos(aipods_type, QAI_RESOURCE_GROUP, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, "1",
                                 str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item)
    else:
        # 只添加单卡和满卡的规格
        new_gpu_count = int(gpu_num)
        new_cpu = str(allowcate_cpu // new_gpu_count)
        new_memory = str(allowcate_memory // new_gpu_count)
        sku_item_one = set_sku_infos(aipods_type, aipods_scope, aipods_usage, new_cpu, new_memory, cpu_model, gpu_model,"1",
                                     str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item_one)
        sku_item_full = set_sku_infos(aipods_type, aipods_scope, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, str(new_gpu_count),
                                      str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item_full)
        # 添加资源组规格
        sku_item = set_sku_infos(aipods_type, QAI_RESOURCE_GROUP, aipods_usage, str(allowcate_cpu), str(allowcate_memory), cpu_model, gpu_model, str(new_gpu_count),
                                 str(gpu_memory), str(os_disk), str(disk), str(nvlink), str(network))
        sku_pool.append(sku_item)
    return sku_pool


def set_sku_infos(
        aipods_type,
        aipods_scope,
        aipods_usage,
        cpu_count,
        memory,
        cpu_model,
        gpu_model,
        gpu_count,
        gpu_memory,
        os_disk,
        disk,
        nvlink,
        network
):
    """
    设置sku数据
    :param aipods_type: 规格类型
    :param aipods_scope: 产品范围
    :param aipods_usage: 资源类型
    :param cpu_count: CPU核数
    :param memory: 内存
    :param cpu_model: CPU型号
    :param gpu_model: GPU型号
    :param gpu_count: GPU数量
    :param gpu_memory: GPU显存
    :param os_disk: 系统盘
    :param disk: 数据盘
    :param nvlink: 是否存在nvlink
    :param network: ib网卡数量
    :return:
    """
    result = [
        {
            "attr_id": "aipods_type",
            "operator": "==",
            "attr_value": aipods_type
        },
        {
            "attr_id": "aipods_scope",
            "operator": "==",
            "attr_value": aipods_scope
        },
        {
            "attr_id": "aipods_usage",
            "operator": "==",
            "attr_value": aipods_usage
        },
        {
            "attr_id": "cpu_count",
            "operator": "==",
            "attr_value": cpu_count
        },
        {
            "attr_id": "memory",
            "operator": "==",
            "attr_value": memory
        },
        {
            "attr_id": "cpu_model",
            "operator": "==",
            "attr_value": cpu_model
        },
        {
            "attr_id": "gpu_model",
            "operator": "==",
            "attr_value": gpu_model
        },
        {
            "attr_id": "gpu_count",
            "operator": "==",
            "attr_value": gpu_count
        },
        {
            "attr_id": "gpu_memory",
            "operator": "==",
            "attr_value": gpu_memory
        },
        {
            "attr_id": "os_disk",
            "operator": "==",
            "attr_value": os_disk
        },
        {
            "attr_id": "nvlink",
            "operator": "==",
            "attr_value": nvlink
        }
    ]
    if disk != "0":
        result.append({
            "attr_id": "disk",
            "operator": "==",
            "attr_value": disk
        })
    if network != "0":
        result.append({
            "attr_id": "network",
            "operator": "==",
            "attr_value": network
        })
    return result


async def kubernetes_get_node_info(hostname):
    config.load_incluster_config()
    v1 = client.CoreV1Api()
    try:
        node = await v1.read_node(hostname)
        return node
    except client.exceptions.ApiException as e:
        print(f"Error fetching node info: {e}")


async def kubernetes_replace_node_tag(hostname, node):
    config.load_incluster_config()
    v1 = client.CoreV1Api()
    try:
        await v1.replace_node(hostname, node)
    except Exception as e:
        print(f"Error replacing node info: {e}")


def get_gpu_lable(node_info):
    label_keys = [
        # nvidia
        "nvidia.com/gpu.product",
        # 海飞科
        "hexaflake.com/aigpu.product",
        # 海光
        "feature.node.kubernetes.io/gpu-model",
        # 升腾
        "servertype"
    ]
    for label_key in label_keys:
        value = node_info.metadata.labels.get(label_key, "")
        if value:
            return value
    return ""


async def init_gpu_sku(reserve_cpu=0, reserve_memory=0, os_disk=40, data_disk=0):
    """
    初始化产品规格
    :param reserve_cpu: 预留cpu核数
    :param reserve_memory: 预留内存核数
    :param os_disk: 系统盘大小
    :param data_disk: 数据盘大小
    :return:
    """
    # 获取QAI规格项信息
    qai_spec = qai_spec_info()
    qai_prod_id = qai_spec["specs"][0]["prod_id"]
    # 获取boss规格属性
    result = product_attr_query_request()
    for attribute in result["attributes"]:
        # 高速网卡和IB网络保持统一
        if attribute['name'] == "高速网卡":
            product_spec_attribute["IB网络"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"
        else:
            product_spec_attribute[f"{attribute['name']}"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"

    # Get the information of nodes.
    async with get_async_session() as session:
        stmt = select(NodeStaticInfo).filter(
            NodeStaticInfo.gpu_model != "-"
        ).order_by(
            NodeStaticInfo.gpu_model, NodeStaticInfo.gpu.desc()
        ).distinct(NodeStaticInfo.gpu_model)
        sr: ScalarResult[NodeStaticInfo] = await session.scalars(stmt)
        nodeHardwareInfos = sr.fetchall()
        for node in nodeHardwareInfos:
            allowcate_cpu = node.cpu - reserve_cpu
            allowcate_memory = node.memory - reserve_memory
            # 获取gpu_discovery识别到的gpu.product
            node_info = await kubernetes_get_node_info(node.hostname)
            gpu_discovery_name = get_gpu_lable(node_info)
            if gpu_discovery_name == "":
                print("gpu operator未生效，请先配置好服务再执行脚本")
                return
            # 获取sku_items
            generate_all_type_sku_infos(node.gpu, allowcate_cpu, allowcate_memory, node.cpu_model, gpu_discovery_name,
                                        node.gpu_memory, os_disk, data_disk, node.nvlink, node.ib_count_compute)
    # 规格生效
    product_aipod_type_update_sku_and_price(qai_prod_id)


async def create_default_pool():
    """
    创建默认资源池
    :return:
    """
    async with get_async_session() as session:
        aipods_type = "only_gpu"
        pool_name = "default"
        # 获取所有worker节点
        stmt = select(NodeStaticInfo).where(
            NodeStaticInfo.role.like('%worker%'),
            NodeStaticInfo.gpu_model != "-"
        )
        sr: ScalarResult[NodeStaticInfo] = await session.scalars(stmt)
        nodeInfos = sr.fetchall()
        hostnames = []
        # 当且仅当所有节点都是control-plane
        all_worker = True
        for node_info in nodeInfos:
            if "control-plane" not in node_info.role:
                # 存在计算节点、管理节点的情况
                all_worker = False
        if all_worker:
            hostnames = [node_info.hostname for node_info in nodeInfos]
        else:
            for node_info in nodeInfos:
                if "control-plane" not in node_info.role:
                    hostnames.append(node_info.hostname)
        # 创建默认资源池并且node打标签
        pool_id = "pool-" + generate_random_string()
        node_count = 0
        gpu_count = 0
        for hostname in hostnames:
            node = read_nodes(hostname)
            node_labels = node.metadata.labels
            node_labels[AIPODS_TYPE] = aipods_type
            node_labels[COMPUTE_GROUP] = pool_id
            # node打标签
            await kubernetes_replace_node_tag(hostname, node)
            # 修改node_static_info表数据
            stmt = select(NodeStaticInfo).where(
                NodeStaticInfo.hostname == hostname
            )
            sr: ScalarResult[NodeStaticInfo] = await session.scalars(stmt)
            node_static_info = sr.first()
            node_static_info.compute_group = pool_id
            node_count += 1
            gpu_count += node_static_info.gpu
            session.add(node_static_info)
        # db添加资源池信息
        resource_pool = ResourcePool(
            pool_id=pool_id,
            pool_name=pool_name,
            aipods_type=aipods_type,
            node_count=node_count,
            gpu_count=gpu_count
        )
        session.add(resource_pool)
        await session.commit()


async def main(reserve_cpu, reserve_memory, os_disk, disk):
    # 获取boss规格属性
    result = product_attr_query_request()
    product_spec_attribute = {}
    for attribute in result["attributes"]:
        product_spec_attribute[f"{attribute['name']}"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"
    print(product_spec_attribute)
    # 初始化sku
    await init_gpu_sku(reserve_cpu, reserve_memory, os_disk, disk)
    # 创建资源池
    await create_default_pool()


if __name__ == '__main__':
    parser = argparse.ArgumentParser(description="Command line arguments for GPU initialization.")
    parser.add_argument('reserve_cpu', type=int, help="Number of reserved CPUs for the machine.")
    parser.add_argument('reserve_memory', type=int, help="Number of reserved memory for the machine")
    parser.add_argument('os_disk', type=int, help="System disk size.")
    parser.add_argument('disk', type=int, help="Data disk size")
    asyncio.run(main(
        parser.parse_args().reserve_cpu,
        parser.parse_args().reserve_memory,
        parser.parse_args().os_disk,
        parser.parse_args().disk
    ))
