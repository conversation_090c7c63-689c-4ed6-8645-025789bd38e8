#!/bin/bash
cd /code || exit 1

version=$(date +%Y%m%d%H%M%S)

alembic revision --autogenerate -m "$version" || exit 1
# shellcheck disable=SC2012
# shellcheck disable=SC2010
last_migration=$(ls -t /code/migration/versions/ | grep "$version" | head -n1)
if [ -z "$last_migration" ]; then
    echo "Migration file not generated!"
    exit 1
fi

last_migration_file="/code/migration/versions/$last_migration"

python3 /code/app/jobs/sqls/alembic_parser.py "$last_migration_file"

last_migration_check=$?
if [ "$last_migration_check" -eq 1 ]; then
    echo "Running alembic upgrade head"
    alembic upgrade head
elif [ "$last_migration_check" -ne 0 ]; then
    exit 1
else
  rm -f "$last_migration_file"
    echo "No need to run alembic upgrade head"
fi

python3 /code/app/jobs/start_up.py
#python3 /code/app/jobs/sqls/execute.py upgrade
