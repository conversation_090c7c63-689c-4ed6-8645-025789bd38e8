#!/bin/bash

input=`ssh -q -p 22 $1 hxsmi query  | grep -E 'SN|Total|Product Name' | grep -v Space | awk -F ':' '{gsub(/^[ \t]+|[ \t]+$/, "", $1); gsub(/^[ \t]+|[ \t]+$/, "", $2); print $1 ":" $2}'`

IFS=$'\n'
n=1
data1=''
data2=''
data3=''
annotate_name='qingcloud/node-nvidia-register'
annotate_value=''
for line in $input; do
    value=$(echo "$line" | cut -d ':' -f 2 | xargs) # 使用cut提取冒号后的部分，并去掉空格
    if [ "$n" -eq 1 ]; then
      data1=$value
      n=$((n + 1))
    elif  [ "$n" -eq 2 ]; then
      data2=$value
      n=$((n + 1))
    else
      data3=$value
      n=1
      #echo "$data2,$data3,$data1"
      if [ -z "${annotate_value:-}" ]; then
       annotate_value="$data2,$data3,$data1"
      else
        annotate_value="$annotate_value:$data2,$data3,$data1"
      fi
    fi
done
kubectl annotate node --overwrite $1 $annotate_name="$annotate_value"

# 使用方式  ./patch_hfk_gpu_info.sh  hostname