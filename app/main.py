import asyncio
import datetime
import importlib
import os.path
import threading
import glob
from fastapi import Depends, FastAPI
from starlette.middleware.cors import CORSMiddleware
import app
from app import watch_config_file
from app.core import bmc
from app.core.bmc import B<PERSON><PERSON>rror, check_error
from app.core.rlock import <PERSON><PERSON>
from app.depends.authz import is_admin
from app.core.exceptions import AICPBaseException
from app.core.init_priority import init_priorityclasses
from app.apps.user.init_user import initialization_user
# noinspection PyUnresolvedReferences
from app.core.loggers import log_middleware, logger
from app.core.middlewares.authn import ProcessAuthnMiddleware
from app.core.middlewares.operation_record import OperationRecordMiddleware
from app.core.middlewares.exception import process_base_exception_middleware, process_exception_middleware
from app.core.middlewares.logger import LoggerRequestsIdMiddleware

application = FastAPI(
    title=app.settings.project_name,
    description=app.settings.description,
    version=app.settings.version,
    docs_url=f'{app.settings.api_v1_prefix}-docs',
    openapi_url=f'{app.settings.api_v1_prefix}-openapi.json',
    redoc_url=None,
)

application.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

application.add_middleware(ProcessAuthnMiddleware)
application.add_middleware(LoggerRequestsIdMiddleware)
application.add_exception_handler(AICPBaseException, process_exception_middleware)
application.add_exception_handler(Exception, process_base_exception_middleware)
application.add_middleware(OperationRecordMiddleware)

# auto load app.router
APP_PATH = os.path.join(os.path.dirname(__file__), "apps")
for app_name in os.listdir(APP_PATH):
    if app_name.startswith("__"):
        continue

    if os.path.isdir(os.path.join(APP_PATH, app_name)):
        try:
            if (not os.path.exists(os.path.join(APP_PATH, app_name, "api.py")) and 
                    not glob.glob(os.path.join(APP_PATH, app_name, "api*.so"))):
                logger.warning(f"Not found app.apps.{app_name}.api. Skip it.")
                continue

            api = importlib.import_module(f"app.apps.{app_name}.api")  # type: ignore
            if not hasattr(api, "router"):
                logger.warning(f"app.apps.{app_name}.router is not a APIRouter")
                continue

            provider = getattr(importlib.import_module(f"app.apps.{app_name}"), "PROVIDER", ["console", "admin"])

            logger.info(f"import app.apps.{app_name}.router success")
            if "console" in provider:
                logger.info(f"import app.apps.{app_name}.router with console prefix success")
                application.include_router(api.router, prefix=f"{app.settings.api_v1_prefix}")
                application.include_router(api.router, prefix=f"{app.settings.api_v1_prefix_qai}")

            # 提供给管理端的接口需要一 kapi 前缀
            if "admin" in provider:
                logger.info(f"import app.apps.{app_name}.router with admin prefix success")
                application.include_router(api.router, prefix=f"{app.settings.api_kapi_prefix}",
                                           dependencies=[Depends(is_admin)])
        except Exception as e:
            raise e


@application.on_event("startup")
async def start_up():
    """
    fast api 启动时会执行这里的代码
    """
    if app.settings.debug:
        logger.info("debug mode, skip config synchronize")
        return
    # notify
    watch_thread = threading.Thread(target=watch_config_file, args=())
    watch_thread.daemon = True
    watch_thread.start()
