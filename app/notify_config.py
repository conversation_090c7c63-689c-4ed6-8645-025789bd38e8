from time import sleep

from kubernetes import client, config, watch
from kubernetes.config import ConfigException

from app import update_config


def watch_config_file():
    try:
        config.load_incluster_config()
    except ConfigException:
        config.load_kube_config()
    v1 = client.CoreV1Api()
    w = watch.Watch()
    namespace = "aicp-system"
    configmap_name = "aicp-web-app-parameters"
    target_file = "api_server.yaml"
    # start to watch
    while True:
        try:
            for event in w.stream(v1.list_namespaced_config_map, namespace=namespace):
                if event['type'] in ['ADDED', 'MODIFIED', 'DELETED']:
                    configmap = event['object']
                    if configmap.metadata.name == configmap_name:
                        # Get api-server.yaml
                        update_config(configmap.data[target_file])
        except Exception as e:
            print(f"Error: {e}")
        sleep(5)