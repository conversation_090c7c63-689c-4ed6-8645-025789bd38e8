class AICPBaseException(Exception):
    """
    Base exception
    """
    BASE_CODE = 500
    BASE_MESSAGE = "Internal server error"

    def __init__(self, message=None, use_base_message=True):
        self.code = self.BASE_CODE
        if use_base_message:
            self.message = f"{self.BASE_MESSAGE}" + (f": {message}" if message else "")
        else:
            self.message = message or self.BASE_MESSAGE

    @property
    def dict(self):
        """
        Return dict
        :return:
        """
        return {
            "ret_code": self.code,
            "message": self.message,
        }

    def __str__(self):
        return f"{self.code}: {self.message}"

    def __repr__(self):
        return f"{self.code}: {self.message}"


class InternalServerErrorException(AICPBaseException):
    BASE_CODE = 500
    BASE_MESSAGE = "服务器内部错误"


internal_server_error = InternalServerErrorException()


class ParameterException(AICPBaseException):
    BASE_CODE = 400
    BASE_MESSAGE = "参数错误"


class PermissionDeniedException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "无权限访问"


class NotSupportingAccessToAllResources(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "此功能不支持访问全部资源"


class PermissionDeniedForNamespaceException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "无权限访问namespace"


class ResourceNotFoundException(AICPBaseException):
    BASE_CODE = 404
    BASE_MESSAGE = "资源不存在"


class QingcloudApiException(AICPBaseException):
    BASE_CODE = 500
    BASE_MESSAGE = "Qingcloud API请求失败"


class NotSupportVolumeTypeException(AICPBaseException):
    BASE_CODE = 400
    BASE_MESSAGE = "不支持的存储类型"


class NotSupportResoutceTypeException(AICPBaseException):
    BASE_CODE = 400
    BASE_MESSAGE = "不支持的资源类型"


class VolumeCreateException(AICPBaseException):
    BASE_CODE = 500
    BASE_MESSAGE = "创建存储失败"


class TaskNotFoundException(AICPBaseException):
    BASE_CODE = 404
    BASE_MESSAGE = "任务不存在"


class InvalidTargetPortException(AICPBaseException):
    BASE_CODE = 3008
    BASE_MESSAGE = "端口不合法"


class DuplicatePortException(AICPBaseException):
    BASE_CODE = 3009
    BASE_MESSAGE = "端口重复"


class TooManyPortsException(AICPBaseException):
    BASE_CODE = 3010
    BASE_MESSAGE = "端口数量超过限制"
