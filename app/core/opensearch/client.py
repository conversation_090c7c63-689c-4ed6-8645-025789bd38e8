import json
import os.path
from datetime import datetime
from typing import Dict, List, Tuple

from opensearchpy import NotFoundError, OpenSearch, AsyncOpenSearch

import app
from app import logger


def remove_dot_key(d: dict) -> dict:
    """
    recursion remove key has dot
    :param d:
    :return:
    """
    if isinstance(d, list):
        for i in d:
            remove_dot_key(i)
    if isinstance(d, dict):
        for dot_key in [k for k in d.keys() if "." in k or k.startswith("f:")]:
            del d[dot_key]

        for k, v in d.items():
            remove_dot_key(v)
    return d


auth = (app.settings.OPENSEARCH_USER, app.settings.OPENSEARCH_PASSWORD)


class OpenSearchClient:

    def __init__(self):
        self.kube_resource_index_name = "kube_resource"
        self.log_index_name = f"{app.settings.cluster_name}-logs*"
        self._client = OpenSearch(
            hosts=[app.settings.OPENSEARCH_HOST],
            http_auth=auth,
            verify_certs=False,
        )

    def get_indeices(self):
        return self._client.indices.get_alias("*")

    def search_kube_resource(self, query):
        return self._client.search(index=self.kube_resource_index_name, body=query)


    def insert_or_update(self, _id, body, remove_dot=True):
        """
        :param remove_dot: remove key has dot or start with f:
        :param index:
        :param _id:
        :param body:
        :return:
        """
        if remove_dot:
            body = remove_dot_key(body)
        return self._client.index(index=self.kube_resource_index_name, id=_id, body=body, timeout=60)

    def get_by_uid(self, uid: str):
        try:
            res = self._client.get(index=self.kube_resource_index_name, id=uid)
            return res["_source"]
        except NotFoundError:
            logger.error(f"opensearch uid: {uid} not found")

    def get_by_uids(self, uids: List[str]):
        query = {
            "query": {
                "ids": {
                    "values": uids
                }
            },
            "size": len(uids),
            "from": 0
        }
        res = self._client.search(index=self.kube_resource_index_name, body=query)
        return [x["_source"] for x in res["hits"]["hits"]]

    def get_pods_by_lables_app_query(self, apps: List[str]):
        return {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "kind": {
                                    "value": "Pod"
                                }
                            }
                        },
                        {
                            "terms": {
                                "metadata.labels.app": apps
                            }
                        }
                    ]
                }
            },
            "size": len(apps) * 10,
            "from": 0
        }

    def get_pods_by_lables_app(self, apps: List[str]):
        query = self.get_pods_by_lables_app_query(apps)
        res = self._client.search(index=self.kube_resource_index_name, body=query)
        return [x["_source"] for x in res["hits"]["hits"]]

    def get_pods_name_by_lables_app(self, apps: List[str]):
        query = self.get_pods_by_lables_app_query(apps)
        query["_source"] = ["metadata.name"]
        res = self._client.search(index=self.kube_resource_index_name, body=query)
        return list(set([x["_source"]["metadata"]["name"] for x in res["hits"]["hits"]]))


    def create_kube_resource_index(self, force=False):
        # if index not exist:
        if self._client.indices.exists(index=self.kube_resource_index_name):
            if not force:
                return
            self._client.indices.delete(index=self.kube_resource_index_name)
        with open(os.path.join(os.path.dirname(__file__), "kube_resource_setting.json"), "r") as f:
            body = json.load(f)
        if not body:
            logger.warning("kube_resource_setting.json is empty")
            return
        self._client.indices.create(index=self.kube_resource_index_name, body=body)

    def get_train_pods_name(self, train_uuid: str):
        train_pods = self.get_pods_by_lables_app([train_uuid])
        return list(set([x["metadata"]["name"] for x in train_pods]))


es_client = OpenSearchClient()


class AsyncOpenSearchClient:

    def __init__(self):
        self.kube_resource_index_name = "kube_resource"
        self.log_index_name = f"{app.settings.cluster_name}-logs*"
        self._client = AsyncOpenSearch(
            [app.settings.OPENSEARCH_HOST],
            http_auth=auth,
            verify_ssl=False
        )

    async def get_indices(self):
        return await self._client.indices.get_alias("*")

    async def insert_or_update(self, _id, body, remove_dot=True):
        if remove_dot:
            body = remove_dot_key(body)
        return await self._client.index(index=self.kube_resource_index_name, id=_id, body=body, timeout=60)

    async def get_by_uid(self, uid: str):
        try:
            res = await self._client.get(index=self.kube_resource_index_name, id=uid)
            return res["_source"]
        except NotFoundError:
            logger.error(f"opensearch uid: {uid} not found")

    async def get_by_uids(self, uids: List[str]):
        query = {
            "query": {
                "ids": {
                    "values": uids
                }
            },
            "size": len(uids),
            "from": 0
        }
        res = await self._client.search(index=self.kube_resource_index_name, body=query)
        return [x["_source"] for x in res["hits"]["hits"]]

    async def get_pods_by_labels_app(self, apps: List[str]):
        query = {
            "query": {
                "bool": {
                    "must": [
                        {
                            "term": {
                                "kind": {
                                    "value": "Pod"
                                }
                            }
                        },
                        {
                            "terms": {
                                "metadata.labels.app": apps
                            }
                        }
                    ]
                }
            },
            "size": 10000,
            "from": 0
        }
        res = await self._client.search(index=self.kube_resource_index_name, body=query)
        return [x["_source"] for x in res["hits"]["hits"]]


async_es_client = AsyncOpenSearchClient()
