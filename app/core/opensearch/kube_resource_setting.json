{"aliases": {}, "mappings": {"properties": {"apiVersion": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "kind": {"type": "keyword"}, "metadata": {"properties": {"creationTimestamp": {"type": "date"}, "labels": {"properties": {"app": {"type": "keyword"}, "train_uuid": {"type": "keyword"}, "user": {"type": "keyword"}, "user_name": {"type": "keyword"}}}, "name": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}, "namespace": {"type": "keyword"}, "uid": {"type": "keyword"}}}, "status": {"properties": {"conditions": {"properties": {"lastTransitionTime": {"type": "date"}, "lastUpdateTime": {"type": "date"}, "status": {"type": "keyword"}, "type": {"type": "text", "fields": {"keyword": {"type": "keyword", "ignore_above": 256}}}}}, "startTime": {"type": "date"}}}}, "dynamic": false}, "settings": {"index": {"mapping": {"total_fields": {"limit": 10000}}, "routing": {"allocation": {"include": {"_tier_preference": "data_content"}}}, "number_of_shards": "1", "number_of_replicas": "1"}}}