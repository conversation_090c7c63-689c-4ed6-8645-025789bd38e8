import json
from typing import List
from app import logger

import httpx
import urllib3
import requests
from requests.auth import HTTPBasic<PERSON>uth
from httpx import BasicAuth

import app

# UFM add empty pkey
def add_pkey_request(pkey: str):
    data = {
        "pkey": pkey,
        "index0": True,
        "ip_over_ib": True,
        "mtu_limit": 2,
        "service_level": 0,
        "rate_limit": 300,
    }
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    json_data = json.dumps(data)
    res = session.post(app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/add", headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD), data=json_data)
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("ufm server create pkey failed")

async def async_add_pkey_request(pkey: str):
    data = {
        "pkey": pkey,
        "index0": True,
        "ip_over_ib": True,
        "mtu_limit": 2,
        "service_level": 0,
        "rate_limit": 300,
    }
    url = app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/add"
    async with httpx.AsyncClient(verify=False) as client:
        res = await client.post(url=url, headers={'Content-Type': 'Application/json'}, auth=BasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD), json=data)
        logger.info(f"add pkey:{res}")
        if res.status_code == 400 and "is already exist!" in str(res.content):
            logger.info(f"{pkey} is already exist!")
        # not 2xx
        if int(res.status_code / 100) != 2:
            logger.info("ufm server create pkey failed")


# ufm server delete pkey
def delete_ufm_pkey(pkey: str):
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    res = session.delete(app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/" + hex(pkey), headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD))
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("ufm unbind pkey failed")

# UFM GUIDs bind PKey
def bind_ufm_pkey_guids(guids: List[str], pkey: str):
    # Remove the prefix '0x'
    new_guids = [item[2:] for item in guids]
    data = {
        "guids": new_guids,
        "pkey": pkey,
        "index0": True,
        "ip_over_ib": True,
        "membership": "full",
        "mtu_limit": 2,
        "service_level": 0,
        "rate_limit": 300,
    }
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    json_data = json.dumps(data)
    res = session.put(app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/", headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD), data=json_data)
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("ufm bind pkey failed")

async def async_bind_ufm_pkey_guids(guids: List[str], pkey: str):
    # Remove the prefix '0x'
    new_guids = [item[2:] for item in guids]
    data = {
        "guids": new_guids,
        "pkey": pkey,
        "index0": True,
        "ip_over_ib": True,
        "membership": "full",
        "mtu_limit": 2,
        "service_level": 0,
        "rate_limit": 300,
    }
    url = app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/"
    async with httpx.AsyncClient(verify=False) as client:
        res = await client.put(url=url, headers={'Content-Type': 'Application/json'}, auth=BasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD), json=data)
        logger.info(f"bind pkey:{res}")
        if res.status_code == 400 and "is already exist!" in str(res.content):
            logger.info(f"{pkey} is already exist!")
        # not 2xx
        if int(res.status_code / 100) != 2:
            logger.info("ufm bind pkey failed")


# ufm server unbind pkey guid
def unbind_ufm_pkey_guid(guids: List[str], pkey: str):
    # Remove the prefix '0x'
    new_guids = [item[2:] for item in guids]
    guid_str = ','.join(new_guids)
    print(pkey)
    print(guid_str)
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    url = app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/" + pkey + "/guids/" + guid_str
    print(url)
    res = session.delete(url, headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD))
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("umf unbind pkey failed")

async def async_unbind_ufm_pkey_guid(guids: List[str], pkey: str):
    # Remove the prefix '0x'
    new_guids = [item[2:] for item in guids]
    guid_str = ','.join(new_guids)
    url = app.settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/" + pkey + "/guids/" + guid_str
    async with httpx.AsyncClient(verify=False) as client:
        res = await client.delete(url=url, headers={'Content-Type': 'Application/json'}, auth=BasicAuth(app.settings.UFM_USERNAME, app.settings.UFM_PASSWORD))
        logger.info(f"unbind pkey:{res}")
        if res.status_code == 400 and "is already exist!" in str(res.content):
            logger.info(f"{pkey} is already exist!")
        # not 2xx
        if int(res.status_code / 100) != 2:
            logger.info("umf unbind pkey failed")
