from typing import List

from sqlalchemy import update

from app.models.gpu import IbDevInfo
from app.core.ufm.models import UFMPkeys
from sqlmodel import Session
from app import logger
from app.core.ufm.ufm_api import add_pkey_request, bind_ufm_pkey_guids, unbind_ufm_pkey_guid
from app.core.utils import generate_random_string
from multiprocessing import Lock

lock = Lock()

class UFMInfoCrud:
    def __init__(self, session: Session):
        self.session: Session = session

    def get_ufm_pkeys(self, uuid: str) -> UFMPkeys:
        return self.session.query(UFMPkeys).get(uuid)

    def get_ufm_info_by_user(self, user_id: str) -> UFMPkeys:
        return self.session.query(UFMPkeys).filter_by(user_id=user_id).first()

    # user bind pkey
    def add_ufm_pkey(self, user_id: str):
        # get user pkey info
        user = self.session.query(UFMPkeys).filter_by(user_id=user_id).all()
        if user:
            logger.error(f"User already exists pkey")
            return
        with lock:
            pkeys_list = self.session.query(UFMPkeys.pkey).all()
            decimal_value = [int(hex_str[0], 16) for hex_str in pkeys_list]
            decimal_values_sorted = sorted(decimal_value)
            pkeys_first = UFMPkeys()
            pkeys_first.user_id = user_id
            pkeys_first.pkey_id = "ufm-" + generate_random_string()
            pkeys_first.status = "success"
            pkey_temp = 0
            # list is not None
            if decimal_values_sorted:
                # pkey more than 32766
                if len(decimal_values_sorted) >= 32766:
                    logger.error(f"Insufficient number of pkey")
                    raise Exception(f"Insufficient number of pkey")
                # find pkey
                for i in range(len(decimal_values_sorted)):
                    if i + 1 != decimal_values_sorted[i]:
                        pkey_temp = i + 1
                        break
                    if i == len(decimal_values_sorted) - 1:
                        pkey_temp = len(decimal_values_sorted) + 1
            # list is None, pkey is 1
            else:
                pkey_temp = 1
            pkeys_first.pkey = hex(pkey_temp)
            # ufm server
            add_pkey_request(pkeys_first.pkey)
            # database bind user and pkey
            self.session.add(pkeys_first)
            self.session.commit()

    # guids bind pkey
    def bind_guids_pkey(self, guids: List[str], pkey: str):
        # get exist guids
        database_guids = self.session.query(IbDevInfo.guid).filter_by(pkey=pkey).all()
        exist_guids = []
        for database_guid in database_guids:
            exist_guids.append(database_guid.guid)
        guids.extend(exist_guids)
        # ufm server
        bind_ufm_pkey_guids(guids, pkey)
        for guid in guids:
            # update
            stmt = update(IbDevInfo).where(IbDevInfo.guid == guid).values(pkey=pkey)
            self.session.execute(stmt)

    # guids unbind pkey
    def unbind_guids_pkey(self, guids: List[str], pkey: str):
        unbind_ufm_pkey_guid(guids, pkey)
        # get all dev info
        dev_list = self.session.query(IbDevInfo).filter(IbDevInfo.pkey == pkey).all()
        if len(dev_list) > len(guids):
            for guid in guids:
                # update
                stmt = update(IbDevInfo).where(IbDevInfo.guid == guid).values(pkey='')
                self.session.execute(stmt)
        else:
            for guid in guids:
                # update
                stmt = update(IbDevInfo).where(IbDevInfo.guid == guid).values(pkey='')
                self.session.execute(stmt)
            database_guids = self.session.query(IbDevInfo.guid).filter_by(pkey=pkey).all()
            if len(database_guids) == 0:
                add_pkey_request(pkey)


    # get guids from hostnames
    def get_guids(self, hostnames: List[str]) -> List[str]:
        guids = self.session.query(IbDevInfo.guid).filter(IbDevInfo.hostname.in_(hostnames)).all()
        res_guids = []
        for guid in guids:
            res_guids.append(guid[0])
        return res_guids
