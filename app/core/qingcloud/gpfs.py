from typing import Dict, List

import app
from app import logger
from httpx import Client, Timeout

from .exceptions import GetGpfsFilesetsException
from .utils import generate_signature
from ..models import QingcloudUser


class QingcloudGpfsClient:
    HOST = app.settings.QINGCLOUD_GPFS_SERVER
    ZONE = app.settings.QINGCLOUD_GPSE_ZONE

    def __init__(self, user: QingcloudUser):
        self.user = user
        self.client = Client(base_url=self.HOST, timeout=Timeout(20.0))

    def get_signature_params(self, method: str, url: str, params: dict) -> str:
        """

        :param method:
        :param url:
        :param params:
        """
        logger.debug(f"get_signature_params: {method}, {url}, {params}")
        return generate_signature(method, url, self.user.access_key_id, self.user.secret_access_key, params)

    def check_rsp_error(self, r):
        if r.json().get("ret_code") != 0:
            logger.error("get gpfs rsp [%s]", r.text)
            raise GetGpfsFilesetsException(r.json().get("message"))

    def get_filesets(self) -> Dict:
        """
        :return: list of filesystems
        """
        URL = "/epfs/api/filesystem"
        params = {
            "zone": self.ZONE,
            "user_id": self.user.user_id
        }
        URL += f"?{self.get_signature_params('GET', URL, params)}"
        r = self.client.get(URL)
        self.check_rsp_error(r)
        logger.debug(f"get filesets: {r.json()}")
        if filesets := r.json().get("filesets"):
            logger.debug(f"get filesets: {filesets}")
            return filesets

        logger.warning(f"not found filesets by user {self.user.user_name}({self.user.user_id})")
        return {}

    def get_be_shared_filesets(self) -> List:
        """
        :return: list of filesystems
        """
        URL = "/epfs/api/filesystem/sharing"
        params = {
            "zone": self.ZONE
        }
        URL += f"?{self.get_signature_params('GET', URL, params)}"
        r = self.client.get(URL)
        self.check_rsp_error(r)

        if r.json().get("total_count") > len(r.json().get("data")):
            params["limit"] = r.json().get("total_count")
            URL += f"?{self.get_signature_params('GET', URL, params)}"
            r = self.client.get(URL)
            self.check_rsp_error(r)

        logger.debug(f"get filesets: {r.json()}")
        if filesets := r.json().get("data"):
            logger.debug(f"get filesets: {filesets}")
            return filesets

        logger.warning(f"not found filesets by user {self.user}")
        return []

    def get_filesets_quota(self) -> tuple:
        """
        :return: list of filesystems
        """
        URL = "/epfs/api/filesystem/used"
        params = {
            "zone": self.ZONE
        }
        URL += f"?{self.get_signature_params('GET', URL, params)}"
        r = self.client.get(URL)
        # 如果返回码为1400，说明用户没有epfs权限，返回默认值
        if r.json().get("ret_code") == 1400:
            logger.info("user [%s] has no epfs permission, return default value", self.user.user_id)
            return 0, 0
        self.check_rsp_error(r)
        logger.debug(f"get filesets: {r.json()}")
        return r.json().get("dir_number"), r.json().get("storage_used")

