import datetime
import json
import threading
import time

from kr8s.objects import Event

import app
from app import logger
from app.core.constant import EventReason
from app.core.qingcloud.exceptions import BillingServiceException
from app.core.qingcloud.interface import IaasClient
from app.core.utils import AsyncifyMethods

VALID_LEASING_DURATION = {
    "1h": 3600, "1d": 86400, "1m": 2592000, "1y": 31536000,
    "1 month": 1, "2 months": 2, "3 months": 3, "4 months": 4, "5 months": 5,
    "6 months": 6, "7 months": 7, "8 months": 8, "9 months": 9, "10 months": 10,
    "11 months": 11, "12 months": 12, "24 months": 24,
    "36 months": 36, "48 months": 48, "60 months": 60, "all months": 0,
}


def create_error_billing_event(req, rep):
    """
    创建计费错误事件
    :param req:
    :param rep:
    """
    message = {"requests": req, "response": rep}
    e = Event({
        "apiVersion": "v1",
        "kind": "Event",
        "metadata": {
            "name": f"aicp-api-server-billing-error-{int(time.time() * 1000)}",
            "namespace": "aicp-system",
        },
        "involvedObject": {
            "apiVersion": "apps/v1",
            "kind": "Deployment",
            "name": "aicp-web-app",
            "namespace": "aicp-system",
        },
        "reportingComponent": "aicp-api-server",
        "reportingInstance": "aicp-api-server",
        "reason": EventReason.InvokeBillingFailed.value,
        "message": json.dumps(message),
        "type": "Warning",
        "firstTimestamp": datetime.datetime.now().astimezone().isoformat(),
        "lastTimestamp": datetime.datetime.now().astimezone().isoformat(),
    })
    e.create()


class BillingService(IaasClient):
    """账单管理类, 属于单例类

    Args:
        IaasClient (_type_): _description_

    Returns:
        _type_: _description_
    """

    PRODUCTION_PRODUCT_CODE = "Should be replaced by real production product code"
    PRODUCTION_PREFIX = "Should be replaced by real production prefix"
    PRODUCTION_SPEC_ID = "Should be replaced by real production spec id"

    def __init__(self):
        """老billing接口,
        billing 流程设计文档: https://cwiki.yunify.com/pages/viewpage.action?pageId=137055724
        billing 支持: https://wiki.yunify.com/pages/viewpage.action?pageId=145916287
        billing yapi: https://yapi.yunify.com/project/405/interface/api

        Args:
            ctx: 实例context
        """
        self.service_name = "billing"
        self.zone = app.settings.qingcloud_zone
        self.enable = app.settings.billing_enable

        super().__init__()

    @classmethod
    def get_resource_id(cls, resource_id):
        """获取资源ID

        Args:
            resource_id (str): 资源ID

        Returns:
            str: 资源ID
        """
        return cls.PRODUCTION_PREFIX + resource_id

    def __send_request(self, action, req, url='/iaas/', verb='GET', exception=True):
        try:
            rep = super().send_request(action, req, url=url, verb=verb)
            logger.debug("handle request rep[%s]" % rep)
            if rep['ret_code'] != 0:
                create_error_billing_event(req, rep)
                if rep['ret_code'] == 2100:
                    return rep
                logger.error("handle request failed ,rep[%s]" % rep)
                if exception:
                    raise BillingServiceException(rep.get("ret_code"), rep.get("message"))
            return rep
        except Exception as e:
            logger.error("handle request failed ,exception[%s]" % e)
            create_error_billing_event(req, str(e))
            raise e

    def lease(self, resource_id, user, price_info, check_resource_balance=False,
              duration=3600, charge_mode="elastic", auto_renew=0, count=1, next_charge_mode: str = None,
              price_type="new", **kwargs):
        """
        创建或更新租赁信息(Lease)
        :param resource_id:
        :param user:
        :param price_info:
        :param check_resource_balance:
        :param duration:
        :param charge_mode:
        :param auto_renew:
        :param count:
        :param next_charge_mode:
        :param price_type:
        :param kwargs:
        :return:
        """
        if not duration:
            duration = 3600
        if duration == 3600 or duration==0:
            charge_mode = "elastic"
        else:
            charge_mode = "monthly"
        return self.__lease(resource_id, user, price_info, check_resource_balance, duration, charge_mode, auto_renew,
                            count, next_charge_mode, price_type, **kwargs)

    def __lease(self, resource_id, user, price_info, check_resource_balance=False,
                duration=3600, charge_mode="elastic", auto_renew=0, count=1, next_charge_mode: str = None,
                price_type="new", is_skip_handle_failure=1, **kwargs):
        """创建或更新租赁信息(Lease)

        接口 https://yapi.yunify.com/project/405/interface/api/11809

        租赁资源。
        可用于如下场景：
        创建租赁信息: 创建pending资源时，同步创建租赁信息（仅管理员）
        更新租赁信息: 更新资源属性完成后，更新租赁信息

        """
        if count and check_resource_balance:
            self.__check_resource_balance(resource_id, user, price_info, price_type=price_type, duration=duration)

        price_info.update({"spec_id": self.PRODUCTION_SPEC_ID})
        price_info.update({"replicas": count})

        lease_info = {
            "duration": duration,
            "user": user,
            "charge_mode": charge_mode,
            "auto_renew": auto_renew,
            "resources": [self.PRODUCTION_PREFIX + resource_id],
            "zone": self.zone,
            "price_info": json.dumps(price_info),
            "next_charge_mode": next_charge_mode,
            "replicas": count,
            "is_skip_handle_failure": is_skip_handle_failure,
        }
        lease_info.update(kwargs)
        return self.__send_request("Lease", lease_info, verb="GET", exception=True)

    def post_lease(self, resource_id, user, price_info, start_time, end_time, **kwargs):
        """一次性扣费接口(post_lease),

        接口 https://yapi.yunify.com/project/405/interface/api/22979

        用于一次性扣费，扣费后资源的计费不可做其他变更。目前仅支持按需模式
        """
        price_info.update({"spec_id": self.PRODUCTION_SPEC_ID})
        lease_info = {
            "zone": self.zone,
            "resource": self.PRODUCTION_PREFIX + resource_id,
            "user": user,
            "price_info": price_info,
            "start_time": start_time,
            "end_time": end_time
        }
        lease_info.update(kwargs)
        return self.__send_request("PostLease", lease_info, verb="GET")

    def unlease(self, resource_id, user, cease=0, force=0):
        """删除租赁信息(Lease)

        接口 https://yapi.yunify.com/project/405/interface/api/11814

        删除租赁信息。
        终止资源时，先释放租赁信息，租赁信息会等待资源终止后，释放租赁，停止计费。
        租赁信息及资源的状态关系 参照 计费主流程：租赁信息与资源控制

        """
        lease_info = self.__get_lease_info(resource_id, user)
        if not (lease_info and lease_info.get("status") not in ["ceasing", "ceased"]):
            return
        if lease_info.get("ret_code") == 2100:
            logger.info("resource not found in billing")
            return
        lease_info = {
            "cease": cease,
            "zone": self.zone,
            "resources": [self.PRODUCTION_PREFIX + resource_id],
            "user": user,
            "force": force,
        }
        return self.__send_request("Unlease", lease_info, verb="GET")

    def check_resource_balance(
            self, resource_id, user, price_info, price_type="new", currency="cny",
            duration=3600, count=1
    ):
        """余额检查(CheckResourcesBalance)
        """
        return self.__check_resource_balance(resource_id, user, price_info, price_type, currency, duration, count)

    def __check_resource_balance(
            self, resource_id, user, price_info, price_type="new", currency="cny",
            duration=3600, count=1
    ):
        """余额检查(CheckResourcesBalance)

        接口 https://yapi.yunify.com/project/405/interface/api/14054

        租赁资源。
        可用于如下场景：
        创建租赁信息: 创建pending资源时，同步创建租赁信息（仅管理员）
        更新租赁信息: 更新资源属性完成后，更新租赁信息

        """
        price_info.update({"spec_id": self.PRODUCTION_SPEC_ID})
        resources_multi = []
        for i in range(count):
            resources_multi.append(
                {
                    "sequence": i,
                    "resource_id": self.PRODUCTION_PREFIX + str(i),
                    "price_info": json.dumps(price_info),
                }
            )
        lease_info = {
            "user": user,
            "resources": resources_multi,
            "price_type": price_type,
            "currency": currency,
            "zone": self.zone,
            "duration": duration,
        }
        return self.__send_request("CheckResourcesBalance", lease_info, verb="GET", exception=True)

    def get_price(self, user, price_info, currency="cny",
                  duration=3600, charge_mode="elastic", *args, **kwargs):
        return self.__get_price(user, price_info, currency, duration, charge_mode, *args, **kwargs)

    def __get_price(self, user, price_info, currency="cny",
                    duration=3600, charge_mode="elastic", *args, **kwargs):
        """获取单价

        Args:
            user (_type_): user id
            price_info (_type_): {"size" : 20}
            type_ (_type_): pro center type code
            currency (str, optional): 币种. Defaults to "cny".

        Returns:
            _type_: _description_
            :param currency:
            :param user:
            :param price_info:
            :param charge_mode:
            :param duration:
        """
        price_info.update({"spec_id": self.PRODUCTION_SPEC_ID})
        lease_info = {
            "user": user,
            "resources": [dict(sequence=0, type=self.PRODUCTION_PRODUCT_CODE, **price_info)],
            "currency": currency,
            "zone": self.zone,
            "duration": duration,
            "charge_mode": charge_mode
        }
        return self.__send_request("GetPrice", lease_info, verb="GET", exception=True)

    def get_lease_info(self, resource_id, user):
        """
        获取租赁信息
        :param resource_id:
        :param user:
        :return:
        """
        return self.__get_lease_info(resource_id, user)

    def __get_lease_info(self, resource_id, user):
        """获取租赁信息

        Args:
            resource_id (_type_): resource id
            user (_type_): user id
        """

        lease_info = {
            "resource": self.PRODUCTION_PREFIX + resource_id,
            "user": user,
            "zone": self.zone,
        }
        lease_info = self.__send_request("GetLeaseInfo", lease_info, verb="GET", exception=True).get("lease_info", {})
        return lease_info

    def get_lease_infos(self, resource_ids, user=None):
        """
        获取租赁信息
        :param resource_ids:
        :param user:
        :return:
        """
        return self.__get_lease_infos(resource_ids, user)

    def __get_lease_infos(self, resource_ids, user=None):
        """获取租赁信息

        Args:
            resource_ids (_type_): resource id
            user (_type_): user id
        """

        lease_info = {
            "resources": [self.PRODUCTION_PREFIX + resource_id for resource_id in resource_ids],
            "user": user,
            "zone": self.zone,
        }
        lease_infos = self.__send_request("GetLeaseInfos", lease_info, verb="GET", exception=True).get("lease_info_set",
                                                                                                       [])
        return lease_infos

    def get_charge_records(self, resource_id, user):
        """获取计费记录

        Args:
            resource_id (_type_): resource id
            user (_type_): user id
        """

        lease_info = {
            "resource": self.PRODUCTION_PREFIX + resource_id,
            "user": user,
            "zone": self.zone,
        }
        charge_records = self.__send_request("GetChargeRecords", lease_info, verb="GET", exception=True).get(
            "charge_record_set", [])
        return charge_records

    def update_lease(self, resource_id, user, new_price_info, count):
        """
        更新租赁信息
        :param resource_id:
        :param user:
        :param new_price_info:
        :param count:
        """
        self.__update_lease(resource_id, user, new_price_info, count)

    def __update_lease(self, resource_id, user, new_price_info, count, is_skip_handle_failure=1, recover=False):
        """
        更新租赁信息
        :param resource_id:
        :param user:
        :param new_price_info:
        :param count:
        :return:
        """
        lease = self.__get_lease_info(resource_id, user)
        if not lease:
            logger.info("lease info not found, no need to update.")
            return
        contract = lease["contract"]

        # 包月资源暂停不停止扣费
        if contract.get("charge_mode") == "monthly" and not new_price_info and count == 0:
            logger.info("monthly charge mode, no need to suspend billing order.")
            return
        # 目前暂停情况下, 已暂停和暂停中的状态不处理
        if count == 0 and lease["status"] in ["suspended", "suspending"]:
            logger.info("lease info suspended, no need to update.")
            return
        if new_price_info is None and count == int(contract.get("price_info").get("replicas")) and not recover:
            logger.info("lease info not changed, no need to update.")
            return
        if recover and lease["status"] == "active" and int(contract.get("price_info").get("replicas")) > 0:
            return {"ret_code": 0}
        # lease["status"] ["suspended", "suspending"] and  int(contract.get("price_info").get("replicas")) > 0 欠费暂停
        price_info = new_price_info or contract["price_info"]
        rep =  self.__lease(
            resource_id, user, price_info=price_info, check_resource_balance=True,
            duration=VALID_LEASING_DURATION[contract.get("duration")], charge_mode=contract.get("charge_mode"),
            auto_renew=contract.get("auto_renew"), count=count,
            next_charge_mode=contract.get("next_charge_mode"),
            is_skip_handle_failure=is_skip_handle_failure
        )
        logger.info(f"update billing rep = {rep}")
        return rep

    def suspend(self, resource_id, user):
        """暂停计费

        Args:
            resource_id (_type_): resource id
            user (_type_): user id
        """
        return self.__update_lease(resource_id, user, None, 0)

    def restart(self, resource_id, user, new_price_info=None, count=1, recover=False):
        """重启计费

        Args:
            resource_id (_type_): resource id
            user (_type_): user id
            new_price_info (_type_): new price info
            count (_type_): count
        """
        return self.__update_lease(resource_id, user, new_price_info, count, recover=recover)

    @classmethod
    def get_real_resource_id(cls, resource_id):
        """获取真实资源ID

        Args:
            resource_id (str): 资源ID

        Returns:
            str: 资源ID
        """
        return resource_id.removeprefix(cls.PRODUCTION_PREFIX)


class QAIBillingService(BillingService):
    PRODUCTION_PRODUCT_CODE = "qai"
    PRODUCTION_PREFIX = "qai-"
    PRODUCTION_SPEC_ID = "aipods"


@AsyncifyMethods
class AsyncQAIBillingService(QAIBillingService):
    """
    Please use awaitable methods in this class

    eg:
        await AsyncQAIBillingService().get_price(...)
        await AsyncQAIBillingService().lease(...)
    """
    pass
