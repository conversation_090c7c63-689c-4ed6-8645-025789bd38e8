import json
from typing import List, Dict

import httpx

import app
from app import logger
from app.core.qingcloud.auth import get_signature
from app.core.qingcloud.interface import describe_access_key_by_user_id


class MaasClient():
    # Maas客户端
    def __init__(self):
        self._maas_svc = app.settings.MAAS_SERVER_SVC
        self._base_url = f"http://{self._maas_svc}"
        self._client = httpx.Client(
            base_url=self._base_url,
        )

    def get_maas_server_num(self, user_id: str):
        maas_server_num_path = f"/maas/admin/inference_service/usage"
        logger.info(f"Get maas server num, user_id:{user_id}")
        try:
            params = {"user_ids": user_id}
            headers = {
                "X-Remote-Group": "system:authenticated",
                "X-Remote-User": "admin"
            }
            res = self._client.get(url=maas_server_num_path, params=params, headers=headers)
            if res.status_code == 200:
                maas_data = res.json()["data"]
                logger.info(f"Get maas server num success, user_id:{user_id},{maas_data}")
                return res.json()["data"]
            else:
                return None
        except Exception as e:
            logger.error(f"Get maas server num failed, user_id:{user_id}, e:{e}")
            return None

    def get_public_model(self, user_id: str):
        path = "/api/model/square"
        try:
            params = {"offset": 0,"size": 100, "tags": "txt2txt"}
            headers = {
                "X-Remote-Group": "system:authenticated",
                "X-Remote-User": "admin"
            }
            res = self._client.get(url=path, params=params, headers=headers)
            if res.status_code == 200:
                logger.info(f"Get maas server success")
                return res.json()["list"]
            else:
                return None
        except Exception as e:
            logger.error(f"Get maas server num failed, user_id:{user_id}, e:{e}")
            return None

    def get_public_model_detail(self, model_id):
        url_path = f"/maas/api/model/square/{model_id}"
        try:
            headers = {
                "X-Remote-Group": "system:authenticated",
                "X-Remote-User": "admin"
            }
            res = self._client.get(url=url_path, headers=headers)
            logger.info("Get maas server %s", res.text)
            if res.status_code == 200:
                logger.info(f"Get maas server success")
                return res.json()["data"]
            else:
                return None
        except Exception as e:
            logger.error(f"Get maas server failed,e:{e}")
            return None

    def create_user_model(self, name: str, tags: list[str], host_path:str, user_id:str):
        # rep = describe_access_key_by_user_id(user_id)
        # access_key_id = rep.get("access_key_set")[0]["access_key_id"]
        # secret_access_key = rep.get("access_key_set")[0]["secret_access_key"]
        url_path = "/maas/api/model"
        # requests_param = get_signature("POST", url_path, access_key_id, secret_access_key, {})
        mounts = [{
            "host_path": host_path,
            "mount_path": "/root/epfs/model"
        }]
        headers = {
            "aicp-userid": user_id,
            "Content-Type": "application/json"
        }
        harbor_host = app.settings.DOCKER_REGISTRY
        image = {
            "type": "official",
            "image": f"{harbor_host}/aicp/aicp/llama-factory:latest",
            "run_cmd": f"cd /app; export API_PORT=8000 ;llamafactory-cli api --model_name_or_path=/root/epfs/model"
        }
        # inference_engine = [
        #     {
        #         "engine_id": "vLLM",
        #         "run_cmd": "sleep 36000"
        #     }
        # ]
        body = {
            "name": name,
            "brief": "调优模型",
            "tags": tags,
            "source_channel": "local_path",
            "mounts": mounts,
            "image": image,
            # "inference_engine": inference_engine,
            "port": 8000,
            # "requirements": "einops==0.8.0 torchsde==0.2.6",
            "category": "official"
        }
        try:
            res = self._client.post(url=url_path, json=body, headers=headers)
            logger.debug(f"Create user model {body}, res:{res.text}")
            if res.status_code == 200 and res.json()["ret_code"] == 0:
                logger.info(f"Get maas server success")
                return res.json()
            else:
                return None
        except Exception as e:
            logger.error(f"Get maas server failed,e:{e}")
            return None