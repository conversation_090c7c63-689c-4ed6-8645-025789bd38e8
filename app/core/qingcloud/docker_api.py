from httpx import Client, Timeout

import app
from app import logger
from app.core.models import QingcloudUser
from app.core.qingcloud.exceptions import GetDockerRepoException
from app.core.qingcloud.utils import generate_signature


class QingcloudDockerApiClient:
    HOST = app.settings.QINGCLOUD_DOCKER_API_SERVER
    ZONE = app.settings.QINGCLOUD_GPSE_ZONE

    def __init__(self, user: QingcloudUser):
        self.user = user
        self.client = Client(base_url=self.HOST, timeout=Timeout(20.0))

    def get_signature_params(self, method: str, url: str, params: dict) -> str:
        """

        :param method:
        :param url:
        :param params:
        """
        logger.debug(f"get_signature_params: {method}, {url}, {params}")
        return generate_signature(method, url, self.user.access_key_id, self.user.secret_access_key, params)

    def check_rsp_error(self, r):
        if r.json().get("ret_code") != 0:
            logger.error("get gpfs rsp [%s]", r.text)
            raise GetDockerRepoException(r.json().get("message"))

    def get_repo_name(self) -> str:
        """
        :return: list of filesystems
        """
        URL = "/qai/docker/api/v1/repo"
        params = {
            "zone": self.ZONE
        }
        URL += f"?{self.get_signature_params('GET', URL, params)}"
        r = self.client.get(URL)
        logger.debug(f"get_repo_name: {URL} and rsp: {r.content}")
        self.check_rsp_error(r)
        logger.debug(f"get filesets: {r.json()}")
        return r.json().get("namespace_path")

    def create_user_and_repo(self):
        try:
            """
                    :return: list of filesystems
                    """
            URL = "/qai/docker/api/v1/namespace"
            params = {
                "zone": self.ZONE
            }
            URL += f"?{self.get_signature_params('POST', URL, params)}"
            r = self.client.post(URL)
            logger.debug(f"get_repo_name: {URL} and rsp: {r.content}")
            self.check_rsp_error(r)
            logger.debug(f"get filesets: {r.json()}")
        except Exception as e:
            logger.error(f"get filesets error: {e}")
        return None

    def get_repo_quota(self) -> str:
        """
        查询镜像仓库配额
        """
        URL = "/docker/api/v1/repo/quota"
        parmas = {
            "zone": self.ZONE
        }
        URL += f"?{self.get_signature_params('GET', URL, parmas)}"
        r = self.client.get(URL)
        logger.debug(f"get_repo_quota: {URL} and rsp: {r.content}")
        self.check_rsp_error(r)
        logger.debug(f"get_repo_quota: {r.json()}")
        quota_used = r.json().get("quota_used")
        project_used = r.json().get("project_used")
        return [quota_used, project_used]




