import base64
import hashlib
import hmac
import json
from collections import OrderedDict
from hashlib import sha256
from typing import Any, Dict
from urllib import parse

from sqlalchemy import Update, text
from starlette.requests import HTTPConnection

import app
from app.core.db import get_db_context_session
from app.core.exceptions import PermissionDeniedException
from app.core.global_server.global_server import GlobalServerClient
from app.core.loggers import logger
from app.core.qingcloud.interface import describe_access_key_by_user_id, describe_user


def hex_encode_md5_hash(data):
    if not data:
        data = "".encode("utf-8")
    else:
        data = data.encode("utf-8")
    md5 = hashlib.md5()
    md5.update(data)
    return md5.hexdigest()


def get_signature(method: str, url: str, ak: str, sk: str, params: dict):
    '''
    :param url: /api/test/  must be end /
    :param ak: access_key_id
    :param sk:  secure_key
    :param params: dict type
    :param method: method GET POST PUT DELETE
    :return:
    '''

    params["access_key_id"] = ak
    keys = sorted(params.keys())
    sorted_param = OrderedDict()
    for key in keys:
        sorted_param[key] = params[key]

    requests_param = parse.urlencode(sorted_param, safe='/', quote_via=parse.quote, doseq=True)

    string_to_sign = method + "\n" + url + "\n" + requests_param + "\n" + hex_encode_md5_hash("")
    h = hmac.new(sk.encode(encoding="utf-8"), digestmod=sha256)
    h.update(string_to_sign.encode(encoding="utf-8"))
    sign = base64.b64encode(h.digest()).strip()
    signature = parse.quote_plus(sign.decode())
    signature = parse.quote_plus(signature)

    requests_param += "&signature=%s" % signature
    return requests_param


def update_last_access_time(user_id):
    """
    更新用户最后的访问时间
    :param user_id:
    """
    with get_db_context_session() as session:
        session.execute(text("UPDATE user_info SET last_login_at = NOW() WHERE user_id = :user_id"),
                        {"user_id": user_id})
        session.commit()


def check_auth(request: HTTPConnection) -> Dict[str, Any]:
    """
    验证 qingcloud 请求是否有效
    :param request:
    :return:

    from extauth>= v2.15.0 the requests has header aicp-userinfo
    {
        "rating": 0,
        "gravatar_email": "<EMAIL>",
        "zones": [
            "ap2a"
        ],
        "user_id": "usr-RMfXzGXv",
        "personal_name": "梁印",
        "regions": [
            "sh1",
            "gd2",
            "jinan1",
            "pekt3"
        ],
        "iam_domain": "usr-RMfXzGXv",
        "privilege": 1,
        "role": "user",
        "user_name": "aizjl",
        "email": "<EMAIL>#aizjl",
        "status": "active",
        "user_type": 1,
        "phone": "86-19938072350",
        "console_id": "qingcloud",
        "lang": "zh-cn",
        "root_user_id": "usr-Bia7OHGH",
        "access_key_id": "VPDWSPYIQTNVQGRPUHFO",
        "secret_access_key": "D7VbhpcYKdL6YcmlO9wIxOUM7NKPsgXIbUBHiUZ1",
        "group_info": {
            "sub_acc_consume": 1,
            "permissions": [
                {
                    "role_id": "ro-NznDicKv",
                    "module": "NB",
                    "permission": "OWN",
                    "module_name": "容器实例"
                },
                {
                    "role_id": "ro-NznDicKv",
                    "module": "TN",
                    "permission": "OWN",
                    "module_name": "训练任务"
                },
                {
                    "role_id": "ro-NznDicKv",
                    "module": "INF",
                    "permission": "OWN",
                    "module_name": "推理服务"
                },
                {
                    "role_id": "ro-NznDicKv",
                    "module": "EPFS",
                    "permission": "OWN",
                    "module_name": "存储与数据"
                },
                {
                    "role_id": "ro-NznDicKv",
                    "module": "RG",
                    "permission": "OWN",
                    "module_name": "专属资源"
                }
            ],
            "version": 1737599418342
        }
    }

    """
    if app.settings.debug:
        logger.debug("check_auth: disable_auth is True, use mock_user")
        return app.settings.mock_user

    user_id = request.headers.get("aicp-userid")
    if user_id is None:
        logger.warning("Header's aicp-userid is missing")
        raise PermissionDeniedException("user_id is None")

    aicp_user_info = request.headers.get("aicp-userinfo")
    if aicp_user_info:
        user_info = json.loads(aicp_user_info)
        if "user_from" in user_info.keys():
            user_info.pop("user_from", None)
        user_info["permissions"] = user_info["group_info"]["permissions"]
    else:
        users = describe_user(user_id)
        if not users.get("user_set"):
            logger.warning("Get user_set error.")
            raise PermissionDeniedException(user_id)

        ak = describe_access_key_by_user_id(user_id)
        if not ak.get("access_key_set"):
            raise PermissionDeniedException(user_id)

        user_info = users.get("user_set")[0]
        user_info["access_key_id"] = ak.get("access_key_set")[0]["access_key_id"]
        user_info["secret_access_key"] = ak.get("access_key_set")[0]["secret_access_key"]
        # 子账号获取用户权限
        if user_info["root_user_id"] != user_info["user_id"]:
            user_info["permissions"] = GlobalServerClient().get_group_auth_info(user_info)["data"]["permissions"]

    # 更新用户最后的访问时间
    update_last_access_time(user_id)

    return user_info
