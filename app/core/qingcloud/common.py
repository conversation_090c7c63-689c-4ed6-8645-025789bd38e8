import datetime
from typing import Dict

import cachetools
import requests

import app
from app import logger
from app.core.qingcloud.auth import get_signature
from app.core.qingcloud.interface import describe_access_key_by_user_id, describe_user


def send_to_push_server(action, resource, status, user_id, resource_type=None, reason=None, op_id=None, extend_user_id=None):
    '''
    :param action:
    :param resource: 资源id
    :param status: 资源状态
    :param user_id: 用户id
    :param resource_type: 资源类型，notebook, train ,image
    :param reason:
    :param op_id: 操作id,通过OperationRecordCrud创建，如有必要可在数据库中记录此操作
    :return:
    '''
    if extend_user_id:
        user_ids = {user_id, extend_user_id}
    else:
        user_ids = [user_id]
    for user_id in user_ids:
        logger.info("start send to push")
        rep = describe_access_key_by_user_id(user_id)
        if not rep.get("access_key_set"):
            logger.error(f"user {user_id} pitrix access key not found, failed to push message: {resource_type}-{action}-{status}-{reason}")
            return
        access_key_id = rep.get("access_key_set")[0]["access_key_id"]
        secret_access_key = rep.get("access_key_set")[0]["secret_access_key"]
        data = {
            "action": action,
            "op_id": op_id,
            "resource": resource,
            "child_resource": resource,
            "status": status,
            "user_id": user_id,
            "reason": reason,
            "resource_type": resource_type
        }

        params = {"user_id": user_id}
        signature = get_signature(method="POST", url='/push/ms/', ak=access_key_id,
                                  sk=secret_access_key,
                                  params=params)
        logger.debug(app.settings.AI_CLOUD_PUSH_SERVER)
        r = requests.post(app.settings.AI_CLOUD_PUSH_SERVER + "?" + signature, json=data)
        logger.info("get rep from push server [%s]", r.text)


@cachetools.cached(cache=cachetools.TTLCache(maxsize=1024, ttl=86_400))
def get_qingcloud_user_info_by_user_id(user_id: str) -> Dict:
    """
    获取用户信息
    :param user_id:
    :return:
    """
    users = describe_user(user_id)
    if not users.get("user_set"):
        raise ValueError(f"not found user by user_id {user_id}")

    ak = describe_access_key_by_user_id(user_id)
    if not ak.get("access_key_set"):
        raise ValueError(f"not found access_key by user_id {user_id}")

    user = users.get("user_set")[0]
    user["access_key_id"] = ak.get("access_key_set")[0]["access_key_id"]
    user["secret_access_key"] = ak.get("access_key_set")[0]["secret_access_key"]
    return user
