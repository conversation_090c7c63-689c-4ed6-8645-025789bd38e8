import asyncio
import copy

from qingcloud.iaas import APIConnection
import copy

from typing import List

import json

from app.core.exceptions import QingcloudApiException
from app.core.loggers import logger
import app
from app.core.rlock import RedisClient

ZONE_INFO = app.settings.qingcloud_zone_zh if app.settings.qingcloud_zone_zh != "" else app.settings.qingcloud_zone
INSTANCE_SHUTDOWN = "container_instance_timed_shutdown"
INSTANCE_RELEASE = "container_instance_scheduled_release"
INSTANCE_ERROR = "container_instance_error"
AICP_JOB_STATUS = "aicp_job_status"
AICP_JOB_RESTART = "aicp_job_restart"
RELEASE_RESOURCE = "release_resource"


class IaasClient(object):
    """

    """

    def __init__(self, conf=app.settings.qingcloud):
        logger.info("init iaas client, conf[%s]" % conf)
        self.iaas_client = APIConnection(conf.get('qingcloud_access_key_id'),
                                         conf.get('qingcloud_secret_access_key'),
                                         conf.get('qingcloud_default_zone', 'test'),
                                         host=conf.get('qingcloud_host'),
                                         port=conf.get('qingcloud_port'),
                                         protocol=conf.get('qingcloud_protocol'))

    def send_request(self, action, req, url='/iaas/', verb='GET', exception=False):
        rep = self.iaas_client.send_request(action, req, url=url, verb=verb)
        if rep['ret_code'] != 0:
            logger.warning("handle request failed ,rep[%s]" % rep)
            if exception:
                raise QingcloudApiException(rep.get("message"))

        return rep

    async def send_request_async(self, action, req, url='/iaas/', verb='GET', exception=False):
        loop = asyncio.get_event_loop()
        rep = await loop.run_in_executor(None, self.iaas_client.send_request, action, req, url, verb)
        if rep['ret_code'] != 0:
            logger.warning("handle request failed ,rep[%s]" % rep)
            if exception:
                raise QingcloudApiException(rep.get("message"))


def describe_access_key(access_key_id):
    r = RedisClient().get(f"{access_key_id}_info")
    if r:
        logger.debug(f"get access_key from cache {r}")
        return json.loads(r)
    req = {
        'access_keys': [access_key_id]
    }
    action = "DescribeAccessKeys"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        RedisClient().set(f"{access_key_id}_info", json.dumps(rsp), ex=86400)
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
    return rsp


def describe_access_key_by_user_id(user_id):
    r = RedisClient().get(user_id)
    if r:
        logger.debug(f"get access_key from cache {r}")
        return json.loads(r)
    req = {
        "owner": [user_id],
        "controller": "pitrix"
    }
    action = "DescribeAccessKeys"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
        RedisClient().set(user_id, json.dumps(rsp), ex=86400)
    return rsp


def describe_user_without_redis(user_ids):
    req = {
        'users': user_ids,
        'offset': 0,
        'limit': 1000,
    }
    action = "DescribeUsers"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
    return rsp


def describe_user(user_id):
    r = RedisClient().get(f"{user_id}_info")
    if r:
        logger.debug(f"get user from cache {r}")
        return json.loads(r)
    req = {
        'users': [user_id]
    }
    action = "DescribeUsers"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        RedisClient().set(f"{user_id}_info", json.dumps(rsp), ex=86400)
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
    return rsp


def describe_sub_users(user_id, ak, sk, offset=0, limit=10):
    req = {
        'owner': user_id,
        'offset': offset,
        'limit': limit,
        'status': ["active"]

    }
    action = "DescribeSubUsers"
    user_qingcloud = copy.deepcopy(app.settings.qingcloud)
    user_qingcloud["qingcloud_access_key_id"] = ak
    user_qingcloud["qingcloud_secret_access_key"] = sk
    iaas_client = IaasClient(user_qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
    return rsp


def describe_sub_users_without_ak_sk(user_id, offset=0, limit=1000):
    req = {
        'owner': user_id,
        'offset': offset,
        'limit': limit,
        'status': ["active"]

    }
    action = "DescribeSubUsers"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
    return rsp


def describe_users(user_ids: List[str]) -> List[dict]:
    """
    获取用户信息
    :param user_ids:
    :return:
    """
    if not user_ids:
        return []
    req = {
        'users': user_ids,
        'offset': 0,
        'limit': len(user_ids)
    }
    action = "DescribeUsers"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req, exception=True)
    return rsp['user_set']


def describe_user_by_type(type, input):
    # send request to qingcloud
    if type == "phone":
        req = {
            'phone': input,
        }
    if type == "email":
        req = {
            'email': input,
        }
    action = "DescribeUsers"
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request(action, req)
    if not isinstance(rsp, dict) or rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
        return rsp
    else:
        logger.debug(
            "get iaas requests from iaas suc, action=[%s] req[%s] "
            "rsp[%s]" % (action, req, rsp))
    return rsp


def delete_user(user_id):
    iaas_client = IaasClient(app.settings.qingcloud)
    action = "DeleteUsers"
    req = {
        'users': user_id
    }
    rsp = iaas_client.send_request(action, req)
    if rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
    return rsp


def restore_user(user_id):
    iaas_client = IaasClient(app.settings.qingcloud)
    action = "RestoreUsers"
    req = {
        'users': user_id
    }
    rsp = iaas_client.send_request(action, req)
    if rsp.get('ret_code') != 0:
        logger.error("get iaas requests failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
    return rsp


def get_user_email(owner):
    email = None
    rsp = describe_user(owner)
    user_set = rsp["user_set"]
    if user_set:
        email = user_set[0]["email"]
    return email


def product_center_query_request(values=None, offset=0, limit=20,
                                 search_word=None,
                                 console_id=app.settings.default_console_id,
                                 regin_id=app.settings.default_regin_id,
                                 filters=None,
                                 status=None,
                                 **kwargs):
    if status is None:
        status = ["sale"]
    params = {
        "prod_id": "qai",
        "console_id": console_id,
        "region_id": [regin_id],
        "status": status,
        "field_mask": ["price"],
        "version": "latest",
        "spec_id": "aipods",
        "offset": offset,
        "limit": limit
    }
    if not filters:
        filters = []
    if values:
        filters.append({"values": values, "code": "aipods_scope"}, )
    if kwargs:
        for key, values in kwargs.items():
            filters.append({"values": values, "code": key})
    params["filters"] = filters
    if search_word:
        params["search_word"] = search_word
    req = {
        "action": "ProductCenterQueryRequest",
        "path": "/v1/skus:search",
        "method": "POST",
        "params": json.dumps(params)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterQueryRequest", req)
    return rsp


def product_attr_query_request():
    params = {
        "prod_id": "qai",
        "attr_from": ["user", "quote_copy"],
        "spec_id": "aipods"
    }
    req = {
        "action": "ProductCenterQueryRequest",
        "path": "/v1/attributes",
        "method": "GET",
        "params": json.dumps(params)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterQueryRequest", req)
    return rsp


def product_aipod_type_query_request(search_word="规格类型"):
    params = {
        "prod_id": "qai",
        "attr_from": ["user", "quote_copy"],
        "search_word": search_word
    }
    req = {
        "action": "ProductCenterRequest",
        "path": "/v1/attributes",
        "method": "GET",
        "params": json.dumps(params)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterRequest", req)
    return rsp


def product_aipod_type_add_attribute(attr_id, prod_id, filters, incomplete_filter=True):
    data = {
        "prod_id": prod_id,
        "attr_id": attr_id,
        "filters": filters,
        "incomplete_filter": incomplete_filter
    }
    req = {
        "action": "ProductCenterQueryRequest",
        "path": "/v1/attributes",
        "method": "PATCH",
        "params": json.dumps(data)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterRequest", req)
    return rsp


def product_aipod_type_update_sku_and_price(prod_id):
    """
    产品更新规格和价格
    """
    data = {
        "async": True,
        "query": False
    }
    req = {
        "action": "ProductCenterQueryRequest",
        "path": f"/v1/products/{prod_id}/load_price",
        "method": "PATCH",
        "params": json.dumps(data)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterRequest", req)
    return rsp


def qai_spec_info(offset=0, limit=50, search_word="QAI"):
    data = {
        # "prod_id": "qai",
        # "attr_from": ["user", "quote_copy"],
        "search_word": search_word,
        "offset": offset,
        "limit": limit
    }
    req = {
        "action": "ProductCenterQueryRequest",
        "path": "/v1/specs",
        "method": "GET",
        "params": json.dumps(data)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterRequest", req)
    return rsp


def qai_add_sku(spec_code, filters, prod_id, spec_id, console_id=app.settings.default_console_id,
                region_id=app.settings.default_regin_id, status="sale", own=1, attr_id="replicas"):
    data = {
        # "prod_name": "",
        "spec_code": spec_code,
        # "pricing_type": "line",
        "skus": [
            {
                "filters": filters,
                "prod_id": prod_id,
                "console_id": console_id,
                "region_id": region_id,
                "status": status,
                "own": own,
                "spec_id": spec_id,
                "attr_id": attr_id
            }
        ],
        "prod_id": prod_id,
        "spec_id": spec_id
    }
    req = {
        "action": "ProductCenterQueryRequest",
        "path": "/v1/specs",
        "method": "PATCH",
        "params": json.dumps(data)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterRequest", req)
    return rsp


def qai_delete_sku(prod_id, spec_id, sku_ids):
    data = {
        "prod_id": prod_id,
        "spec_id": spec_id,
        "sku_ids": sku_ids,
        "description": "Delete by AICP.",
        "force": False
    }
    req = {
        "action": "ProductCenterRequest",
        "path": "/v1/skus",
        "method": "DELETE",
        "params": json.dumps(data)
    }
    logger.info(req)
    iaas_client = IaasClient(app.settings.qingcloud)
    rsp = iaas_client.send_request("ProductCenterRequest", req)
    return rsp


def send_message_request(template_code, user_id, data):
    iaas_client = IaasClient(app.settings.qingcloud)
    action = "SendMsgHubPost"
    req = {
        "users": [user_id],
        "template_code": template_code,
        "receiver_filter_type": "specified",
        "render_data": data,
        "notify_types": [
            "sms",
            "email",
            "web",
            "webhook"
        ]
    }
    rsp = iaas_client.send_request(action, req)
    if rsp.get('ret_code') != 0:
        logger.error("send iaas message failed, "
                     "action=[%s] rsp[%s]" %
                     (action, rsp))
    return rsp


def patch_user_info_for_models(models: List, user_key="user_id"):
    """
    补充用户信息
    :param models:
    :param user_key:
    """
    user_ids = set()
    for model in models:
        user_ids.add(getattr(model, user_key))
    user_infos = describe_users(list(user_ids))
    user_info_dict = {user["user_id"]: user for user in user_infos}
    for model in models:
        user_info = user_info_dict.get(getattr(model, user_key))
        if user_info:
            if hasattr(model, "user_name"):
                setattr(model, "user_name", user_info["user_name"])
            if hasattr(model, "email"):
                setattr(model, "email", user_info["email"])


# def list_product_attributes():
