import base64
import collections
import hashlib
import hmac
import urllib
from urllib import parse
from typing import Dict


def hex_encode_md5_hash(data) -> str:
    if not data:
        data = "".encode("utf-8")
    else:
        data = data.encode("utf-8")
    md5 = hashlib.md5()
    md5.update(data)
    return md5.hexdigest()


def generate_signature(method: str, url: str, ak: str, sk: str, params: Dict) -> str:
    """

    :param method:
    :param url:
    :param ak:
    :param sk:
    :param params:
    :return:
    """

    url += "/" if not url.endswith("/") else ""

    params["access_key_id"] = ak
    keys = sorted(params.keys())
    sorted_param = collections.OrderedDict()
    for key in keys:
        sorted_param[key] = params[key]
    requests_param: str = parse.urlencode(sorted_param)
    string_to_sign = f"{method}\n{url}\n{requests_param}\n{hex_encode_md5_hash('')}"
    h = hmac.new(sk.encode(encoding="utf-8"), digestmod=hashlib.sha256)
    h.update(string_to_sign.encode(encoding="utf-8"))
    sign = base64.b64encode(h.digest()).strip()
    signature = parse.quote_plus(sign.decode())
    signature = parse.quote_plus(signature)
    requests_param += "&signature=%s" % signature

    return requests_param
