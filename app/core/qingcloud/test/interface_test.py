import json
import unittest

import kr8s

from app.core.qingcloud.interface import describe_access_key, describe_user, describe_user_without_redis, \
    describe_user_by_type, send_message_request, INSTANCE_SHUTDOWN, product_center_query_request, \
    product_attr_query_request, qai_spec_info, qai_delete_sku, qai_add_sku, product_aipod_type_add_attribute
from app.jobs.init_sku.init_sku import set_sku_infos


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.k8s_client = None

    def test_describe_access_key(self):
        print(describe_access_key("EZGBBHHRUGQGZBYRUCDV"))

    def test_describe_user(self):
        print(describe_user("usr-DlGGouF5"))

    def test_describe_user_without_redis(self):
        print(describe_user_without_redis(["usr-mock-user-id", "usr-Ny21pLcc"]))

    def test_describe_user_by_type(self):
        print(describe_user_by_type("email", "<EMAIL>"))

    def test_message(self):
        data = {
            "id": "nb-xxxx",
            "spec": "16c 128g",
            "expire_time": "2024-10-09 10:19:07"
        }
        print(send_message_request(INSTANCE_SHUTDOWN, "usr-xV8J09cM", json.dumps(data)))

    def test_boss_sku_table(self):
        result = product_attr_query_request()
        res_data = []
        for attribute in result["attributes"]:
            item = {
                "name": attribute['name'],
                "prod_id": attribute['prod_id'],
                "attr_id": attribute['attr_id'],
                "attr_code": attribute['attr_code'],
            }
            res_data.append(item)
        print(res_data)

    def test_boss_sku(self):
        # 模拟前端传参
        offset = 0
        limit = 500
        # sort_kind = "gpu_count"
        # sort_kind = "aipods_type"
        sort_kind = "aipods_scope"
        res = product_center_query_request(offset=offset, limit=limit, status=["sale", "no_sale"])
        json_output = json.dumps(res, indent=4, ensure_ascii=False)
        print(json_output)
        skus = res["skus"]
        sorted_skus = sorted(skus, key=lambda sku: get_sorted_key(sku, sort_kind))
        print(sorted_skus)

    def test_add_sku(self):
        # 获取QAI规格项信息
        qai_spec = qai_spec_info()
        qai_prod_id = qai_spec["specs"][0]["prod_id"]
        qai_spec_id = qai_spec["specs"][0]["spec_id"]
        qai_spec_code = qai_spec["specs"][0]["spec_code"]
        # 获取boss规格属性
        result = product_attr_query_request()
        product_spec_attribute = {}
        for attribute in result["attributes"]:
            product_spec_attribute[f"{attribute['name']}"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"
        # mock前端传参
        sku_item = set_sku_infos("only_gpu",
                                 "inference_compute",
                                 "NVIDIA-GeForce-RTX-2080-Ti",
                                 "13",
                                 "58",
                                 "Intel Xeon Processor (Cascadelake)",
                                 "NVIDIA-GeForce-RTX-2080-Ti",
                                 "1",
                                 "11",
                                 "50",
                                 "0",
                                 "0",
                                 "0")
        boss_add_sku_attributes(product_spec_attribute, sku_item)
        result = qai_add_sku(qai_spec_code, sku_item, qai_prod_id, qai_spec_id)
        if result["ret_code"] == 0:
            print(f"boss添加规格信息完成")


    def test_modify_sku(self):
        # 获取QAI规格项信息
        qai_spec = qai_spec_info()
        qai_prod_id = qai_spec["specs"][0]["prod_id"]
        qai_spec_id = qai_spec["specs"][0]["spec_id"]
        qai_spec_code = qai_spec["specs"][0]["spec_code"]
        # 需要先将所有的attr属性增加一遍

        # status = "no_sale"
        sku_id = "sku_JOYrm8X4qn2G"
        # 先删除QAI规格
        qai_delete_sku(qai_prod_id, qai_spec_id, [sku_id])
        # 添加新的规格


    def test_delete_sku(self):
        # 获取QAI规格项信息
        qai_spec = qai_spec_info()
        qai_prod_id = qai_spec["specs"][0]["prod_id"]
        qai_spec_id = qai_spec["specs"][0]["spec_id"]
        qai_spec_code = qai_spec["specs"][0]["spec_code"]
        # 获取boss规格属性
        result = product_attr_query_request()
        product_spec_attribute = {}
        for attribute in result["attributes"]:
            # 高速网卡和IB网络保持统一
            if attribute['name'] == "高速网卡":
                product_spec_attribute["IB网络"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"
            else:
                product_spec_attribute[f"{attribute['name']}"] = f"{attribute['prod_id']}-{attribute['attr_id']}-{attribute['attr_code']}"

        # status = "no_sale"
        sku_id = "sku_8nMXkkyW49Aw"
        res = qai_delete_sku(qai_prod_id, qai_spec_id, [sku_id])
        print(res)


def get_sorted_key(sku, sort_kind):
    data = sku["filters"]
    for i in range(len(data)):
        if data[i]["attr_id"] == sort_kind:
            return data[i]["attr_value"]
    return ""


def set_sku_infos(
        aipods_type,
        aipods_scope,
        aipods_usage,
        cpu_count,
        memory,
        cpu_model,
        gpu_model,
        gpu_count,
        gpu_memory,
        os_disk,
        disk,
        nvlink,
        network
):
    """
    设置sku数据
    :param aipods_type: 规格类型
    :param aipods_scope: 产品范围
    :param aipods_usage: 资源类型
    :param cpu_count: CPU核数
    :param memory: 内存
    :param cpu_model: CPU型号
    :param gpu_model: GPU型号
    :param gpu_count: GPU数量
    :param gpu_memory: GPU显存
    :param os_disk: 系统盘
    :param disk: 数据盘
    :param nvlink: 是否存在nvlink
    :param network: ib网卡数量
    :return:
    """
    result = [
        {
            "attr_id": "aipods_type",
            "operator": "==",
            "attr_value": aipods_type
        },
        {
            "attr_id": "aipods_scope",
            "operator": "==",
            "attr_value": aipods_scope
        },
        {
            "attr_id": "aipods_usage",
            "operator": "==",
            "attr_value": aipods_usage
        },
        {
            "attr_id": "cpu_count",
            "operator": "==",
            "attr_value": cpu_count
        },
        {
            "attr_id": "memory",
            "operator": "==",
            "attr_value": memory
        },
        {
            "attr_id": "cpu_model",
            "operator": "==",
            "attr_value": cpu_model
        },
        {
            "attr_id": "gpu_model",
            "operator": "==",
            "attr_value": gpu_model
        },
        {
            "attr_id": "gpu_count",
            "operator": "==",
            "attr_value": gpu_count
        },
        {
            "attr_id": "gpu_memory",
            "operator": "==",
            "attr_value": gpu_memory
        },
        {
            "attr_id": "os_disk",
            "operator": "==",
            "attr_value": os_disk
        },
        {
            "attr_id": "nvlink",
            "operator": "==",
            "attr_value": nvlink
        }
    ]
    if disk != "0":
        result.append({
            "attr_id": "disk",
            "operator": "==",
            "attr_value": disk
        })
    if network != "0":
        result.append({
            "attr_id": "network",
            "operator": "==",
            "attr_value": network
        })
    return result


def boss_add_sku_attributes(product_spec_attribute, sku_info):
    for item in sku_info:
        if item['attr_id'] == 'aipods_usage':
            # 资源类型
            result = add_sku_attribute(product_spec_attribute, "资源类型", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加资源类型：{item['attr_value']}，成功。")
        if item['attr_id'] == 'cpu_count':
            # CPU核数
            result = add_sku_attribute(product_spec_attribute, "CPU核数", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加CPU核数：{item['attr_value']}，成功。")
        if item['attr_id'] == 'memory':
            # 内存
            result = add_sku_attribute(product_spec_attribute, "内存", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加内存：{item['attr_value']}，成功。")
        if item['attr_id'] == 'cpu_model':
            # CPU型号
            result = add_sku_attribute(product_spec_attribute, "CPU型号", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加CPU型号：{item['attr_value']}，成功。")
        if item['attr_id'] == 'gpu_model':
            # GPU型号
            result = add_sku_attribute(product_spec_attribute, "GPU型号", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加GPU型号：{item['attr_value']}，成功。")
        if item['attr_id'] == 'gpu_count':
            # GPU数量
            result = add_sku_attribute(product_spec_attribute, "GPU数量", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加GPU数量：{item['attr_value']}，成功。")
        if item['attr_id'] == 'gpu_memory':
            # GPU显存
            result = add_sku_attribute(product_spec_attribute, "GPU显存", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加GPU显存：{item['attr_value']}，成功。")
        if item['attr_id'] == 'os_disk':
            # 系统盘
            result = add_sku_attribute(product_spec_attribute, "系统盘", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加系统盘：{item['attr_value']}，成功。")
        if item['attr_id'] == 'nvlink':
            # nvlink
            result = add_sku_attribute(product_spec_attribute, "nvlink", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加nvlink：{item['attr_value']}，成功。")
        if item['attr_id'] == 'network':
            # IB网卡
            result = add_sku_attribute(product_spec_attribute, "IB网络", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加IB网卡：{item['attr_value']}，成功。")
        if item['attr_id'] == 'disk':
            # 数据盘
            result = add_sku_attribute(product_spec_attribute, "数据盘", item['attr_value'])
            if result["ret_code"] == 0:
                print(f"添加数据盘：{item['attr_value']}，成功。")


def add_sku_attribute(product_spec_attribute, sku_attr_type, value):
    prod_id = product_spec_attribute[sku_attr_type].split('-')[0]
    attr_id = product_spec_attribute[sku_attr_type].split('-')[1]
    attr_infos = [
        {
            "name": value,
            "attr_value": value,
            "description": value,
            "operator": "=="
        }
    ]
    return product_aipod_type_add_attribute(attr_id, prod_id, attr_infos)


if __name__ == '__main__':
    unittest.main()
