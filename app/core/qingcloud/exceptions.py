from ..exceptions import AICPBaseException


class GetGpfsFilesetsException(AICPBaseException):
    BASE_CODE = 2501
    BASE_MESSAGE = "获取gpfs文件集失败"



class LeaseFailedException(AICPBaseException):
    BASE_CODE = 2502
    BASE_MESSAGE = "订单创建失败"


class BillingServiceException(AICPBaseException):
    BASE_CODE = 2503
    BASE_MESSAGE = "计费服务异常"

    def __init__(self, code, message):
        self.billing_code = code
        self.message = message
        super().__init__(message)
        self.process_code()

    def process_code(self):
        if self.billing_code == 2400:
            self.message = "余额不足, 请充值"

class GetDockerRepoException(AICPBaseException):
    BASE_CODE = 2504
    BASE_MESSAGE = "获取docker仓库失败"