from typing import Union

import kr8s
from httpx import HTTPStatusError
from kr8s import NotFoundError
from kr8s.objects import PersistentVolume, PersistentVolumeClaim, APIObject

from app.core.models import QingcloudUser, VolumeSpecBase
from app.core.qingcloud.gpfs import QingcloudGpfsClient
from app import logger, settings
from app.core.volumes.exceptions import FileSetPermissionException, GetGpfsFilesetException
from app.core.volumes.objects import StorageClass


class BaseVolume:
    """
    BASE Volume
    """

    def __init__(self, user: QingcloudUser, namespace: str, volume_spec: VolumeSpecBase, **kwargs):
        self.user = user
        self.namespace = namespace
        self.volume_spec = volume_spec
        self.quota = None

    def is_read_only(self) -> bool:
        """
        check if volume is read only
        :return:
        """
        return self.volume_spec.permission == "ro"

    @property
    def exists(self) -> bool:
        """
        check if volume exists, default is True, need to be implemented in subclass
        :return:
        """
        return True

    @property
    def storage_class_name(self) -> str:
        """
        storage class name format: qingcloud-gpfs-storage-class-{file_set}
        :return: storage class name for self.volume_spec.file_set
        """
        raise NotImplementedError

    @property
    def pv_name(self) -> str:
        """
        pv name format: qingcloud-gpfs-pv-{file_set}
        :return: pv name for self.volume_spec.file_set
        """
        raise NotImplementedError

    @property
    def pvc_name(self) -> str:
        """
        pvc name format: qingcloud-gpfs-pvc-{file_set}
        :return: pvc name for self.volume_spec.file_set
        """
        raise NotImplementedError

    def has_storage_class(self) -> Union[None, StorageClass]:
        """
        check if storage class exists
        :return: StorageClass object if exists, else None
        """
        try:
            return StorageClass.get(self.storage_class_name)
        except NotFoundError:
            logger.warning(f"not found storage class {self.storage_class_name}")

        return None

    def has_pv(self) -> Union[None, PersistentVolume]:
        """
        check if pv exists
        :return: PersistentVolume object if exists, else None
        """
        try:
            return PersistentVolume.get(self.pv_name)
        except NotFoundError:
            logger.warning(f"not found pv {self.pv_name}")

        return None

    def create_storage(self):
        """
        create storage class, pv, pvc

        :exception FileSetPermissionException: if user has no permission to access fileset

        :param namespace:
        """
        raise NotImplementedError

    def delete_storage(self):
        """
        delete storage class, pv, pvc,
        No need to verify fileset permissions
        :param namespace:
        """
        raise NotImplementedError

    def get_volume_definition(self) -> dict:
        """
        get volumes definitions
        :return:
        """
        raise NotImplementedError

    def get_volume_mounts_definition(self) -> dict:
        """
        get volume mounts definition
        :return:
        """
        raise NotImplementedError

    def change(self):
        """
        Change volume size.
        """
        pass
