import uuid
from functools import lru_cache
from typing import Dict, List, Optional
import re
import base64
import app
from app import logger
from app.core.volumes.public_volume import PublicVolume
from kubernetes import client, config
from kubernetes.config import ConfigException


@lru_cache()
def get_hash_by_mount_path(mount_path: str) -> str:
    """
    根据挂载路径生成hash
    :param mount_path:
    :return:
    """
    return str(uuid.uuid5(uuid.NAMESPACE_OID, mount_path))


def get_volumes_definitions(namespace: str, volumes_specs: list[Dict], code_volume: bool = False,
                            special_volumes_definitions: list[Dict] = None) -> list[Dict]:
    """
    获取volumes的定义
    :param volumes_specs:
    :return:
    """
    # get default volumes tensorboard and code_path
    volumes = [
        {
            "emptyDir": {
                "sizeLimit": "1Gi",
                "medium": "Memory"
            },
            "name": "dshm"
        }
    ]
    if app.settings.PUBLIC_NFS_SERVER:
        volumes.append({
            "name": "public-nfs-pvc",
            "persistentVolumeClaim": {
                "claimName": PublicVolume(namespace).get_pvc()
            }
        })

    for volume_spec in volumes_specs:
        volumes.append(
            {
                "name": get_hash_by_mount_path(volume_spec["mount_path"]),
                "persistentVolumeClaim": {
                    "claimName": volume_spec["pvc_name"]
                }
            }
        )
    if special_volumes_definitions:
        volumes.extend(special_volumes_definitions)
    return volumes


def get_envs_definitions(envs: List[Dict], special_envs: List[Dict] = None) -> list[Dict]:
    default_envs = [
        {
            "name": "TENSORBOARD_LOG_PATH",
            "value": app.settings.TENSORBOARD_MOUNT_PATH
        }
    ]

    if app.settings.NVIDIA_DRIVER_CAPABILITIES:
        default_envs.append({"name": "NVIDIA_DRIVER_CAPABILITIES", "value": app.settings.NVIDIA_DRIVER_CAPABILITIES})

    if special_envs:
        default_envs.extend(special_envs)

    default_envs.extend(envs)
    return default_envs


def get_volume_mounts_definitions(
        code_path_uuid: Optional[uuid.UUID], tensorboard_path_uuid: str, volumes_specs: list[Dict],
        special_volume_mounts_definitions: list[Dict] = None
) -> list[Dict]:
    """
    获取volumeMounts的定义
    :param train_id:
    :param user_id:
    :param namespace:
    :param volumezs_specs:
    :return:
    """
    # get default volumeMounts tensorboard and code_path
    volume_mounts = [
        {
            "mountPath": "/dev/shm",
            "name": "dshm"
        }
    ]
    if app.settings.TENSORBOARD_ENABLE:
        volume_mounts.append(
            {
                "mountPath": app.settings.TENSORBOARD_MOUNT_PATH,
                "name": "default",
                "subPath": tensorboard_path_uuid
            }
        )
    if code_path_uuid:
        volume_mounts.append(
            {
                "mountPath": app.settings.TRAIN_CODE_MOUNT_PATH,
                "name": "default",
                "subPath": str(code_path_uuid)
            }
        )
    if app.settings.PUBLIC_NFS_SERVER:
        volume_mounts.append(
            {
                "mountPath": app.settings.PUBLIC_NFS_IN_POD_PATH,
                "name": "public-nfs-pvc",
            }
        )
    for volume_spec in volumes_specs:
        volume_mounts.append(
            {
                "name": get_hash_by_mount_path(volume_spec["mount_path"]),
                "mountPath": volume_spec["mount_path"]
            }
        )

    if special_volume_mounts_definitions:
        volume_mounts.extend(special_volume_mounts_definitions)

    return volume_mounts


def to_k8s_rfc_1123(name):
    # 删除非允许的字符
    name = re.sub(r'[^a-z0-9-]', '', name.lower())
    # 删除开头和结尾的连字符
    name = re.sub(r'^-|-$', '', name)
    # 确保名字长度不超过63个字符
    name = name[:63]
    return name

def create_k8s_secret(namespace: str, secret_name: str, ssh_public_key: str):
    try:
        config.load_incluster_config()
    except ConfigException:
        config.load_kube_config()

    v1_core = client.CoreV1Api()

    authorized_keys_content = "\n".join([key.key for key in ssh_public_key])
    encoded_authorized_keys = base64.b64encode(authorized_keys_content.encode()).decode()

    # 创建 Secret 对象
    secret = client.V1Secret(
        metadata=client.V1ObjectMeta(name=secret_name, namespace=namespace),
        data={"authorized_keys": encoded_authorized_keys},
        type="Opaque"
    )

    try:
        v1_core.create_namespaced_secret(namespace=namespace, body=secret)
        logger.info("创建 Secret '{%s}' 成功", secret_name)
    except client.exceptions.ApiException as e:
        logger.error("创建 Secret '{%s}' 失", e)
