import random
from typing import Union

from kr8s import NotFoundError
from kr8s.objects import APIObject, PersistentVolumeClaim, PersistentVolume

from app import logger
from app.core.models import QingcloudUser, VolumeSpecBase
from app.core.volumes.base_volume import BaseVolume
from app.core.volumes.exceptions import FileSetPermissionException

import app


class PublicVolume(BaseVolume):
    """NFS volume.

    该卷适用于公共数据的存储目录，为了通用性好，使用nfs
    """

    def __init__(self, user: QingcloudUser, namespace: str, volume_spec: VolumeSpecBase, **kwargs):
        super().__init__(user, namespace, volume_spec, **kwargs)
        self.nfs_server = random.choice(app.settings.PUBLIC_NFS_SERVER.split(","))
        self.nfs_path = app.settings.PUBLIC_NFS_PATH
        if app.settings.PUBLIC_NFS_SERVER:
            self.create_pv()

    @property
    def pv_name(self) -> str:
        """
        pv name format: qingcloud-gpfs-pv-{file_set}
        :return: pv name for self.volume_spec.file_set
        """
        return f"public-nfs-pv-{self.namespace}"

    @property
    def pvc_name(self) -> str:
        """
        pvc name format: qingcloud-gpfs-pvc-{file_set}
        :return: pvc name for self.volume_spec.file_set
        """
        return f"public-nfs-pvc-{self.namespace}"

    def get_pvc(self):
        """
        check if pvc exists
        :param namespace: which namespace to check
        :return: PersistentVolumeClaim object if exists, else None
        """
        try:
            if PersistentVolumeClaim.get(self.pvc_name, namespace=self.namespace):
                return self.pvc_name
        except NotFoundError:
            self.create_pvc()
            logger.warning(f"not found pvc {self.pvc_name}")
            return self.pvc_name

        return None

    def create_storage(self):
        pass

    def create_pv(self):
        try:
            if PersistentVolume.get(self.pv_name):
                return self.pv_name
        except NotFoundError:
            logger.warning(f"not found pv {self.pv_name}")

        logger.debug(f"create pv {self.pv_name}")
        pv: APIObject = PersistentVolume({
            "apiVersion": "v1",
            "kind": "PersistentVolume",
            "metadata": {
                "name": self.pv_name,
            },
            "spec": {
                "capacity": {
                    "storage": "1Gi"
                },
                "accessModes": [
                    "ReadOnlyMany"
                ],
                "nfs": {
                    "server": self.nfs_server,
                    "path": self.nfs_path
                },
                # "storageClassName": self.storage_class_name,
                "volumeMode": "Filesystem"
            }
        }
        )
        pv.create()
        return self.pv_name

    def create_pvc(self) -> APIObject:
        """
        :return: pvc object
        """
        logger.debug(f"create pvc {self.pvc_name}")
        pvc: APIObject = PersistentVolumeClaim({
            "apiVersion": "v1",
            "kind": "PersistentVolumeClaim",
            "metadata": {
                "name": self.pvc_name,
                "namespace": self.namespace
            },
            "spec": {
                "accessModes": [
                    "ReadOnlyMany"
                ],
                "resources": {
                    "requests": {
                        "storage": "1Gi"
                    }
                },
                "volumeName": self.create_pv(),
                "storageClassName": "",
                "volumeMode": "Filesystem"
            }
        }
        )
        pvc.create()
        return pvc

    def get_volume_definition(self):
        return {
            "name": self.pvc_name,
            "persistentVolumeClaim": {
                "claimName": self.get_pvc()
            }
        }

    def get_volume_mounts_definition(self):
        return {
            "mountPath": app.settings.PUBLIC_NFS_IN_POD_PATH,
            "name": self.pvc_name,
        }
