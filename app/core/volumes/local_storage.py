"""
被 zfs_storage 使用
"""

from typing import Union
from kr8s import NotFoundError
from kr8s.objects import APIObject, PersistentVolumeClaim
import app
from app import logger
from app.core.kube.kr8s.asyncio.objects import _ZFSVolume
from app.core.kube.kr8s.objects import ZFSVolume
from app.core.models import QingcloudUser, VolumeSpecBase
from app.core.volumes.base_volume import BaseVolume
from app.core.volumes.utils import to_k8s_rfc_1123


def format_quota(quota: str) -> str:
    """
    format quota
    :param quota: quota string
    :return: quota string
    """
    if not quota:
        raise ValueError("quota is empty")

    unit_map = {
        "MB": "Mi",
        "GB": "Gi",
        "TB": "Ti",
        "PB": "Pi"
    }
    quota_unit = quota[-2:]

    if quota_unit not in unit_map:
        raise ValueError(f"quota unit {quota_unit} not supported")

    return f"{int(quota[:-2])}{unit_map[quota_unit]}"


class LocalStorageVolume(BaseVolume):
    """
    used by notebook local storage
    """

    def __init__(self, user: QingcloudUser, namespace: str, volume_spec: VolumeSpecBase, **kwargs):
        super().__init__(user, namespace, volume_spec, **kwargs)

    @property
    def storage_class_name(self) -> str:
        """
        storage class name format
        :return: storage class name for self.file_set
        """
        return app.settings.LOCAL_STORAGE_CLASS

    @property
    def pvc_name(self) -> str:
        """
        pvc name format: qingcloud-gpfs-pvc-{file_set}
        :return: pvc name for self.volume_spec.file_set
        """
        return f"{app.settings.LOCAL_STORAGE_CLASS}-{to_k8s_rfc_1123(self.volume_spec.file_set)}"

    @property
    def volume_name(self) -> str:
        """
        volume name format: to_k8s_rfc_1123
        :return: volume name for self.volume_spec.file_set
        """
        return f"{app.settings.LOCAL_STORAGE_CLASS}-{to_k8s_rfc_1123(self.volume_spec.file_set)}"

    # def has_storage_class(self) -> Union[None, StorageClass]:
    #     """
    #     check if storage class exists
    #     :return: StorageClass object if exists, else None
    #     """
    #     try:
    #         sc = kr8s.get(StorageClass.endpoint, self.storage_class_name)
    #         if not sc:
    #             raise LocalStorageException(f"not found storage class {self.storage_class_name}")
    #         return sc[0]
    #     except NotFoundError:
    #         logger.warning(f"not found storage class {self.storage_class_name}")
    #         raise LocalStorageException(f"not found storage class {self.storage_class_name}")

    def has_pvc(self) -> Union[None, PersistentVolumeClaim]:
        """
        check if pvc exists
        :param namespace: which namespace to check
        :return: PersistentVolumeClaim object if exists, else None
        """
        try:
            if pvc := PersistentVolumeClaim.get(self.pvc_name, namespace=self.namespace):
                return pvc
        except NotFoundError:
            logger.warning(f"not found pvc {self.pvc_name}")

        return None

    def create_pvc(self) -> APIObject:
        """
        :return: pvc object
        """
        if pvc := self.has_pvc():
            logger.debug(f"pvc {self.pvc_name} already exists")
            return pvc

        logger.debug(f"create pvc {self.pvc_name}")
        pvc_definition = {
            "kind": "PersistentVolumeClaim",
            "apiVersion": "v1",
            "metadata": {
                "name": self.pvc_name,
                "namespace": self.namespace,
            },
            "spec": {
                "storageClassName": self.storage_class_name,
                "accessModes": [
                    "ReadWriteOnce"
                ],
                "resources": {
                    "requests": {
                        "storage": f"{self.volume_spec.quota}Gi"
                    },
                    "limits": {
                        "storage": f"{self.volume_spec.quota}Gi"
                    }
                }
            }
        }

        pvc: APIObject = PersistentVolumeClaim(pvc_definition)
        pvc.create()

        return pvc

    def delete_storage_class(self):
        """
        delete storage class
        """
        logger.info("Not need to delete storage class of local storage(ZFS).")
        pass

    def delete_pvc(self):
        """
        delete pvc
        """
        if pvc := self.has_pvc():
            logger.debug(f"delete pvc {self.pvc_name}")
            pvc.delete()

    def create_storage(self):
        """
        create storage class, pv, pvc

        :exception FileSetPermissionException: if user has no permission to access fileset

        :param namespace:
        """
        logger.debug(f"create storage {self.volume_spec.file_set} for {self.namespace}")
        self.has_storage_class()
        self.create_pvc()

    def compression(self):
        """
        compare zfs storage with volume spec
        :return:
        """
        try:
            if pvc := self.has_pvc():
                logger.info(f"Open ZFS compression for {self.pvc_name}")
                pvc_volume_name = pvc.spec["volumeName"]
                zfs_volume: ZFSVolume = ZFSVolume.get(pvc_volume_name, namespace="openebs")
                zfs_volume.compress_on()
                logger.info(f"Open ZFS compression success for {self.pvc_name}")
        except Exception as e:
            logger.warning(f"Open ZFS compression failed: {e}")

    def delete_storage(self):
        """
        delete storage class, pv, pvc,
        No need to verify fileset permissions
        :param namespace:
        """
        logger.debug(f"delete storage {self.volume_spec.file_set} for {self.namespace}")
        self.delete_pvc()

    def get_volume_definition(self):
        return {
            "name": self.volume_name,
            "persistentVolumeClaim": {
                "claimName": self.pvc_name
            }
        }

    def get_volume_mounts_definition(self):
        return {
            "mountPath": self.volume_spec.mount_path,
            "name": self.volume_name
        }

    def change(self):
        """
        Change volume size.
        """
        if pvc := self.has_pvc():
            if pvc.spec.resources.requests["storage"] == f"{self.volume_spec.quota}Gi":
                logger.info(f"Volume size not changed for {self.pvc_name}")
                return
            pvc.spec.resources.requests["storage"] = f"{self.volume_spec.quota}Gi"
            patches = [
                {
                    "op": "replace",
                    "path": "/spec/resources/requests/storage",
                    "value": f"{self.volume_spec.quota}Gi"
                },
                {
                    "op": "replace",
                    "path": "/spec/resources/limits/storage",
                    "value": f"{self.volume_spec.quota}Gi"
                }
            ]
            pvc.patch(patches, type="json")
            logger.info(f"Change volume size success for {self.pvc_name}")
        else:
            logger.warning(f"Change volume size failed for {self.pvc_name}")
