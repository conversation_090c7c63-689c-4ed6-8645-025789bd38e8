from kr8s.objects import APIObject

# class StorageClass(APIObject):
#     version = "storage.k8s.io/v1"
#     endpoint = "storageclasses"
#     kind = "StorageClass"
#     plural = "storageclasses"
#     singular = "storageclass"
#     namespaced = False

from kr8s.objects import new_class


class StorageClass(
    new_class(
        kind="StorageClass",
        version="storage.k8s.io/v1",
        plural="storageclasses",
        namespaced=False,
        scalable=False,
    )
):
    pass
