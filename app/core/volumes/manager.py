import json
from functools import cached_property
from typing import List, <PERSON><PERSON>

from starlette.authentication import Base<PERSON>ser

from .base_volume import BaseVolume
from .cm import ConfigmapVolume
from .empty_dir import EmptyDirVolume
from .exceptions import DuplicateMountPathException, NotSupportStorageTypeException
from .host_path import HostPathStorageVolume
from .local_storage import LocalStorageVolume
from .local_path_storage import LocalPathStorageVolume
from .manual_pvc import ManualPVCStorageVolume
from .public_volume import PublicVolume
from .qingcloud_gpfs import QingcloudGpfsVolume
from .shared_memory import SharedMemoryVolume
from ..models import QingcloudUser, VolumeSpecBase
from ... import logger


class VolumesManager:
    """
    VolumesManager is a class that manages volumes for a user.
    """
    VolumesMap = {
        "PUBLIC_NFS": PublicVolume, # public model
        "LOCAL": LocalStorageVolume, # ZFS
        # "MINIO": UserMinio,
        "GPFS": QingcloudG<PERSON>sVolume, # QingCloud GPFS
        "LOCAL_PATH": LocalPathStorageVolume,
        "EMPTY_DIR": EmptyDirVolume, # EmptyDir, used by notebook init container
        "SHARED_MEMORY": SharedMemoryVolume, # /dev/shm
        "MANUAL_PVC": ManualPVCStorageVolume, # manual pvc
        "HOST_PATH": HostPathStorageVolume,
        "CM": ConfigmapVolume
    }

    def __init__(self, user: BaseUser, namespace: str, volume_spec: VolumeSpecBase, **kwargs):
        self.user = user
        self.namespace = namespace
        self.volume_spec = volume_spec
        self.kwargs = kwargs

        if self.volume_spec.volume_type not in self.VolumesMap:
            raise NotSupportStorageTypeException(self.volume_spec.volume_type)

        self.volume = self.VolumesMap[self.volume_spec.volume_type](
            self.user, self.namespace, self.volume_spec, **self.kwargs
        )

    def exists(self):
        """
        Check if a storage exists.
        """
        return self.volume.exists

    def create_storage(self):
        """
        Create a storage for a user.
        """
        self.volume.create_storage()

    def delete_storage(self):
        """
        Delete a storage for a user.
        """
        self.volume.delete_storage()

    def get_volume_definition(self):
        """
        Get volume mounts definitions.
        """
        return self.volume.get_volume_definition()

    def get_volume_mounts_definition(self):
        """
        Get volume mounts.
        """
        return self.volume.get_volume_mounts_definition()

    def change(self):
        """
        Change volume size.
        """
        self.volume.change()


class VolumesDefinition:

    def __init__(self, user: QingcloudUser, namespace: str, volume_specs: List[VolumeSpecBase]):
        self.user = user
        self.namespace = namespace
        self.volume_specs = volume_specs

    def exists(self) -> Tuple[bool, str]:
        """
        Check if volumes exists.
        """
        for volume_spec in self.volume_specs:
            try:
                if not VolumesManager(self.user, self.namespace, volume_spec).exists():
                    logger.error(f"volume {volume_spec.volume_type}-{volume_spec.file_set} not exists")
                    return False, f"{volume_spec.file_set}"
            except Exception as e:
                logger.exception(f"check volumes exists error: {e}")
                return False, f"{volume_spec.file_set}"
        return True, "success"

    def get_volumes_definition(self):
        """
        Get volumes definitions.
        """
        volume_set = set()
        volumes_definition = []
        for volume_spec in self.volume_specs:
            volume = VolumesManager(self.user, self.namespace, volume_spec).get_volume_definition()
            if volume["name"] not in volume_set:
                volume_set.add(volume["name"])
                volumes_definition.append(volume)
        return volumes_definition

    def get_volumes_mounts_definition(self):
        """
        Get volumes mounts definitions.
        """
        volume_mounts_definition = []
        volume_mounts_paths = set()
        for volume_spec in self.volume_specs:
            volume_mounts = VolumesManager(self.user, self.namespace, volume_spec).get_volume_mounts_definition()
            if volume_mounts["mountPath"] in volume_mounts_paths:
                raise DuplicateMountPathException(volume_mounts["mountPath"])
            volume_mounts_paths.add(volume_mounts["mountPath"])
            volume_mounts_definition.append(volume_mounts)
        return volume_mounts_definition
