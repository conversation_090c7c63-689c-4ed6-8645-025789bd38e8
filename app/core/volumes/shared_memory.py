"""
被 zfs_storage 使用
"""
from app.core.models import QingcloudUser, VolumeSpecBase
from app.core.volumes.base_volume import BaseVolume
from app.core.volumes.utils import to_k8s_rfc_1123


class SharedMemoryVolume(BaseVolume):
    """
    SharedMemoryVolume for /dev/shm
    """

    def __init__(self, user: QingcloudUser, namespace: str, volume_spec: VolumeSpecBase, **kwargs):
        super().__init__(user, namespace, volume_spec, **kwargs)

    @property
    def pvc_name(self) -> str:
        """
        use md5 hash of file_set as pvc name
        :return:
        """
        return f"shared-memory-{to_k8s_rfc_1123(self.volume_spec.file_set)}"

    @property
    def volume_name(self):
        """
        use md5 hash of file_set as volume name
        :return:
        """
        return f"shared-memory-{to_k8s_rfc_1123(self.volume_spec.file_set)}"

    def create_storage(self):
        """
        create storage class, pv, pvc

        :exception FileSetPermissionException: if user has no permission to access fileset

        :param namespace:
        """
        pass

    def delete_storage(self):
        """
        delete storage class, pv, pvc,
        No need to verify fileset permissions
        :param namespace:
        """
        pass

    def get_volume_definition(self):
        return {
            "name": self.volume_name,
            "emptyDir": {
                "medium": "Memory",
                "sizeLimit": f"{self.volume_spec.quota}Gi",
            }
        }

    def get_volume_mounts_definition(self):
        return {
            "mountPath": "/dev/shm",
            "name": self.volume_name
        }
