"""
The inner minio is deprecated, so we need to remove it from the project.
see https://track.yunify.com/browse/QAI-1896
"""


# import datetime
# import json
# import os
# from typing import BinaryIO, Dict, Union
#
# from kr8s import NotFoundError
# from kr8s.objects import APIObject, PersistentVolume, PersistentVolumeClaim, Secret
# from minio import Minio
# from minio.minioadmin import MinioAdmin
# from minio.credentials import Credentials, Provider
# from minio.error import S3Error
#
# import app
# from app import logger
# from app.core import bmc
# from app.core.models import QingcloudUser, VolumeSpecBase
# from .base_volume import BaseVolume
# from .objects import StorageClass
#
# MINIO_SERVER_ALIAS = app.settings.MINIO_SERVER_ALIAS
#
# class AicpMinioProvider(Provider):
#     """Credential provider from MinIO environment variables."""
#
#     def retrieve(self) -> Credentials:
#         """Retrieve credentials."""
#         return Credentials(
#             access_key=app.settings.MINIO_ACCESS_KEY,
#             secret_key=app.settings.MINIO_SECRET_KEY,
#         )
#
# client = Minio(
#     f"{app.settings.MINIO_HOST}:{app.settings.MINIO_PORT}",
#     access_key=app.settings.MINIO_ACCESS_KEY,
#     secret_key=app.settings.MINIO_SECRET_KEY,
#     secure=app.settings.MINIO_SECURE,
#     cert_check=app.settings.MINIO_CERT_CHECK,
# )
#
# minio_admin_client = MinioAdmin(
#     f"{app.settings.MINIO_HOST}:{app.settings.MINIO_PORT}",
#     credentials=AicpMinioProvider(),
#     secure=app.settings.MINIO_SECURE,
#     cert_check=app.settings.MINIO_CERT_CHECK
# )
#
#
# def generate_secret_key():
#     """
#     generate secret key for user
#     :return:
#     """
#     return "minio-secret-key"
#     # return base64.b64encode(os.urandom(66)).decode("utf-8")
#
#
# class AdminMinio():
#
#     def __init__(self):
#         self.bucket_name = "admin"
#         self.policy_name = "admin"
#
#     def fput_object(self, object_name, file_path):
#         """
#         put object to minio
#         :param object_name:
#         :param file_path:
#         :return:
#         """
#         client.fput_object(self.bucket_name, object_name, file_path)
#
#     def presigned_get_object(self, object_name):
#         return client.presigned_get_object(self.bucket_name, object_name, expires=datetime.timedelta(minutes=30))
#
#     def put_object_and_presigned_get(self, object_name, file_path):
#         try:
#             self.fput_object(object_name, file_path)
#             return self.presigned_get_object(object_name)
#         except Exception as e:
#             logger.error(f"put object {object_name} failed, {e}")
#             raise Exception(f"put object {object_name} failed, {e}")
#         finally:
#             if os.path.exists(file_path):
#                 logger.info(f"remove file {file_path}")
#                 os.remove(file_path)
#
#
# class UserMinio(BaseVolume):
#     """
#     UserMinio for user's minio
#     """
#
#     def __init__(self, user: QingcloudUser, namespace: str, volume_spec: VolumeSpecBase = None, **kwargs):
#         super().__init__(user, namespace, volume_spec, **kwargs)
#         self.namespace = namespace
#         self.group_name = self.namespace
#         self.bucket_name = self.namespace
#         self.policy_name = self.namespace
#         self.user = user
#
#         self.MOUNT_NAME = "default-minio"
#
#     def create_user(self, secret_key) -> (str, str):
#         """
#         create user for minio
#         :return: tuple (user_id, secret_key)
#         """
#         user_info = bmc.admin_user_info(target=MINIO_SERVER_ALIAS, username=self.user.user_id, )
#         if user_info.status == "success":
#             logger.info(f"user {self.user.user_id} already exists")
#             return self.user.user_id, secret_key
#
#         logger.info(f"create user {self.user.user_id}")
#         r = bmc.admin_user_add(target=MINIO_SERVER_ALIAS, username=self.user.user_id, password=secret_key)
#         if r.status != "success":
#             logger.error(f"create user {self.user.user_id} failed, {r.content}")
#             raise Exception(f"create user {self.user.user_id} failed")
#         return self.user.user_id, secret_key
#
#     def create_group(self):
#         """
#         create group for minio
#         :return: group name
#         """
#         group_info = bmc.admin_group_info(target=MINIO_SERVER_ALIAS, group=self.group_name)
#         if group_info.status == "success":
#             logger.info(f"group {self.group_name} already exists")
#             return self.group_name
#
#         logger.info(f"create group {self.namespace}")
#         r = bmc.admin_group_add(target=MINIO_SERVER_ALIAS, group=self.group_name, members=[self.user.user_id])
#         if r.status != "success":
#             logger.error(f"create group {self.namespace} failed, {r.content}")
#             raise Exception(f"create group {self.namespace} failed")
#         return self.namespace
#
#     def create_policy(self):
#         """
#         create policy for minio
#         group policy has permission to get {self.namespace} buckets
#         :return:
#         """
#         policy_info = bmc.admin_policy_info(target=MINIO_SERVER_ALIAS, name=self.policy_name)
#         if policy_info.status == "success":
#             logger.info(f"policy {self.policy_name} already exists")
#             return self.policy_name
#
#         logger.info(f"create policy {self.policy_name}")
#         policy_name = self.namespace
#         policy = {
#             "Version": "2012-10-17",
#             "Statement": [
#                 {
#                     "Effect": "Allow",
#                     "Action": [
#                         "s3:ListAllMyBuckets",
#                         "s3:DeleteObject",
#                         "s3:GetObject",
#                         "s3:PutObject",
#                         "s3:ListBucket",
#                     ],
#                     "Resource": [
#                         f"arn:aws:s3:::{self.namespace}/*"
#                     ]
#                 }
#             ]
#         }
#         policy_f = os.path.join(app.settings.MINIO_POLICY_TMP_DIR, f"{self.namespace}.{policy['Version']}.json")
#         with open(policy_f, "w", encoding="utf-8") as f:
#             f.write(json.dumps(policy))
#
#         try:
#             r = bmc.admin_policy_create(target=MINIO_SERVER_ALIAS, name=policy_name, file=policy_f)
#             if r.status != "success":
#                 logger.error(f"create group {self.namespace} failed, {r.content}")
#                 raise Exception(f"create group {self.namespace} failed")
#         except S3Error as e:
#             logger.error(f"create group {self.namespace} failed, {e}")
#             raise Exception(f"create group {self.namespace} failed, {e}")
#         finally:
#             os.remove(policy_f)
#
#         return policy_name
#
#     def attach_policy_for_group(self):
#         """
#         attach policy to group
#         :return:
#         """
#         group_info = bmc.admin_group_info(target=MINIO_SERVER_ALIAS, group=self.group_name)
#         if group_info.status == "success" and "groupPolicy" in group_info.content:
#             logger.info(f"policy {self.policy_name} already attached to group {self.group_name}")
#             return
#
#         logger.info(f"attach policy {self.policy_name} to group {self.group_name}")
#         r = bmc.admin_policy_attach(target=MINIO_SERVER_ALIAS, name=self.policy_name, group=self.group_name)
#         if r.status != "success":
#             logger.error(f"create group {self.namespace} failed, {r.content}")
#             raise Exception(f"create group {self.namespace} failed")
#
#     def get_quota_info(self) -> Dict:
#         """
#         get bucket quota
#         :return:
#         """
#         return json.loads(minio_admin_client.bucket_quota_get(self.bucket_name))
#
#     def set_quota(self, new_quota: int):
#         """
#
#         :param new_quota: unit: GB, transfer to bytes
#         """
#         new_quota = new_quota * 1024 ** 3
#         quota_info = self.get_quota_info()
#         now_quota = quota_info["quota"]
#         if new_quota == now_quota:
#             return
#         minio_admin_client.bucket_quota_set(self.bucket_name, new_quota)
#
#     def create(self, secret_key) -> None:
#         self.create_bucket()
#         ak, sk = self.create_user(secret_key)
#         group_name = self.create_group()
#         policy_name = self.create_policy()
#         self.attach_policy_for_group()
#         self.create_storage(secret_key, create=True)
#         self.set_quota(app.settings.MINIO_DEFAULT_HARD_LIMIT)
#
#     def create_bucket(self) -> None:
#         if not client.bucket_exists(self.bucket_name):
#             client.make_bucket(self.bucket_name)
#
#     def presigned_put_object(self, object_name):
#         ppo = client.presigned_put_object(self.bucket_name, object_name, expires=datetime.timedelta(minutes=10))
#         return ppo
#
#     def fput_object(self, object_name, file_path):
#         """
#         put object to minio
#         :param object_name:
#         :param file_path:
#         :return:
#         """
#         client.fput_object(self.bucket_name, object_name, file_path)
#         client.presigned_put_object(bucket_name="s", object_name="x", )
#
#     def put_object(self, object_name: str, file: BinaryIO):
#         file_length = file.seek(0, os.SEEK_END)
#         file.tell()
#         put_r = client.put_object(self.bucket_name, object_name, file, file_length)
#         logger.info(f"put object {object_name} to {self.bucket_name}, {put_r.location}")
#
#     @property
#     def secret_name(self) -> str:
#         """
#         secret name format: aicp-oss-secret-{file_set}
#         :return: secret name for self.file_set
#         """
#         return f"aicp-oss-secret-{self.namespace}"
#
#     @property
#     def storage_class_name(self) -> str:
#         """
#         storage class name format: aicp-oss-storage-class-{file_set}
#         :return: storage class name for self.file_set
#         """
#         return f"aicp-oss-storage-class-{self.namespace}"
#
#     @property
#     def pv_name(self) -> str:
#         """
#         pv name format: aicp-oss-pv-{file_set}
#         :return: pv name for self.file_set
#         """
#         return f"aicp-oss-pv-{self.namespace}"
#
#     @property
#     def pvc_name(self) -> str:
#         """
#         pvc name format: aicp-oss-pvc-{file_set}
#         :return: pvc name for self.file_set
#         """
#         return f"aicp-oss-pvc-{self.namespace}"
#
#     @property
#     def volume_name(self) -> str:
#         """
#         volume name format: aicp-oss-{file_set}
#         :return: volume name for self.file_set
#         """
#         return f"aicp-oss-{self.namespace}"
#
#     @staticmethod
#     def get_pvc_name(namespace) -> str:
#         """
#         pvc name format: aicp-oss-pvc-{file_set}
#         :param namespace:
#         :return:
#         """
#         return f"aicp-oss-pvc-{namespace}"
#
#     def has_pv(self, namespace) -> Union[None, PersistentVolume]:
#         """
#         check if pv exists
#         :param namespace: which namespace to check
#         :return: PersistentVolume object if exists, else None
#         """
#         try:
#             return PersistentVolume.get(self.pv_name, namespace=namespace)
#         except NotFoundError:
#             logger.warning(f"not found pv {self.pv_name}")
#
#         return None
#
#     def has_pvc(self, namespace) -> Union[None, PersistentVolumeClaim]:
#         """
#         check if pvc exists
#         :param namespace: which namespace to check
#         :return: PersistentVolumeClaim object if exists, else None
#         """
#         try:
#             return PersistentVolumeClaim.get(self.pvc_name, namespace=namespace)
#         except NotFoundError:
#             logger.warning(f"not found pvc {self.pvc_name}")
#
#         return None
#
#     def has_secret(self, namespace) -> Union[None, Secret]:
#         """
#         check if secret exists
#         :param namespace: which namespace to check
#         :return: Secret object if exists, else None
#         """
#         try:
#             return Secret.get(self.secret_name, namespace=namespace)
#         except NotFoundError:
#             logger.warning(f"not found secret {self.secret_name}")
#
#         return None
#
#     def create_secret(self, secret_access_key) -> APIObject:
#         """
#
#         :param secret_access_key:
#         :return:
#         """
#         if secret := self.has_secret(self.namespace):
#             logger.debug(f"secret {self.secret_name} already exists")
#             return secret
#
#         logger.debug(f"create secret {self.secret_name}")
#         secret: APIObject = Secret({
#             "apiVersion": "v1",
#             "kind": "Secret",
#             "metadata": {
#                 "namespace": self.namespace,
#                 "name": self.secret_name
#             },
#             "stringData": {
#                 "accessKeyID": self.user.user_id,
#                 "secretAccessKey": secret_access_key,
#                 "endpoint": app.settings.MINIO_SC_ENDPOINT
#             }
#         })
#         secret.create()
#         return secret
#
#     def create_storage_class(self) -> APIObject:
#         """
#         create storage class by self.file_set,
#         storage class name format: self.storage_class_name
#
#         :return: sc object
#         """
#         if sc := self.has_storage_class():
#             logger.debug(f"sotrage class {self.storage_class_name} already exists")
#             return sc
#
#         logger.debug(f"create storage class {self.storage_class_name}")
#         sc: APIObject = StorageClass({
#             "kind": "StorageClass",
#             "apiVersion": "storage.k8s.io/v1",
#             "metadata": {
#                 "name": self.storage_class_name
#             },
#             "provisioner": "ru.yandex.s3.csi",
#             "reclaimPolicy": "Retain",
#             "parameters": {
#                 "mounter": "geesefs",
#                 "bucket": self.bucket_name,
#                 "options": "--memory-limit 1000 --dir-mode 0777 --file-mode 0666",
#                 "csi.storage.k8s.io/provisioner-secret-name": self.secret_name,
#                 "csi.storage.k8s.io/provisioner-secret-namespace": self.namespace,
#                 "csi.storage.k8s.io/controller-publish-secret-name": self.secret_name,
#                 "csi.storage.k8s.io/controller-publish-secret-namespace": self.namespace,
#                 "csi.storage.k8s.io/node-stage-secret-name": self.secret_name,
#                 "csi.storage.k8s.io/node-stage-secret-namespace": self.namespace,
#                 "csi.storage.k8s.io/node-publish-secret-name": self.secret_name,
#                 "csi.storage.k8s.io/node-publish-secret-namespace": self.namespace
#             }
#         })
#         sc.create()
#         return sc
#
#     def create_pv(self, namespace: str) -> APIObject:
#         """
#         create pv by self.file_set,
#         pv name format: self.pv_name
#
#         :param namespace: which namespace to create pv
#
#         :return: pv object
#         """
#         if pv := self.has_pv(namespace):
#             logger.debug(f"pv {self.pv_name} already exists")
#             return pv
#
#         logger.debug(f"create pv {self.pv_name}")
#         pv: APIObject = PersistentVolume({
#             "apiVersion": "v1",
#             "kind": "PersistentVolume",
#             "metadata": {
#                 "name": self.pv_name,
#                 "namespace": self.namespace
#             },
#             "spec": {
#                 "storageClassName": self.storage_class_name,
#                 "capacity": {
#                     "storage": app.settings.DEFAULT_VOLUMES_CAPACITY
#                 },
#                 "accessModes": [
#                     "ReadWriteMany"
#                 ],
#                 "claimRef": {
#                     "namespace": self.namespace,
#                     "name": self.pvc_name
#                 },
#                 "csi": {
#                     "driver": "ru.yandex.s3.csi",
#                     "controllerPublishSecretRef": {
#                         "name": self.secret_name,
#                         "namespace": self.namespace
#                     },
#                     "nodePublishSecretRef": {
#                         "name": self.secret_name,
#                         "namespace": self.namespace
#                     },
#                     "nodeStageSecretRef": {
#                         "name": self.secret_name,
#                         "namespace": self.namespace
#                     },
#                     "volumeAttributes": {
#                         "capacity": app.settings.DEFAULT_VOLUMES_CAPACITY,
#                         "mounter": "geesefs",
#                         "options": "--memory-limit 1000 --dir-mode 0777 --file-mode 0666"
#                     },
#                     "volumeHandle": self.bucket_name
#                 }
#             }
#         })
#         pv.create()
#         return pv
#
#     def create_pvc(self, namespace: str) -> APIObject:
#         """
#         :return: pvc object
#         """
#         if pvc := self.has_pvc(namespace):
#             logger.debug(f"pvc {self.pvc_name} already exists")
#             return pvc
#
#         logger.debug(f"create pvc {self.pvc_name}")
#         pvc: APIObject = PersistentVolumeClaim({
#             "apiVersion": "v1",
#             "kind": "PersistentVolumeClaim",
#             "metadata": {
#                 "name": self.pvc_name,
#                 "namespace": self.namespace
#             },
#             "spec": {
#                 "storageClassName": "",
#                 "volumeName": "",
#                 "accessModes": [
#                     "ReadWriteMany"
#                 ],
#                 "resources": {
#                     "requests": {
#                         "storage": app.settings.DEFAULT_VOLUMES_CAPACITY
#                     }
#                 }
#             }
#         })
#         pvc.create()
#         return pvc
#
#     def delete_storage_class(self):
#         """
#         delete storage class
#         """
#         if sc := self.has_storage_class():
#             logger.debug(f"delete storage class {self.storage_class_name}")
#             sc.delete()
#
#     def delete_pv(self, namespace: str):
#         """
#         delete pv
#         """
#         if pv := self.has_pv(namespace):
#             logger.debug(f"delete pv {self.pv_name}")
#             pv.delete()
#
#     def delete_pvc(self, namespace: str):
#         """
#         delete pvc
#         """
#         if pvc := self.has_pvc(namespace):
#             logger.debug(f"delete pvc {self.pvc_name}")
#             pvc.delete()
#
#     def create_storage(self, secret_key: str = "", create=False):
#         """
#         create storage class, pv, pvc
#
#         :exception FileSetPermissionException: if user has no permission to access fileset
#
#         :param namespace:
#         """
#         self.create_secret(secret_key)
#         self.create_storage_class()
#         self.create_pv(self.namespace)
#         self.create_pvc(self.namespace)
#
#     def delete_storage(self):
#         """
#         delete storage class, pv, pvc,
#         No need to verify fileset permissions
#         :param namespace:
#         """
#         self.delete_pvc(self.namespace)
#         self.delete_pv(self.namespace)
#         self.delete_storage_class()
#
#     def get_default_pvc(self):
#         """
#         获取默认的code_path pvc
#         用于默认的 code_path logs 输出
#         :return:
#         """
#         # create_namespace_code_path_pvc(namespace)
#         return [{
#             "name": self.MOUNT_NAME,
#             "persistentVolumeClaim": {
#                 "claimName": self.pvc_name
#             }
#         }]
#
#     def get_default_code_mount(self, code_path_uuid: str, tensorboard_path_uuid: str):
#         """
#         获取默认的code_path pvc
#         用于默认的 code_path logs 输出
#         :return:
#         """
#         # create_namespace_code_path_pvc(namespace)
#         return [
#             {
#                 "mountPath": app.settings.TRAIN_CODE_MOUNT_PATH,
#                 "name": self.MOUNT_NAME,
#                 "subPath": code_path_uuid
#             },
#             {
#                 "mountPath": app.settings.TENSORBOARD_MOUNT_PATH,
#                 "name": self.MOUNT_NAME,
#                 "subPath": tensorboard_path_uuid
#             }
#         ]
#
#     def get_volume_definition(self):
#         return {
#             "name": self.volume_name,
#             "persistentVolumeClaim": {
#                 "claimName": self.pvc_name
#             }
#         }
#
#     def get_volume_mounts_definition(self):
#         return {
#             "mountPath": self.volume_spec.mount_path,
#             "subPath": self.volume_spec.file_set,
#             "name": self.volume_name
#         }
