from app.core.exceptions import PermissionDeniedException, AICPBaseException


class GetGpfsFilesetException(AICPBaseException):
    """
    get gpfs fileset error
    """

    BASE_CODE = 2401
    BASE_MESSAGE = "获取gpfs fileset失败"


class FileSetPermissionException(PermissionDeniedException):
    """
    fileset permission error
    """

    BASE_CODE = 2403
    BASE_MESSAGE = "无权限访问fileset"

class LocalStorageException(AICPBaseException):
    """
    local storage exception
    """

    BASE_CODE = 2404
    BASE_MESSAGE = "本地存储异常"

class NotSupportStorageTypeException(AICPBaseException):
    """
    not support storage type exception
    """

    BASE_CODE = 2405
    BASE_MESSAGE = "不支持的存储类型"

class DuplicateMountPathException(AICPBaseException):
    """
    not support storage type exception
    """

    BASE_CODE = 2405
    BASE_MESSAGE = "重复的挂载路径"