import string
from enum import Enum

BM_GROUP = "kubemetal.qingcloud.com"
BM_VERSION = "v1alpha1"
BM_KIND = "instances"



NODE_TAG_LOCK = "node_tag_lock"
BM_TAG_LOCK = "bm_node"

STATUS_PENDING = 'pending'
STATUS_ACTIVE = 'active'
STATUS_UNACTIVE = 'unactive'
STATUS_DETELE = 'delete'
STATUS_TERMINATED = "Terminated"
STATUS_CEASED = "ceased"
STATUS_SUSPENDED = "Suspended"

Reason_Suspend_by_billing = "欠费暂停"
STATUS_SUCCESS = "success"


USER_ROLE_ADMIN = "global_admin"

# 专属资源组标签key
RG_TAG_KEY = "aicp.group/resource_group"

# 专属资源组污点key
RG_TOLERATION_KEY = "aicp.group/resource_group"

# 专属资源组节点标签key aicp.group/rgn_id
RG_NODE_TAG_KEY = "aicp.group/resource_group_node"
BM_NODE_TAG_KEY = "aicp.group/bm_node"

RG_NODE_TOLERATION_KEY = "aicp.group/resource_group_node"

RG_NODE_TOLERATION_KEY_SUSPENDED = "aicp.group/resource_group_node_suspended"

SKU_TAG = "aicp.group.sku.%s"

AIPODS_TYPE = "aicp.group/aipods_type"
COMPUTE_GROUP = "aicp.group/compute_group"

# 切分annotations
HAMI_DCU = "hami.io/node-dcu-register"
HAMI_ASCEND = "hami.io/node-ascend-register"
HAMI_NVDIA = "hami.io/node-nvidia-register"

# K8S node status K8S节点服务状态
READY = "Ready"
NOT_READY = "NotReady"
UNREACHABLE = "Unreachable"
OUT_OF_DISK = "OutOfDisk"
MEMORY_PRESSURE = "MemoryPressure"
DISK_PRESSURE = "DiskPressure"
NETWORK_UNAVAILABLE = "NetworkUnavailable"

# hyper ndoe status  节点物理状态
ACTIVE = "active"
UNACTIVE = "unactive"

# 资源类型
RESOURCE_NOTEBOOK = "notebook"
RESOURCE_TRAIN = "train"
RESOURCE_IMAGE = "image"
RESOURCE_BM = "bm"

UNLIMITED = 10000

# gpu厂商
NVIDIA = "nvidia"
HUAWEI = "huawei"
HEXAFLAKE = "hexaflake"
HYGON = "hygon"

# 分布式训练任务优先级
PRIORITY_CLASSES_JOBS = [
    "job-first",
    "job-second",
    "job-third",
    "job-fourth",
    "job-fifth",
    "job-sixth",
    "job-seventh",
    "job-eighth",
    "job-ninth",
    "job-tenth"
]

# 更换源需要执行的命令
SOURCE_COMMAND = {
    "pip-pypi": ["/bin/sh", "-c",
                 "mkdir -p /root/.pip && [ -e /root/.pip/pip.conf ] && cp /root/.pip/pip.conf /root/.pip/pip.conf.bak ; printf \"[global_server]\nindex-url = https://pypi.org/simple\ntrusted-host = pypi.org\n\" > /root/.pip/pip.conf"],
    "pip-aliyun": ["/bin/sh", "-c",
                   "mkdir -p /root/.pip && [ -e /root/.pip/pip.conf ] && cp /root/.pip/pip.conf /root/.pip/pip.conf.bak ; printf \"[global_server]\nindex-url = https://mirrors.aliyun.com/pypi/simple\ntrusted-host = mirrors.aliyun.com\n\" > /root/.pip/pip.conf"],
    "pip-tsinghua": ["/bin/sh", "-c",
                     "mkdir -p /root/.pip && [ -e /root/.pip/pip.conf ] && cp /root/.pip/pip.conf /root/.pip/pip.conf.bak ; printf \"[global_server]\nindex-url = https://pypi.tuna.tsinghua.edu.cn/simple\ntrusted-host = pypi.tuna.tsinghua.edu.cn\n\" > /root/.pip/pip.conf"],
    "pip-huaweicloud": ["/bin/sh", "-c",
                        "mkdir -p /root/.pip && [ -e /root/.pip/pip.conf ] && cp /root/.pip/pip.conf /root/.pip/pip.conf.bak ; printf \"[global_server]\nindex-url = https://mirrors.huaweicloud.com/repository/pypi/simple\ntrusted-host = mirrors.huaweicloud.com\n\" > /root/.pip/pip.conf"],

    "conda-conda": ["/bin/sh", "-c",
                    "[ -e /root/.condarc ] && cp /root/.condarc /root/.condarc.bak ; echo \"\" > /root/.condarc > /root/.condarc"],
    "conda-aliyun": ["/bin/sh", "-c",
                     "[ -e /root/.condarc ] && cp /root/.condarc /root/.condarc.bak ; printf \"channels:\n  - defaults\nshow_channel_urls: true\ndefault_channels:\n  - http://mirrors.aliyun.com/anaconda/pkgs/main\n  - http://mirrors.aliyun.com/anaconda/pkgs/r\n  - http://mirrors.aliyun.com/anaconda/pkgs/msys2\ncustom_channels:\n  conda-forge: http://mirrors.aliyun.com/anaconda/cloud\n  msys2: http://mirrors.aliyun.com/anaconda/cloud\n  bioconda: http://mirrors.aliyun.com/anaconda/cloud\n  menpo: http://mirrors.aliyun.com/anaconda/cloud\n  pytorch: http://mirrors.aliyun.com/anaconda/cloud\n  simpleitk: http://mirrors.aliyun.com/anaconda/cloud\n\" > /root/.condarc"],
    "conda-tsinghua": ["/bin/sh", "-c",
                       "[ -e /root/.condarc ] && cp /root/.condarc /root/.condarc.bak ; printf \"channels:\n  - defaults\nshow_channel_urls: true\ndefault_channels:\n  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/main\n  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/r\n  - https://mirrors.tuna.tsinghua.edu.cn/anaconda/pkgs/msys2\ncustom_channels:\n  conda-forge: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n  msys2: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n  bioconda: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n  menpo: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n  pytorch: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n  pytorch-lts: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n  simpleitk: https://mirrors.tuna.tsinghua.edu.cn/anaconda/cloud\n\" > /root/.condarc"],

    "apt-ubuntu": ["/bin/sh", "-c",
                   "mkdir -p /etc/apt && codename=$(lsb_release -c | awk '{print $2}') && [ -e /etc/apt/sources.list ] && cp /etc/apt/sources.list /etc/apt/sources.list.bak ; printf \"deb http://archive.ubuntu.com/ubuntu/ %s main restricted\ndeb http://archive.ubuntu.com/ubuntu/ %s-updates main restricted\ndeb http://archive.ubuntu.com/ubuntu/ %s universe\ndeb http://archive.ubuntu.com/ubuntu/ %s-updates universe\ndeb http://archive.ubuntu.com/ubuntu/ %s multiverse\ndeb http://archive.ubuntu.com/ubuntu/ %s-updates multiverse\ndeb http://archive.ubuntu.com/ubuntu/ %s-backports main restricted universe multiverse\ndeb http://security.ubuntu.com/ubuntu/ %s-security main restricted\ndeb http://security.ubuntu.com/ubuntu/ %s-security universe\ndeb http://security.ubuntu.com/ubuntu/ %s-security multiverse\n\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" > /etc/apt/sources.list"],
    "apt-aliyun": ["/bin/sh", "-c",
                   "mkdir -p /etc/apt && codename=$(lsb_release -c | awk '{print $2}') && [ -e /etc/apt/sources.list ] && cp /etc/apt/sources.list /etc/apt/sources.list.bak ; printf \"deb http://mirrors.aliyun.com/ubuntu/ %s main restricted universe multiverse\ndeb-src http://mirrors.aliyun.com/ubuntu/ %s main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ %s-security main restricted universe multiverse\ndeb-src http://mirrors.aliyun.com/ubuntu/ %s-security main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ %s-updates main restricted universe multiverse\ndeb-src http://mirrors.aliyun.com/ubuntu/ %s-updates main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ %s-backports main restricted universe multiverse\ndeb-src http://mirrors.aliyun.com/ubuntu/ %s-backports main restricted universe multiverse\ndeb http://mirrors.aliyun.com/ubuntu/ %s-proposed main restricted universe multiverse\ndeb-src http://mirrors.aliyun.com/ubuntu/ %s-proposed main restricted universe multiverse\n\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" > /etc/apt/sources.list"],
    "apt-tsinghua": ["/bin/sh", "-c",
                     "mkdir -p /etc/apt && codename=$(lsb_release -c | awk '{print $2}') && [ -e /etc/apt/sources.list ] && cp /etc/apt/sources.list /etc/apt/sources.list.bak ; printf \"deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s main restricted universe multiverse\n# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s main restricted universe multiverse\ndeb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-updates main restricted universe multiverse\n# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-updates main restricted universe multiverse\ndeb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-backports main restricted universe multiverse\n# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-backports main restricted universe multiverse\ndeb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-security main restricted universe multiverse\n# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-security main restricted universe multiverse\n# deb https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-proposed main restricted universe multiverse\n# deb-src https://mirrors.tuna.tsinghua.edu.cn/ubuntu/ %s-proposed main restricted universe multiverse\n\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" > /etc/apt/sources.list"],
    "apt-huaweicloud": ["/bin/sh", "-c",
                        "mkdir -p /etc/apt && codename=$(lsb_release -c | awk '{print $2}') && [ -e /etc/apt/sources.list ] && cp /etc/apt/sources.list /etc/apt/sources.list.bak ; printf \"deb https://repo.huaweicloud.com/ubuntu/ %s main multiverse restricted universe\ndeb https://repo.huaweicloud.com/ubuntu/ %s-backports main multiverse restricted universe\ndeb https://repo.huaweicloud.com/ubuntu/ %s-proposed main multiverse restricted universe\ndeb https://repo.huaweicloud.com/ubuntu/ %s-security main multiverse restricted universe\ndeb https://repo.huaweicloud.com/ubuntu/ %s-updates main multiverse restricted universe\ndeb-src https://repo.huaweicloud.com/ubuntu/ %s main multiverse restricted universe\ndeb-src https://repo.huaweicloud.com/ubuntu/ %s-backports main multiverse restricted universe\ndeb-src https://repo.huaweicloud.com/ubuntu/ %s-proposed main multiverse restricted universe\ndeb-src https://repo.huaweicloud.com/ubuntu/ %s-security main multiverse restricted universe\ndeb-src https://repo.huaweicloud.com/ubuntu/ %s-updates main multiverse restricted universe\n\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" \"$codename\" > /etc/apt/sources.list"]
}


class UserFrom(str, Enum):
    """
    用户来源
    """
    DEBUG = "debug"
    KSE = "kse"
    CONSOLE = "console"
    SDK = "sdk"
    API = "api"


class EventReason(str, Enum):
    # 计费调用失败
    InvokeBillingFailed = "InvokeBillingFailed"
    # 对账异常
    CheckAccountsError = "CheckAccountsError"


TRAINNING_OPERATOR_COMMAND = {
    "volcano-enable": ['/manager', '--gang-scheduler-name=volcano', '--mpi-kubectl-delivery-image',
                       'hub.kubesphere.com.cn/public/kubectl-delivery:latest', '--pytorch-init-container-max-tries',
                       '1800', '--pytorch-init-container-image', 'hub.kubesphere.com.cn/public/alpine:3.10'],
    "volcano-disable": ['/manager', '--mpi-kubectl-delivery-image',
                        'hub.kubesphere.com.cn/public/kubectl-delivery:latest', '--pytorch-init-container-max-tries',
                        '1800', '--pytorch-init-container-image', 'hub.kubesphere.com.cn/public/alpine:3.10']
}


class AICPType(str, Enum):
    AICPWebApp = "aicp-web-app"
    AICPUpdateJob = "update-job"

PASSWORD_CHARS = string.ascii_letters + string.digits + string.punctuation

# queue
OPERATION_INVOKE_EVENT_QUEUE = 'operation_invoke_event_queue'
API_EVENT_QUEUE_MAX_LEN = 1000

# resource
BILLING_RESOURCE_PFRFIX = "qai-"

NOTEBOOK_PRE_IMAGE_INFO_KEY = "kubeflow-resource-pre-image-info"

NOTEBOOK_USER_VOLUME_TYPES = {"GPFS", "MANUAL_PVC", "HOST_PATH"}
