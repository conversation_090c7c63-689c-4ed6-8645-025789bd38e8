import time
from typing import List

import redis as redis
from app import logger
import app


class RedisClient(object):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(RedisClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        password = app.settings.REDIS_PASSWORD if app.settings.REDIS_PASSWORD else None
        self.conn = redis.StrictRedis(host=app.settings.REDIS_HOST, port=app.settings.REDIS_PORT,
                                      password=password, decode_responses=True)

    def set(self, key, value, nx=True, ex=10, xx=False):
        try:
            return self.conn.set(key, value, nx=nx, ex=ex, xx=xx)
        except Exception as e:
            logger.error(f"set cache failed: {e}")

    def get(self, key):
        try:
            return self.conn.get(key)
        except Exception as e:
            logger.error("get cache failed")
            return None

    def mget(self, keys: List[str]):
        try:
            return self.conn.mget(keys)
        except Exception as e:
            logger.error("get cache failed")
            return None

    def delete(self, key):
        return self.conn.delete(key)

    def lock(self, _id):
        return self.conn.lock(_id)


class Rlock(object):

    def __init__(self, rlock_key, timeout=60, retry_get=True):
        self.rlock_key = rlock_key
        self.rlock_v = rlock_key
        self.timeout = timeout
        self.retry_get = retry_get

    def __enter__(self):
        if not self.retry_get:
            if RedisClient().set(self.rlock_key, self.rlock_v, ex=self.timeout):
                return self
            else:
                raise Exception("GET LOCK FAILED")
        retry = 60
        while retry > 0:
            if RedisClient().set(self.rlock_key, self.rlock_v, ex=self.timeout):
                break
            else:
                logger.warning("get lock failed, retry...")
                time.sleep(1)
                retry = retry - 1
            # 在进入代码块时执行的操作
        if retry <= 0:
            raise Exception("GET LOCK FAILED")
        return self

    def __exit__(self, exc_type, exc_value, traceback):
        RedisClient().delete(self.rlock_key)
