from typing import Dict

from kubernetes import client, config
from kubernetes.client import ApiException
from kubernetes.config import ConfigException

from app.core.constant import PRIORITY_CLASSES_JOBS


def get_priorityclasses_definition(name: str, value: int) -> Dict:
    # priorityclasses.scheduling.k8s.io
    priority_class = {
        "apiVersion": "scheduling.k8s.io/v1",
        "kind": "PriorityClass",
        "metadata": {
            "name": name
        },
        "value": value
    }
    return priority_class


def init_priorityclasses():
    try:
        config.load_incluster_config()
    except ConfigException:
        config.load_kube_config()
    v1 = client.SchedulingV1Api()
    k8s_exist_priorities = v1.list_priority_class()
    exist_priorities = []
    for k8s_exist_priority in k8s_exist_priorities.items:
        # 记录存在的priorityclasses
        if k8s_exist_priority.metadata.name in PRIORITY_CLASSES_JOBS:
            exist_priorities.append(k8s_exist_priority.metadata.name)
    if set(exist_priorities) == set(PRIORITY_CLASSES_JOBS):
        return
    # 创建不存在的priorityclasses
    for index, priority_classes_job in enumerate(PRIORITY_CLASSES_JOBS, start=1):
        if priority_classes_job in exist_priorities:
            continue
        priority_resource = get_priorityclasses_definition(priority_classes_job, index)
        try:
            v1.create_priority_class(body=priority_resource)
            print(f"Priority class {priority_classes_job} created successfully.")
        except ApiException as e:
            print(f"Priority class {priority_classes_job} creation failed.")

