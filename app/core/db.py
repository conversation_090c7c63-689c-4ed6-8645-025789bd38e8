from collections.abc import Iterable
from contextlib import asynccontextmanager, contextmanager

from sqlalchemy import create_engine
from sqlalchemy.ext.asyncio import create_async_engine
from sqlmodel import SQLModel, Session
from sqlmodel.ext.asyncio.session import AsyncSession

import app
from app import logger

engine = create_engine(
    app.settings.DB_CONNECTION_STR,
    pool_size=app.settings.DB_POOL_SIZE,
    max_overflow=app.settings.DB_MAX_OVERFLOW,
    pool_recycle=app.settings.DB_POOL_RECYCLE,
    pool_pre_ping=app.settings.DB_POOL_PRE_PING,
    echo=app.settings.ECHO,
    future=True
)


def get_db_session() -> Session:
    with Session(engine, expire_on_commit=False) as session:
        yield session
        session.commit()
        logger.debug("Session (get_db_session) committed, and closed.")

def get_db_session_local() -> Session:
    session = Session(engine)
    return session


@contextmanager
def get_db_context_session(expire_on_commit=True) -> Session:
    with Session(engine, expire_on_commit=expire_on_commit) as session:
        yield session
        session.commit()
        logger.debug("Session (get_db_context_session) committed, and closed.")


async_engine = create_async_engine(
    app.settings.DB_ASYNC_CONNECTION_STR,
    pool_size=app.settings.DB_POOL_SIZE,
    max_overflow=app.settings.DB_MAX_OVERFLOW,
    pool_recycle=app.settings.DB_POOL_RECYCLE,
    pool_pre_ping=app.settings.DB_POOL_PRE_PING,
    echo=app.settings.ECHO,
    future=True
)


@asynccontextmanager
async def get_async_db_session(expire_on_commit=True) -> AsyncSession:
    async with AsyncSession(async_engine, expire_on_commit=expire_on_commit) as session:
        yield session
        await session.commit()
        logger.debug("AsyncSession committed, and closed.")


def session_scope(func):
    """
    inject session scop to function
    :param func:
    :return:
    """

    def wrapper(*args, **kwargs):
        if kwargs.get("session_") is not None:
            return func(*args, **kwargs)

        with get_db_context_session(expire_on_commit=False) as session:
            kwargs["session_"] = session
            return func(*args, **kwargs)

    return wrapper


def load_eager_fields(func):
    def wrapper(*args, **kwargs):
        eager_load = kwargs.pop("eager_load", True)
        obj = func(*args, **kwargs)
        if eager_load:
            if isinstance(obj, SQLModel):
                objs = [obj]
            else:
                objs = obj
            if not isinstance(obj, Iterable):
                logger.warning(f"eager_load: obj is not Iterable")
                return

            for cls in objs:
                for field in getattr(cls, "__eager__fields__", []):
                    getattr(cls, field)

        return obj

    return wrapper


def async_session_scope(func):
    """
    inject session scop to function

    :param func:
    :return:
    """

    async def wrapper(*args, **kwargs):
        if kwargs.get("session_") is not None:
            return await func(*args, **kwargs)

        async with get_async_db_session(expire_on_commit=False) as session:
            kwargs["session_"] = session
            return await func(*args, **kwargs)

    return wrapper


def async_load_eager_fields(func):
    async def wrapper(*args, **kwargs):
        eager_load = kwargs.pop("eager_load", False)
        obj = await func(*args, **kwargs)
        if eager_load:
            if isinstance(obj, SQLModel):
                obj = [obj]
            if not isinstance(obj, Iterable):
                logger.warning(f"eager_load: obj is not Iterable")
                return

            for cls in obj:
                for field in getattr(cls, "__eager__fields__", []):
                    getattr(cls, field)

        return obj

    return wrapper
