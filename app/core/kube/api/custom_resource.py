from . import custom_api


def create_custom_rsrc(group, version, kind, data, namespace):
    return custom_api.create_namespaced_custom_object(group, version,
                                                      namespace, kind, data)


def update_custom_rsrc(group, version, plural, name, body, namespace):
    return custom_api.patch_namespaced_custom_object(group, version,
                                                     namespace, plural, name, body)


def delete_custom_rsrc(group, version, kind, name, namespace,
                       policy="Foreground"):
    return custom_api.delete_namespaced_custom_object(
        group, version, namespace, kind, name, propagation_policy=policy
    )


def list_custom_rsrc(group, version, kind, namespace):
    return custom_api.list_namespaced_custom_object(group, version, namespace,
                                                    kind)


def get_custom_rsrc(group, version, kind, namespace, name):
    return custom_api.get_namespaced_custom_object(group, version, namespace,
                                                   kind, name)


def list_cluster_custom_rsrc(group, version, kind):
    return custom_api.list_cluster_custom_object(group, version, kind)


def get_cluster_custom_rsrc(group, version, kind, name):
    return custom_api.get_cluster_custom_object(group, version, kind, name)


def delete_cluster_custom_rsrc(group, version, kind, name):
    return custom_api.delete_cluster_custom_object(group, version, kind, name)


def remove_cluster_custom_finalizer(group, version, kind, name):
    body = {
        "metadata": {
            "finalizers": None
        }
    }
    return custom_api.patch_cluster_custom_object(group, version, kind, name, body)
