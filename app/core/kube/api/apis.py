from kubernetes import client, config
from kubernetes.config import ConfigException

import app
from app import logger

try:
    config.load_incluster_config()
except ConfigException:
    try:
        config.load_kube_config()
    except Exception as e:
        if app.settings.export_swagger_mode:
            logger.warning("No kubernetes configuration found, using mock client")
        else:
            raise ConfigException("No kubernetes configuration found")

# Create the Apis
v1_core = client.CoreV1Api()
custom_api = client.CustomObjectsApi()
storage_api = client.StorageV1Api()
