import base64
import json
import re

from kr8s import NotFoundError
from kr8s.objects import Secret

from app import logger

def to_k8s_rfc_1123(name):
    # 删除非允许的字符
    name = re.sub(r'[^a-z0-9-]', '', name.lower())
    # 删除开头和结尾的连字符
    name = re.sub(r'^-|-$', '', name)
    # 确保名字长度不超过63个字符
    name = name[:63]
    return name

class DockerCfgSecret:

    def __init__(self, namespace, url, username, password):
        self.namespace = namespace
        self.url = url
        self.username = username
        self.password = password

    @property
    def name(self):
        return to_k8s_rfc_1123(f'{self.url.replace(".", "-").replace(":", "-")}-{self.username}')

    def create(self):
        try:
            Secret.get(self.name, self.namespace)
            return None
        except NotFoundError as e:
            logger.info("create docker secret")

        secret = Secret({
            "apiVersion": "v1",
            "kind": "Secret",
            "metadata": {
                "name": self.name,
                "namespace": self.namespace
            },
            "type": "kubernetes.io/dockerconfigjson",
            "data": {
                ".dockerconfigjson": base64.b64encode(json.dumps({
                    "auths": {
                        self.url: {
                            "username": self.username,
                            "password": self.password,
                            "auth": base64.b64encode(f"{self.username}:{self.password}".encode()).decode()
                        }
                    }
                }).encode()).decode()
            }
        })
        secret.create()
