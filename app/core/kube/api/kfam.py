import json
import random
import pickle
import time
from typing import List, Optional

import httpx

from app.cruds.user import UserCRUD
from app.apps.user.exceptions import CreateUserException
from ..errors.exceptions import NoBindsException
from ..models.kfam import Bindings, Binding, Namespace, WorkGroupInfo

import app
from app.core.loggers import logger
from ...redis.redis_client import KfamCacheClient

workgroup_cache = KfamCacheClient()

class KfamClient():

    def __init__(self, user_id: str):
        self._KFAM_HOST = "profiles-kfam.kubeflow.svc"
        self._KFAM_PORT = 8081
        self._BASE_URL = f"http://{self._KFAM_HOST}:{self._KFAM_PORT}/kfam"

        self._client = httpx.Client(
            base_url=self._BASE_URL, auth=("", ""), headers={"kubeflow-userid": user_id}
        )
        self.user_id = user_id

    def get_v1_role_cluster_admin(self) -> bool:
        path = "/v1/role/clusteradmin"
        params = {}

        logger.debug(f"Getting vi role cluster admin for user {self.user_id}...")
        response = self._client.get(path, params=params)
        response.raise_for_status()
        logger.debug(f"Vi role cluster admin for user {self.user_id} retrieved successfully.")

        return response.json() is False

    def read_bindings(self) -> List[Binding]:
        max_retries = 3
        retry_count = 0
        path = "/v1/bindings"
        params = {
            "user": self.user_id
        }

        while retry_count < max_retries:
            try:
                response = self._client.get(path, params=params)
                response.raise_for_status()

                if response.json():
                    logger.debug(f"Bindings for user {self.user_id} retrieved successfully. Response: {response.json()}")
                    return Bindings(**response.json()).bindings

                logger.warning(f"Empty response received for user {self.user_id}, retrying {retry_count + 1}...")

            except Exception as e:
                logger.warning(f"Request failed for user {self.user_id}: {str(e)}, retrying {retry_count + 1}...")

            delay = 0.1 + random.random() * 0.2
            time.sleep(delay)
            retry_count += 1

        raise NoBindsException(f"No bindings found for user {self.user_id}")


    def has_admin_profile(self, workgroup: WorkGroupInfo) -> bool:
        if not workgroup:
            return False
        for namespace in workgroup.namespaces:
            if namespace.role == "admin":
                return True
        return False

    def get_or_create_profile(self, user_db: UserCRUD, kind: str = "User", timeout: int = 3000) -> WorkGroupInfo:
        workgroup: WorkGroupInfo = self.get_workgroup_info()
        if not self.has_admin_profile(workgroup):
            workgroup = self.create_profile(user_db, kind, timeout)

        # Check user_info existed.
        user_db.exist_user_info_add(self.user_id, self.user_id.lower())

        namespaces = [ns.namespace for ns in workgroup.namespaces]
        users_info = user_db.get_user_by_namespaces(namespaces)
        users_info_map = { user_info["user_id"].lower(): user_info for user_info in users_info }

        for ns in workgroup.namespaces:
            user_info = users_info_map.get(ns.namespace)
            if user_info:
                ns.email = user_info["email"]
                ns.namespace_user_id = user_info["user_id"]

        return workgroup

    def get_all_profile(self, user_db: UserCRUD) -> WorkGroupInfo:
        workgroup: WorkGroupInfo = self.get_workgroup_info()
        namespaces = [ns.namespace for ns in workgroup.namespaces]
        users_info = user_db.get_user_by_namespaces(namespaces)
        # 重组workgroup.namespaces
        workgroup_namespaces = []
        for item in workgroup.namespaces:
            for user_info in users_info:
                if user_info["status"] == "active" and item.namespace == user_info["user_id"].lower():
                    workgroup_namespaces.append(Namespace(user=item.user, namespace=item.namespace, role=item.role,
                              email=user_info["email"], namespace_user_id=user_info["user_id"]))
                    break
        workgroup.namespaces = workgroup_namespaces
        return workgroup

    def create_profile(self, user_db: UserCRUD, kind: str = "User", timeout: int = 3000, ) -> WorkGroupInfo:
        path = "/v1/profiles"
        body = {
            "metadata": {
                "name": self.user_id.lower(),
            },
            "spec": {
                "owner": {
                    "kind": kind,
                    "name": self.user_id
                }
            }
        }
        # init_user_info
        user_db.init_user_info(self.user_id, self.user_id.lower())

        logger.debug(f"Creating profile for user {self.user_id}...with body: {json.dumps(body)}")
        response = self._client.post(path, json=body)
        if response.is_error:
            logger.error(f"Create profile for user {self.user_id} failed. Response: {response.text}")

        logger.debug(f"Profile for user {self.user_id} created successfully. ")

        for i in range(10):
            logger.debug(f"Waiting for profile {self.user_id} to be created. Attempt {i + 1}...")
            if workgroup := self.get_workgroup_info():
                # init success user.status -> active
                user_db.update_user_info_status(self.user_id, "active")
                return workgroup
            time.sleep(1)

        raise CreateUserException(f"Create profile for user {self.user_id} failed.")

    def get_workgroup_info(self) -> Optional[WorkGroupInfo]:
        """
        获取用户的工作组信息, 如果用户没有绑定工作组, 则创建一个工作组, 默认名称为用户的user_id.lower()
        :return:
        """

        try:
            if cached := workgroup_cache.get_work_group_cache(self.user_id):
                return cached
            admin_response = self.get_v1_role_cluster_admin()
            bindings = self.read_bindings()
            namespace = [
                Namespace(user=binding.user.name, namespace=binding.referred_namespace, role=binding.role_ref.name,
                          email=binding.user.name)
                for binding in bindings]
            work_group_info = WorkGroupInfo(user=self.user_id, namespaces=namespace, isClusterAdmin=admin_response)
            workgroup_cache.set_work_group_cache(self.user_id, work_group_info)
            return work_group_info
        except NoBindsException as e:
            logger.warning(f"User {self.user_id} has no bindings. {e}")
            return None

    def get_shared_namespace_user_id(self, namespace: str) -> str:
        workgroup_info = self.get_workgroup_info()
        for ns in workgroup_info.namespaces:
            if ns.namespace == namespace:
                return ns.user

    def delete(self, namespace: str):
        path = f"/v1/profiles/{namespace}"
        response = self._client.delete(path)
        response.raise_for_status()
        return

    def has_profile(self):
        workgroup: WorkGroupInfo = self.get_workgroup_info()
        return self.has_admin_profile(workgroup)