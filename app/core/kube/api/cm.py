from app import logger
from app.core.kube.api import v1_core,client



def create_cm(name, namespace, data, filename="config.yaml"):

    configmap = client.V1ConfigMap(
        api_version="v1",
        kind="ConfigMap",
        metadata=client.V1ObjectMeta(name=name, namespace=namespace),
        data={filename: data}  # 将 YAML 文件内容存入 ConfigMap
    )
    try:
        v1_core.create_namespaced_config_map(namespace=namespace, body=configmap)
        return True
    except Exception as e:
        logger.error(e)
        return False


def delete_cm(name, namespace):
    try:
        v1_core.delete_namespaced_config_map(name=name, namespace=namespace)
        return True
    except Exception as e:
        logger.error(e)
        return False