import copy
import ipaddress
import json
import traceback

from . import custom_api


class SubnetAllocator:
    def __init__(self, current_subnet=None):
        # 定义父网络和起始子网
        self.parent_network = ipaddress.IPv4Network('10.0.0.0/8')
        if not current_subnet:
            self.current_subnet = ipaddress.IPv4Network('********/24')
        else:
            self.current_subnet = ipaddress.IPv4Network(current_subnet)

        # 验证初始子网有效性
        if not self.current_subnet.subnet_of(self.parent_network):
            raise ValueError("初始子网不在父网络范围内")

    def get_next_subnet(self):
        # 保存当前要返回的子网
        current = self.current_subnet

        # 计算下一个子网地址
        next_address = self.current_subnet.network_address + 256

        try:
            # 尝试创建下一个子网
            next_subnet = ipaddress.IPv4Network(
                (next_address, self.current_subnet.prefixlen),
                strict=True
            )

            # 检查是否仍在父网络范围内
            if next_subnet.subnet_of(self.parent_network):
                self.current_subnet = next_subnet
            else:
                self.current_subnet = None  # 标记分配完毕
        except ValueError:
            self.current_subnet = None  # 标记分配完毕

        return str(current)




template = {
    "kind": "Subnet",
    "apiVersion": "kubeovn.io/v1",
    "metadata":{
        "name": ""
    },
    "spec":{
      "vpc": "zd-vpc",
      "cidrBlock": "",
      "protocol": "IPv4",
      "allowEWTraffic": True,
      "namespaces": ["zd-ns-user1"],
      "acls": [
          {
              "action": "allow",
              "direction": "to-lport",
              "match": "ip4.src == ************/24 && ip4.dst == ********/24 && ip",
              "priority": 1001,
          },
          {
              "action": "allow",
              "direction": "to-lport",
              "match": "ip4.src == **********/16 && ip4.dst == ********/24 && ip",
              "priority": 1001
          },
          {
              "action": "drop",
              "direction": "to-lport",
              "match": "ip4.src == 10.0.0.0/8 && ip4.dst == ********/24 && ip",
              "priority": 1000
          }
       ]

    }
}


def list_subnet():
    return custom_api.list_cluster_custom_object(group="kubeovn.io", version="v1", plural="subnets")

def get_subnet(name):
    return custom_api.get_cluster_custom_object(group="kubeovn.io", version="v1", plural="subnets", name=name)

def create_subnet(namespace, name, subnet):
    data = copy.deepcopy(template)
    data['metadata']['name'] = name
    data['spec']['namespaces'] = [namespace]
    data["spec"]["cidrBlock"] = subnet
    custom_api.create_cluster_custom_object(group="kubeovn.io", version="v1",
                                            plural="subnets", body=data)


