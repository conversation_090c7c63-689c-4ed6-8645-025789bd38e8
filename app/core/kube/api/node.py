import re
from typing import List, Optional

from kubernetes.client import <PERSON><PERSON><PERSON><PERSON><PERSON>, V1Node, V1NodeList, V1Taint

from app import logger
from . import v1_core
from ...utils import get_number


def list_nodes(**kwargs) -> Optional[V1NodeList]:
    try:
        result = v1_core.list_node(**kwargs)
        return result
    except ApiException as e:
        logger.error(e)
        logger.error(type(e))
        logger.info(e.status)
        return None


def read_nodes(node_name, **kwargs) -> Optional[V1Node]:
    try:
        result = v1_core.read_node(node_name, **kwargs)
        return result
    except ApiException as e:
        logger.error(e)
        logger.error(type(e))
        logger.info(e.status)
        return None


def replace_node_tag(node_name, node):
    try:
        v1_core.replace_node(node_name, node)
        return True
    except Exception as e:
        logger.error("update node tag failed [%s]", e)
        return None


def check_node_resource_satisfy(node_name, gpu=0, cpu=0, memory=0, gpu_model_resource_key="nvidia.com/gpu"):
    logger.info(f"check node {node_name}")
    pods = v1_core.list_pod_for_all_namespaces(field_selector=f"spec.nodeName={node_name}")
    allocated_cpu = 0
    allocated_memory = 0
    allocated_gpu = 0
    for pod in pods.items:
        # if not pod.metadata.owner_references:
        #     continue
        # pod_name = pod.metadata.owner_references[0].name
        # if pod_name.startswith("nb") or pod_name.startswith("tn"):
        #     logger.info(f"{node_name} has resource {pod_name}")
        #     return False
        for container in pod.spec.containers:
            if container.resources.requests:
                allocated_cpu += get_mcpu(container.resources.requests.get('cpu', '0'))
                allocated_memory += get_mb_memory(container.resources.requests.get('memory', '0'))
                allocated_gpu += get_mcpu(container.resources.requests.get(gpu_model_resource_key, '0'))
    node = v1_core.read_node_status(node_name)
    taints = node.spec.taints
    if taints:
        for taint in taints:
            if taint.key == "node.kubernetes.io/unschedulable" and taint.effect == "NoSchedule":
                logger.warning(f"node {node_name} is NoSchedule")
                return False
    allocatable_cpu = get_mcpu(node.status.allocatable["cpu"])
    allocatable_memory = int(get_number(node.status.allocatable["memory"])) / 1024 / 1024
    allocatable_gpu = int(node.status.allocatable.get(gpu_model_resource_key, 0))
    logger.info(f"{node_name} resource  req[{cpu},{memory},{gpu}], node has "
                f"[{(allocatable_cpu - allocated_cpu) // 1000},{(allocatable_memory - allocated_memory) // 1000},{allocatable_gpu - allocated_gpu}]")
    if allocated_gpu == 0 and allocatable_gpu == gpu:
        if allocatable_cpu - allocated_cpu > cpu * 1000:
            # 可用内存大于申请内存,并且节点实际内存比申请的规格内存多500G范围内，认为节点符合专属节点的配置
            if memory * 1000 < allocatable_memory - allocated_memory < (memory + 500) * 1000:
                logger.info("node has been selected")
                return True
    logger.warning("node not satisfy")
    return False


def check_user_resource(nodes, namespace):
    '''
    判断节点是否存在用户资源
    :param namespace:
    :param nodes: 节点列表hostname
    :return:
    '''
    data = v1_core.list_namespaced_pod(namespace)
    pods = data.items
    for pod in pods:
        node_name = pod.spec.node_name
        # resource_type = pod.metadata.owner_references[0].kind
        resource = pod.metadata.owner_references[0].name
        if resource.startswith("nb") or resource.startswith("tn") or resource.startswith("inf") or resource.startswith("fn"):
            if node_name in nodes:
                logger.info("user  has resource[%s] on node[%s]", resource, node_name)
                return resource
    return False


def get_mb_memory(input_string) -> int:
    match = re.match(r'^(\d+)', input_string)
    if match:
        if "m" in input_string.lower():
            return int(match.group(1))
        else:
            return int(match.group(1)) * 1000
    else:
        return 0


def get_mcpu(input_string) -> int:
    match = re.match(r'^(\d+)', input_string)
    if match:
        if "m" in input_string.lower():
            return int(match.group(1))
        else:
            return int(match.group(1)) * 1000
    else:
        return 0


def check_taints(taints):
    # 获取节点的taints是否符合要求,
    if not taints:
        return True
    if len(taints) > 1:
        logger.warning("node has more than one taints")
        return False
    if taints[0].key != "aicp.group/worker":
        logger.warning("node has taints which is not aicp.group/worker")
        return False
    return True


def check_taints_by_whitelist(taints):
    """
    检查taints是否符合要求
    :param taints:
    :return:
    """
    white_list = {"aicp.group/worker", "aicp.group/resource_group_node", "aicp.group/resource_group"}
    # 获取节点的taints是否符合要求,
    if not taints:
        return True
    for taint in taints:
        if taint.key not in white_list:
            logger.warning(f"node has taints[{taint.key}] which is not in white list")
            return False
    return True


def get_allowable_nodes() -> list[V1Node]:
    """
    获取可用节点
    :return:
    """
    nodes: V1NodeList = list_nodes(label_selector="aicp.group/aipods_type")
    return list(filter(lambda x: check_taints(x.spec.taints), nodes.items))


def get_resource_group_nodes(rg_id: str) -> list[V1Node]:
    """
    获取资源组节点
    :return:
    """
    nodes: V1NodeList = list_nodes(label_selector="aicp.group/resource_group={}".format(rg_id))
    return nodes.items


def get_nodes_by_resource_groups(rg_ids: List[str]) -> list[V1Node]:
    """
    获取资源组节点
    :return:
    """
    nodes: V1NodeList = v1_core.list_node(label_selector="aicp.group/resource_group in ({})".format(",".join(rg_ids)))
    return nodes.items


def get_nodes_by_names(names: List[str]) -> List[V1Node]:
    """
    获取节点
    :return:
    """
    nodes: V1NodeList = list_nodes()
    return list(filter(lambda x: x.metadata.name in names, nodes.items))
