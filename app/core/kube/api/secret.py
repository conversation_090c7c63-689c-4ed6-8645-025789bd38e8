import copy

from . import v1_core

template = {
    "apiVersion": "v1",
    "data":{
        "password": "",
        "username": ""
    },
    "kind": "Secret",
    "metadata":{
        "annotations":{
            "kubesphere.io/creator": "admin"
        },
        "name": "",
        "namespace": ""
    },
}

def get_secret(namespace, name, auth=True):
    return v1_core.read_namespaced_secret(name, namespace)


def create_secret(namespace, data, name, auth=True):
    secret = copy.deepcopy(template)
    secret["metadata"]["name"] = name
    secret["data"] = data
    secret["metadata"]["namespace"] = namespace
    return v1_core.create_namespaced_secret(namespace, secret)






