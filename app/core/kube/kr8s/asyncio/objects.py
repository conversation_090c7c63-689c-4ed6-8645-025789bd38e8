from kr8s.asyncio.objects import new_class


class _AuthorizationPolicy(
    new_class(
        kind="AuthorizationPolicy",
        version="security.istio.io/v1",
        plural="authorizationpolicies",
        namespaced=True,
        scalable=False,
    )
):
    pass


class _ZFSVolume(
    new_class(
        kind="ZFSVolume",
        version="zfs.openebs.io/v1",
        plural="zfsvolumes",
        namespaced=True,
        scalable=False,
    )
):


    def compression(self):
        """
        compression
        """
        return self.spec.get("compression", "off")

    async def compress_on(self):
        """
        compress_on
        """
        await self.async_patch({"spec": {"compression": "on"}})
