import time

from kr8s.asyncio.objects import APIObject
from kr8s.asyncio.objects import Event


async def create_aicp_resource_warning_event(cr: APIObject, reason: str, message: str):
    """
    :param cr:
    :param message:
    :param reason:
    :return:
    """
    e = await Event({
        "apiVersion": "v1",
        "kind": "Event",
        "metadata": {
            "name": f"aicp-api-server-{cr.name}-{int(time.time())}",
            "namespace": "aicp-system",
        },
        "reportingComponent": "aicp-api-server",
        "reportingInstance": "aicp-api-server",
        "reason": reason,
        "message": message,
        "type": "Warning",
        "firstTimestamp": "2021-08-06T07:00:00Z",
        "lastTimestamp": "2021-08-06T07:00:00Z",
    })
    await e.create()
    return e
