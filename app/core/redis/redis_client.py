# -*- coding: utf-8 -*-
import pickle

import redis
import asyncio
from typing import List, Dict, Any, Optional
from app.core.constant import OPERATION_INVOKE_EVENT_QUEUE, API_EVENT_QUEUE_MAX_LEN
from datetime import datetime
from app.core.utils import generate_random_string
from app import logger
import app


class RedisClient:
    def __init__(self, prefix: str = app.settings.REDIS_PREFIX, decode_responses: bool = True):
        self.prefix = prefix
        self.conn = redis.StrictRedis(  # noqa
            host=app.settings.REDIS_HOST,
            port=app.settings.REDIS_PORT,
            password=app.settings.REDIS_PASSWORD,
            decode_responses=decode_responses
        )
        try:
            self.conn.ping()
            logger.info("Connected to Redis successfully.")
        except redis.ConnectionError as e:
            logger.error("Failed to connect to Redis", exc_info=True)
            raise e

    def _get_prefixed_key(self, key: str) -> str:
        """Returns key with prefix."""
        return f"{self.prefix}{key}"

    def set(self, key: str, value: Any, nx: bool = False, ex: int = 60) -> bool:
        return self.conn.set(self._get_prefixed_key(key), value, nx=nx, ex=ex)

    def get(self, key: str) -> Optional[str]:
        return self.conn.get(self._get_prefixed_key(key))

    def delete(self, key: str) -> int:
        return self.conn.delete(self._get_prefixed_key(key))

    def mset(self, data: Dict[str, Any], ex: int = 60) -> bool:
        prefixed_data = {self._get_prefixed_key(k): v for k, v in data.items()}
        if self.conn.mset(prefixed_data):
            for key in prefixed_data.keys():
                self.conn.expire(key, ex)
            return True
        return False

    def mget(self, keys: List[str]) -> List[Optional[str]]:
        prefixed_keys = [self._get_prefixed_key(key) for key in keys]
        return self.conn.mget(prefixed_keys)

    def hset(self, name: str, mappings: Dict[str, Any], ex: int = 60) -> int:
        full_name = self._get_prefixed_key(name)
        result = self.conn.hset(full_name, mapping=mappings)
        self.conn.expire(full_name, ex)
        return result

    def hget(self, name: str, key: str) -> Optional[str]:
        return self.conn.hget(self._get_prefixed_key(name), key)

    def hget_all(self, name: str) -> Dict[str, str]:
        return self.conn.hgetall(self._get_prefixed_key(name))

    def hdel(self, name: str, key: str) -> int:
        return self.conn.hdel(self._get_prefixed_key(name), key)

    def zincrby(self, name: str, key: str, val: float) -> float:
        return self.conn.zincrby(self._get_prefixed_key(name), val, key)

    def expire(self, key: str, ex: int) -> bool:
        return self.conn.expire(self._get_prefixed_key(key), ex)

    async def product_msg(self, queue: str, data: Dict[str, Any], max_len: int = API_EVENT_QUEUE_MAX_LEN) -> str:
        queue_name = self._get_prefixed_key(queue)
        try:
            return self.conn.xadd(queue_name, data, maxlen=max_len)
        except Exception as e:
            logger.error(f"Failed to produce message to queue {queue_name}", exc_info=True)
            raise e

    def init_consume_group(self, queue: str, group_name: str) -> None:
        queue_name = self._get_prefixed_key(queue)
        try:
            self.conn.xgroup_create(queue_name, group_name, id='0', mkstream=True)
        except redis.exceptions.ResponseError as e:
            if 'BUSYGROUP Consumer Group name already exists' not in str(e):
                logger.error(f'Failed to create consumer group [{group_name}]', exc_info=True)
                raise e

    def consume_msg(self, queue: str, group_name: str, consumer_name: str, count: int = 100, block: int = 10000) -> List[tuple]:
        queue_name = self._get_prefixed_key(queue)
        message = self.conn.xreadgroup(group_name, consumer_name, {queue_name: '>'}, count=count, block=block)
        return message[0][1] if message else []

    def ack_msg(self, queue: str, group_name: str, message_ids: List[str], need_del: bool = True) -> None:
        queue_name = self._get_prefixed_key(queue)
        self.conn.xack(queue_name, group_name, *message_ids)
        if need_del:
            self.conn.xdel(queue_name, *message_ids)


redis_client = RedisClient()


def create_redis_data(user_id: str, operation_user_id: str, resource_id: str, resource_type: str, operation: str,
                      start_time: datetime, trace_id: str):
    return {
        "user_id": user_id,
        "operation_user_id": operation_user_id,
        "resource_id": resource_id,
        "resource_type": resource_type,
        "operation": operation,
        "start_time": start_time,
        "trace_id": trace_id,
    }


async def producer(data: Dict):
    try:
        message_id = await redis_client.product_msg(OPERATION_INVOKE_EVENT_QUEUE, data)
        logger.info(f"Produced message ID: {message_id}, Data: {data}")
    except Exception as e:
        logger.info(f"Error producing message: {e}")
    await asyncio.sleep(1)


class KfamCacheClient(RedisClient):

    def __init__(self):
        super().__init__(prefix="aicp:workgroup:", decode_responses=False)

    def set_work_group_cache(self, user_id: str, work_group: Any) -> bool:
        try:
            self.conn.set(self._get_prefixed_key(user_id), pickle.dumps(work_group), ex=43200)
            return True
        except Exception as e:
            logger.warning(f"Failed to set work group cache for user {user_id}", exc_info=True)
            return False

    def get_work_group_cache(self, user_id: str) -> Optional[Any]:
        try:
            dumps_cache = self.conn.get(self._get_prefixed_key(user_id))
            if not dumps_cache:
                return pickle.loads(dumps_cache)
        except Exception as e:
            logger.info(f"Failed to get work group cache for user {user_id}")

        return None

    def remove_work_group_cache(self, user_id: str) -> bool:
        try:
            self.conn.delete(self._get_prefixed_key(user_id))
            return True
        except Exception as e:
            logger.error(f"Failed to remove work group cache for user {user_id}", exc_info=True)
            return False
