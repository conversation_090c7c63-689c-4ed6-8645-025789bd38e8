from typing import Dict, List

import aiohttp
import requests

from app import logger
from app.core.utils import GPUManager


class PrometheusClient(object):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PrometheusClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        prometheus_server = "prometheus-k8s.kubesphere-monitoring-system:9090"
        self.url = f'http://{prometheus_server}/api/v1/query'

    def query_prometheus(self, query):
        params = {
            'query': query
        }
        response = requests.get(self.url, params=params)
        return response.json()

    def expression_execute(self, metric_name, expression=""):
        try:
            rep = self.query_prometheus('%s%s' % (metric_name, expression))
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get expression_execute failed, exception: %s", e)
            return []


class PromethusClient:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PromethusClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        prometheus_server = "prometheus-k8s.kubesphere-monitoring-system:9090"
        # prometheus_server = "60.216.39.180:32610"
        self.url = f'http://{prometheus_server}/api/v1/query'
        self.rang_url = f'http://{prometheus_server}/api/v1/query_range'

    def query_prometheus(self, query):
        params = {
            'query': query
        }
        r = requests.get(self.url, params=params)
        logger.debug("query_prometheus result [%s] ", r.text)
        return r.json()

    def query_range(self, query, start, end, step):
        params = {
            'query': query,
            'start': start,
            'end': end,
            'step': step
        }
        r = requests.get(self.rang_url, params=params)
        logger.debug("query_prometheus result [%s] ", r.text)
        return r.json()

    def get_aicp_resource_requests(self, nodename):
        try:
            query = 'sum(kube_pod_container_resource_requests{node="%s", pod=~"^(nb|tn|inf|ft)-.*$"}) by (resource)' % nodename
            rep = self.query_prometheus(query)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []

    def get_aicp_resource_requests_precise(self, nodes: List[str]):
        try:
            query = 'sum(kube_pod_container_resource_requests{node=~"%s", pod=~"^(nb|tn|inf|ft)-.*$", container=~"nb-.*|inference.*|pytorch.*"}) by (node, resource)' % "|".join(nodes)
            rep = self.query_prometheus(query)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []

    def get_resource_requests_by_nodes(self, nodes: List[str]):
        try:
            query = 'sum(kube_pod_container_resource_requests{node=~"%s"}) by (resource, node)' % "|".join(nodes)
            logger.info("get_resource_requests_by_nodes query: [%s]", query)
            rep = self.query_prometheus(query)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []

    def get_vgpu_resource_by_nodes(self, nodes: List[str]) -> Dict[str, Dict[str, int]]:
        """
        获取 vgpu 资源
        :param nodes:
        :return:
        {
            nodename: {
                "gpu_id": {
                    "total": 0,
                    "used": 0,
                    "available": 0,
                }
            }
        }
        """
        try:
            if not nodes:
                return {}

            nodename = "|".join(nodes)
            vgpu_devices_mem_limit_query = f"sum(GPUDeviceMemoryLimit{{nodeid=~'{nodename}'}}) by (nodeid, deviceuuid)"
            vgpu_devices_mem_used_query = f"sum(vGPUPodsDeviceAllocated{{nodename=~'{nodename}'}}) by (nodename, deviceuuid)"
            vgpu_device_core_available_query = f"sum(GPUDeviceCoreLimit{{nodeid=~'{nodename}'}}) by (nodeid, deviceuuid) - sum(GPUDeviceCoreAllocated{{nodeid=~'{nodename}'}}) by (nodeid, deviceuuid)"
            vgpu_devices_mem_limit_res = self.query_prometheus(vgpu_devices_mem_limit_query)
            vgpu_devices_mem_used_res = self.query_prometheus(vgpu_devices_mem_used_query)
            vgpu_device_core_available_res = self.query_prometheus(vgpu_device_core_available_query)

            vgpu_resource = {}

            if vgpu_devices_mem_limit_res.get("status") == 'success' and vgpu_devices_mem_used_res.get(
                    "status") == 'success':
                for result in vgpu_devices_mem_limit_res.get("data")["result"]:
                    node, device = result["metric"]["nodeid"], result["metric"]["deviceuuid"]
                    if node not in vgpu_resource:
                        vgpu_resource[node] = {}
                    vgpu_resource[node][device] = {
                        "total": int(result["value"][-1]),
                        "used": 0,
                        "available": int(result["value"][-1]),
                        "core_available": 0,
                    }
                for result in vgpu_device_core_available_res.get("data")["result"]:
                    node, device = result["metric"]["nodeid"], result["metric"]["deviceuuid"]
                    if device not in vgpu_resource.get(node, {}):
                        logger.warning("node %s not in vgpu_resource, skip", node)
                        continue
                    vgpu_resource[node][device]["core_available"] = int(result["value"][-1])

                for result in vgpu_devices_mem_used_res.get("data")["result"]:
                    node, device = result["metric"]["nodename"], result["metric"]["deviceuuid"]
                    if device not in vgpu_resource.get(node, {}):
                        logger.warning("node %s not in vgpu_resource, skip", node)
                        continue
                    used = int(result["value"][-1])
                    vgpu_resource[node][device]["used"] = used
                    vgpu_resource[node][device]["available"] = vgpu_resource[node][device]["total"] - used
            return vgpu_resource

        except Exception as e:
            logger.error("get resource data failed")
            return {}

    def get_resource_requests_by_namespace(self, nodename, namespace):
        try:
            query = 'sum(kube_pod_container_resource_requests{node="%s",namespace="%s"}) by (resource)' % (
                nodename, namespace)
            rep = self.query_prometheus(query)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []

    def aicp_resource_used_node(self) -> Dict[str, int]:
        """
        获取 部署了 aicp 资源的(训练或者容器实例) 的节点

        :return: dict :
            {
                "node_name": 1,
            }
        """
        res = {}
        try:
            query = 'sum(kube_pod_info{pod=~"^(nb|tn|inf)-.*$", node!=""}) by (node)'
            rep = self.query_prometheus(query)
            if rep.get("status") != 'success':
                logger.error("get aicp resource num node failed")
            for result in rep.get("data")["result"]:
                if "node" not in result["metric"]:
                    continue
                res[result["metric"]["node"]] = int(result["value"][-1])

        except Exception as e:
            logger.error("get resource data failed")

        return res

    def get_aicp_resource_used_gpu_group_by_node(self):
        """
        获取 部署了 aicp gpu资源的(训练或者容器实例) 的节点

        :return: dict :
            {
                "node_name": 1,
            }
        """
        res = {}
        try:
            resources_name = GPUManager().get_all_resource_name_in_prometheus()
            query = 'sum(kube_pod_container_resource_requests{resource=~"%s", node!=""}) by (node)' % "|".join(resources_name)
            rep = self.query_prometheus(query)
            if rep.get("status") != 'success':
                logger.error("get aicp resource num node failed")
            for result in rep.get("data")["result"]:
                res[result["metric"]["node"]] = int(result["value"][-1])

        except Exception as e:
            logger.error(f"get resource data failed: {e}")
        return res

    def get_namespace_notebook_probe_success(self, namespace: str, notebook_id: str):
        try:
            query = f'probe_success{{job="Notebook-blackbox-exporter",notebook=~"{notebook_id}"}}'
            rep = self.query_prometheus(query)
            if rep.get("status") != 'success':
                logger.error("get notebook probe status success failed")
                return {}
            res = {}
            for result in rep.get("data")["result"]:
                notebook_id, port = result["metric"]["notebook"], str(result["metric"]["port"])
                if notebook_id not in res:
                    res[notebook_id] = {}
                res[notebook_id][port] = bool(int(result["value"][1]))
            logger.debug("get notebook probe success data: [%s]", res)
            if not res:
                return {}
            return res[notebook_id]

        except Exception as e:
            logger.error("get notebook probe success failed, reason: %s", str(e))
            return {}

    def get_notebook_probe_success(self, notebooks_id: List[str]):
        if not notebooks_id:
            return {}

        try:
            query = 'probe_success{job="Notebook-blackbox-exporter",notebook=~"%s"}' % "|".join(notebooks_id)
            rep = self.query_prometheus(query)
            if rep.get("status") != 'success':
                logger.error("get notebook probe success failed")
                return {}
            res = {}
            for result in rep.get("data")["result"]:
                notebook_id, port = result["metric"]["notebook"], str(result["metric"]["port"])
                if notebook_id not in res:
                    res[notebook_id] = {}
                res[notebook_id][port] = bool(int(result["value"][1]))
            logger.info("get notebook probe success data: [%s]", res)
            return res

        except Exception as e:
            logger.error("get notebook probe success failed")
            return {}

    def get_aicp_resource_used_vgpu_group_by_node(self):
        """
        获取 部署了 aicp vgpu资源的(训练或者容器实例) 的节点

        :return: dict :
            {
                "node_name": 1,
            }
        """
        res = {}
        try:
            query = 'count(count(vGPUPodsDeviceAllocated{}) by (nodename, deviceuuid)) by (nodename)'
            rep = self.query_prometheus(query)
            if rep.get("status") != 'success':
                logger.error("get aicp resource num node failed")
            for result in rep.get("data")["result"]:
                res[result["metric"]["nodename"]] = int(result["value"][-1])

        except Exception as e:
            logger.error(f"get resource data failed: {e}")
        return res

    def get_count_used_gpu_and_vgpu_by_node(self):
        gpu = self.get_aicp_resource_used_gpu_group_by_node()
        vgpu = self.get_aicp_resource_used_vgpu_group_by_node()
        return {**gpu, **vgpu}

    def get_pod_node(self, pods_name: List[str]):
        """
        获取 pod 所在的 node
        :param pods_name:
        :return:
            {
                "pod_name": "node_name"
            }
        """
        if not pods_name:
            return {}

        try:
            query = 'kube_pod_info{pod=~"%s"}' % "|".join(pods_name)
            rep = self.query_prometheus(query)
            if rep.get("status") != 'success':
                logger.error("get pod node failed")
                return {}
            res = {}
            for result in rep.get("data")["result"]:
                pod_name, node = result["metric"]["pod"], result["metric"]["node"]
                res[pod_name] = node
            logger.info("get pod node data: [%s]", res)
            return res
        except Exception as e:
            logger.error(f"get pod node failed, {e}")
            return {}



class PromethusAsyncClient(object):
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(PromethusAsyncClient, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        prometheus_server = "prometheus-k8s.kubesphere-monitoring-system:9090"
        # prometheus_server = "60.216.39.180:32610"
        self.url = f'http://{prometheus_server}/api/v1/query'

    async def query_prometheus(self, query):
        params = {
            'query': query
        }
        async with aiohttp.ClientSession() as session:
            async with session.get(self.url, params=params) as response:
                return await response.json()

    async def get_disk_ava(self, mount_path):
        try:
            query = 'node_filesystem_free_bytes{mountpoint="%s"}/1024/1024/1024' % mount_path
            rep = await self.query_prometheus(query)
            logger.info("get disk data: [%s]", rep)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get query_prometheus failed")
            return []

    async def get_disk_total(self, mount_path):
        try:
            query = 'node_filesystem_size_bytes{mountpoint="%s"}/1024/1024/1024' % mount_path
            rep = await self.query_prometheus(query)
            logger.info("get disk data: [%s]", rep)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get query_prometheus failed")
            return []

    async def get_gpu_processes(self, uuids: str):
        try:
            query = 'gpu_processes{UUID=~"%s"}' % uuids
            prom_res = await self.query_prometheus(query)
            if prom_res.get("status") == 'success':
                data = prom_res.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []

    async def get_resource_requests(self, nodename):
        try:
            query = 'sum(kube_pod_container_resource_requests{node="%s"}) by (resource)' % nodename
            rep = await self.query_prometheus(query)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []

    async def get_resource_requests_by_namespace(self, nodename, namespace):
        try:
            query = 'sum(kube_pod_container_resource_requests{node="%s",namespace="%s"}) by (resource)' % (
                nodename, namespace)
            rep = await self.query_prometheus(query)
            if rep.get("status") == 'success':
                data = rep.get("data")["result"]
                return data
        except Exception as e:
            logger.error("get resource data failed")
            return []
