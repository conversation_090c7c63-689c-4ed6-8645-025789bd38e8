from app.core.exceptions import AICPBaseException


class GlobalServerImplementException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "Global服务功能未实现"


class GlobalServerResponseException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "Global服务响应异常"


class UserHasNotPermissionException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "用户没有访问权限"


class UserHasNotWritePermissionException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "用户没有写的权限"


class GetSubUserInfoException(AICPBaseException):
    BASE_CODE = 1400
    BASE_MESSAGE = "获取子账号信息异常"