import requests

import app
import httpx
from enum import Enum
from typing import List

from app import logger
from app.core.global_server.exceptions import GlobalServerImplementException, GlobalServerResponseException
from app.core.qingcloud import utils


# 鉴权方式：集群内服务，使用header aicp-userid；集群外服务，使用签名
class AuthType(str, Enum):
    HEADER = 'header'
    SIGNATURE = 'signature'
    NONE = 'none'


class GlobalServerClient():
    # global服务客户端
    def __init__(self):
        self._global_server_url = app.settings.GLOBAL_SERVER_URL

    def _send_request(self, path, params=None, data=None, user_id=None, access_key_id=None, secret_access_key=None, auth_type: AuthType = AuthType.HEADER, method: str = 'GET', timeout: int = 5):
        """
        发送http请求到global服务
        """
        params = {} if params is None else params
        headers = {'Content-Type': 'application/json', 'aicp-userid': user_id}
        if auth_type == AuthType.SIGNATURE:
            path = path + '?' + utils.generate_signature(method, path, access_key_id, secret_access_key, params)

        url = f'{self._global_server_url}{path}'
        logger.debug(f'[AICP-REQ] {url}')
        if method == 'GET':
            rep = requests.get(url, headers=headers, params=params if auth_type != AuthType.SIGNATURE else {}, timeout=timeout)
        elif method == 'POST':
            rep = requests.post(url, headers=headers, data=data, timeout=timeout)
        else:
            raise GlobalServerImplementException()
        if rep.status_code != 200:
            logger.error(f"[AICP-ERR] 请求 [{url}] 失败: [{rep.status_code}][{rep.text}]")
            raise GlobalServerImplementException()
        try:
            # 对http返回结果进行解析
            _data = rep.json()
            if _data.get('ret_code') != 0:
                logger.error(f"[AICP-ERR] 请求 [{url}] 失败: [{_data.get('ret_code')}][{_data.get('message')}]")
                raise GlobalServerResponseException()
            logger.debug(f'[AICP-RSP] {_data}')
            return _data
        except ValueError:
            raise GlobalServerResponseException()

    def get_group_auth_info(self, user, auth_type: AuthType = AuthType.SIGNATURE):
        """
        获取团队管理用户认证信息
        """
        path = "/global/team/api/user/auth"
        try:
            response = self._send_request(path, user_id=user["user_id"], access_key_id=user["access_key_id"], secret_access_key=user["secret_access_key"], auth_type=auth_type)
            return response
        except Exception as e:
            logger.error("查询用户权限信息失败，使用默认权限:", e)
            return {
                "data": {
                    "permissions": [
                        {
                            "module": "NB",
                            "module_name": "容器实例",
                            "permission": "OWN"
                        },
                        {
                            "module": "TN",
                            "module_name": "训练任务",
                            "permission": "OWN"
                        },
                        {
                            "module": "INF",
                            "module_name": "推理服务",
                            "permission": "OWN"
                        },
                        {
                            "module": "EPFS",
                            "module_name": "存储与数据",
                            "permission": "OWN"
                        },
                        {
                            "module": "RG",
                            "module_name": "专属资源",
                            "permission": "OWN"
                        }
                    ]
                }
            }
