import logging
import os
import sys
import uuid
from logging.handlers import RotatingFileHandler

from flask import Request

from app.core.config import coroutine_id, get_coroutine_id_value, get_request_id_context_value, request_id_context
from app.core.constant import AICPType


class TraceIDFilter(logging.Filter):
    def filter(self, record):
        record.traceid = get_request_id_context_value()
        return True


class CoroutineIDFilter(logging.Filter):
    def filter(self, record):
        record.coroutine_id = get_coroutine_id_value() # Default to 'unknown' if not set
        return True


async def log_middleware(request: Request, call_next):
    request_id = str(uuid.uuid4())[:8]
    request_id_context.set(request_id)
    response = await call_next(request)
    response.headers["X-Request-Id"] = request_id
    request_id_context.set(None)
    logger.debug("request_id: %s" % request_id + " " + request.url.path)
    return response


def get_logger():
    """
    获取日志记录器
    :return:
    """
    aicp_type = os.environ.get("AICP_TYPE", AICPType.AICPWebApp.value)
    if aicp_type == AICPType.AICPWebApp.value:
        logger_filter = TraceIDFilter("aicp")
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s(%(traceid)s)')
    elif aicp_type == AICPType.AICPUpdateJob.value:
        logger_filter = CoroutineIDFilter("aicp")
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s(%(coroutine_id)s)')
    else:
        logger_filter = None
        formatter = logging.Formatter(
            f'%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s({aicp_type})')
    # 创建一个StreamHandler，将日志记录到控制台
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)

    # 获取或创建一个日志记录器
    aicp_logger = logging.getLogger(aicp_type)
    # logger.addHandler(file_handler)
    aicp_logger.setLevel(logging.DEBUG)
    aicp_logger.addHandler(console_handler)
    if logger_filter:
        aicp_logger.addFilter(logger_filter)
    return aicp_logger


logger = get_logger()
async_pool_logger = logger
