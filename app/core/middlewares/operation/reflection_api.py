# -*- coding: utf-8 -*-
"""
常量类 / 常量
"""
from enum import Enum


class URLPrefix(str, Enum):
    data = "data"


url_reflection_type = {
    "notebooks": "container",
    "keys": "public_key",
    "trains": "distribution_job",
    "resource": "resource_group",
    "billing": "billing",
    "finetuning": "finetuning",
}


def need_get_response(first_segment: str, last_segment: str, method: str) -> bool:
    """

    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :return:
    """
    if first_segment == "notebooks" or first_segment == "trains":
        if last_segment == "create":
            return True
        else:
            return False
    if first_segment == "resource":
        if last_segment in ["user", "add_node", "remove_node"]:
            return False
    if first_segment == "billing":
        return False
    if method == "DELETE" or method == "PUT":
        return False
    else:
        return True

