# -*- coding: utf-8 -*-
import app
import datetime
from app.core.middlewares.operation.reflection_api import URLPrefix, url_reflection_type
from app.core.middlewares.operation.base import get_id_from_json, get_ids_from_param
from app.core.middlewares.operation.comm_const import OPERATION
from app import logger
from app.core.redis.redis_client import create_redis_data
from app.core.constant import BILLING_RESOURCE_PFRFIX


def remove_prefix(url_path: str) -> str:
    """
    如果前缀不存在于路径开头，则返回原路径。
    :param url_path: 原始路径，例如 "/operation/api/operation"
    :return: 去除前缀后的剩余路径，例如 "/api/operation"
    """
    # 确保前缀和路径都是以斜杠开头（标准化）
    for prefix in [app.settings.api_v1_prefix, app.settings.api_kapi_prefix, app.settings.api_v1_prefix_qai]:
        # 确保路径以该前缀开头
        if url_path.startswith(prefix):
            # 去掉匹配的前缀部分
            return url_path[len(prefix):] or "/"
    return url_path


def get_resource_id_origin(first_segment, last_segment, method, param=None, json_data=None):
    """
    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :param param: url的参数
    :param json_data: json数据
    :return:
    """
    # 字典映射参数组合到函数
    func_map = {
        ("notebooks", "sources", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "update", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "stop", "POST"): (get_id_from_json, json_data, "uuids"),
        ("notebooks", "start", "POST"): (get_id_from_json, json_data, "uuids"),
        ("notebooks", "create", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "set_stop_time", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "set_delete_time", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "add_port", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "remove_port", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "add", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "remove", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "change_resource_group", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "change_image", "POST"): (get_id_from_json, json_data, "uuid"),
        ("notebooks", "save", "POST"): (get_id_from_json, json_data, "nb_id"),
        ("keys", "public_key", "DELETE"): (get_ids_from_param, param, "pk_id"),
        ("keys", "public_key", "POST"): (get_id_from_json, json_data, "pk_id"),
        ("trains", "create", "POST"): (get_id_from_json, json_data, "uuid"),
        ("trains", "stop", "POST"): (get_ids_from_param, param, "uuid"),
        ("trains", "start", "POST"): (get_ids_from_param, param, "uuid"),
        ("trains", "DELETE", "DELETE"): (get_ids_from_param, param, "uuid"),
        ("trains", "PATCH", "PATCH"): (get_ids_from_param, param, "uuid"),
        ("trains", "ports", "POST"): (get_ids_from_param, param, "uuid"),
        ("trains", "ports", "DELETE"): (get_ids_from_param, param, "uuid"),
        ("resource", "resource_group", "DELETE"): (get_ids_from_param, param, "rg_id"),
        ("resource", "resource_group", "POST"): (get_id_from_json, json_data, "rg_id"),
        ("resource", "resource_group", "PUT"): (get_id_from_json, json_data, "rg_id"),
        ("resource", "user", "POST"): (get_id_from_json, json_data, "rg_id"),
        ("resource", "user", "DELETE"): (get_ids_from_param, param, "rg_id"),
        ("resource", "template", "DELETE"): (get_ids_from_param, param, "ids"),
        ("resource", "template", "POST"): (get_id_from_json, json_data, "tmp_ids"),
        ("resource", "template", "PUT"): (get_id_from_json, json_data, "tmp_id"),
        ("resource", "node", "DELETE"): (get_ids_from_param, param, "rg_id"),
        ("resource", "node", "POST"): (get_id_from_json, json_data, "rgn_ids"),
        ("resource", "add_node", "POST"): (get_ids_from_param, param, "rg_id"),
        ("resource", "remove_node", "POST"): (get_id_from_json, json_data, "rg_id"),
        ("finetuning", "finetuning", "POST"): (get_id_from_json, json_data, "ft_task_id"),
        # ("billing", "terminate", "GET"): (get_id_from_json, json_data, "resource_ids"),
        # ("billing", "suspend", "GET"): (get_id_from_json, json_data, "resource_ids"),
        # ("billing", "resume", "GET"): (get_id_from_json, json_data, "resource_ids"),
    }
    # 根据参数组合获取并调用对应的函数
    values = func_map.get((first_segment, last_segment, method), (get_ids_from_param, param, "uuid"))

    # 将第一个值作为函数，其余作为参数列表
    func, *func_params = values

    # 调用函数并传递参数
    return func(*func_params)

def get_resource_id(first_segment, last_segment, method, param=None, json_data=None):
    """
    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :param param: url的参数
    :param json_data: json数据
    :return:
    """
    ids = get_resource_id_origin(first_segment, last_segment, method, param, json_data)
    # if first_segment == "billing":
    #     return process_list(ids, BILLING_RESOURCE_PFRFIX)
    # else:
    return ids


def generate_resource_redis_data(url, first_segment, last_segment, method, id, user_id, operation_user_id, trace_id):
    """
    :param url: 请求的url
    :param first_segment: 去掉固定前缀的url的prefix
    :param last_segment: url的最后一段
    :param method: http的方法
    :param id: 资源id
    :param user_id: 用户id
    :param operation_user_id: 操作用户id
    :param trace_id: trace_id
    :return:
    """
    # 字典映射参数组合到函数
    resource_type = url_reflection_type[first_segment]
    operation = OPERATION[resource_type][last_segment + "_" + method]
    start_time = int(datetime.datetime.now().timestamp())
    ret = create_redis_data(user_id, operation_user_id, id, resource_type, operation, start_time, trace_id)
    return ret


def process_list(input_list, prefix):
    # 使用列表推导式处理列表
    return [item[len(prefix):] if item.startswith(prefix) else item for item in input_list]
