# -*- coding: utf-8 -*-
"""
常量类 / 常量
"""

CONTAINER_OPERATION = {
    "create_POST": "create_container",
    "start_POST": "start_container",
    "stop_POST": "stop_container",
    "save_POST": "save_image",
    "notebooks_DELETE": "delete_container",
    "sources_POST": "update_source",
    "update_POST": "update_container",
    "set_stop_time_POST": "set_stop_time_container",
    "set_delete_time_POST": "set_delete_time_container",
    "add_port_POST": "add_port_container",
    "remove_port_POST": "remove_port_container",
    "add_POST": "add_mount_container",
    "remove_POST": "remove_mount_container",
    "change_resource_group_POST": "change_resource_group_container",
    "change_image_POST": "change_image_container",
}

PUBLIC_KEY_OPERATION = {
    "public_key_POST": "create_public_key",
    "public_key_DELETE": "delete_public_key",
}

DISTRIBUTION_JOB_OPERATION = {
    "create_POST": "create_distribution_job",
    "create_code_POST": "create_distribution_job_code_path",
    "start_POST": "start_distribution_job",
    "stop_POST": "stop_distribution_job",
    "DELETE_DELETE": "delete_distribution_job",
    "PATCH_PATCH": "update_distribution_job",
    "PATCH_priority_PATCH": "update_distribution_job_priority",
    "ports_POST": "distribution_job_add_ports",
    "ports_DELETE": "distribution_job_delete_ports",
}


RESOURCE_GROUP_OPERATION = {
    "resource_group_POST": "create_resource_group",
    "resource_group_DELETE": "delete_resource_group",
    "resource_group_PUT": "update_resource_group",
    "user_POST": "resource_group_share_user",
    "user_DELETE": "resource_group_delete_share_user",
    "template_POST": "resource_group_create_template",
    "template_DELETE": "resource_group_delete_template",
    "template_PUT": "resource_group_update_template",
    "node_POST": "resource_group_create_node",
    "node_DELETE": "resource_group_delete_node",
    "add_node_POST": "resource_group_add_node",
    "remove_node_DELETE": "resource_group_remove_node",
}

FINETUNING_OPERATION = {
    "finetuning_POST": "create_finetuning",
}

BILLING_OPERATION = {
    "billing_terminate": "terminate_billing",
    "billing_suspend": "suspend_billing",
    "billing_resume": "resume_billing",
}

OPERATION = {
    "container": CONTAINER_OPERATION,
    "public_key": PUBLIC_KEY_OPERATION,
    "distribution_job": DISTRIBUTION_JOB_OPERATION,
    "resource_group": RESOURCE_GROUP_OPERATION,
    "billing": BILLING_OPERATION,
    "finetuning": FINETUNING_OPERATION,
}
