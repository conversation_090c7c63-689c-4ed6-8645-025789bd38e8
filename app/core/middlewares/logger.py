import contextvars
import time
import uuid

from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.loggers import logger
from app.core.config import request_id_context, user_id_context


class LoggerRequestsIdMiddleware(BaseHTTPMiddleware):
    """
    Middleware to process exceptions
    """

    async def dispatch(self, request: Request, call_next):
        """
        Process request
        :param request:
        :param call_next:
        """
        request_id = str(uuid.uuid4())[:8]
        request_id_context.set(request_id)
        logger.debug(f"request: {request_id} - {request.method} - {request.url.path}")
        start_time = time.time()
        response = await call_next(request)
        if (process_time := (time.time() - start_time)) > 10:
            logger.warning(
                f"Requests too long: {process_time:.2f}s - {request_id} - {request.method} - {request.url.path}")
        response.headers["X-Request-Id"] = request_id
        request_id_context.set(None)
        return response
