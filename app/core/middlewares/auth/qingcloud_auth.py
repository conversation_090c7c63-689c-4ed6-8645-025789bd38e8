import traceback
from typing import Callable, Dict, <PERSON>, <PERSON><PERSON>

from fastapi import <PERSON><PERSON><PERSON>
from starlette.authentication import AuthCredentials, AuthenticationBackend, AuthenticationError, BaseUser
from starlette.requests import HTTPConnection, Request
from starlette.responses import JSONResponse
from starlette.middleware.authentication import AuthenticationMiddleware

from app.core.constant import UserFrom
from app.core.models import QingcloudUser
from app.core.qingcloud.auth import check_auth



class QingcloudAuthBackend(AuthenticationBackend):
    """ Auth Backend for FastAPI """

    def __init__(self, verify_header: Callable[[Dict], Tuple[List[str], BaseUser]], excluded_urls: List[str] = None):
        """ Auth Backend constructor. Part of an AuthenticationMiddleware as backend.

        Args:
            verify_header (callable): A function handle that returns a list of scopes and a BaseUser
            excluded_urls (List[str]): A list of URL paths (e.g. ['/login', '/contact']) the middleware should not check for user credentials ( == public routes)
        """
        self.verify_header = verify_header
        self.excluded_urls = [] if excluded_urls is None else excluded_urls

    def authenticate(self, conn: HTTPConnection) -> <PERSON><PERSON>[AuthCredentials, BaseUser]:
        """ The 'magic' happens here. The authenticate method is invoked each time a route is called that the middleware is applied to.

        Args:
            conn (HTTPConnection): An HTTP connection by FastAPI/Starlette

        Returns:
            Tuple[AuthCredentials, BaseUser]: A tuple of AuthCredentials (scopes) and a user object that is or inherits from BaseUser
        """

        if conn.url.path in self.excluded_urls:
            return AuthCredentials(scopes=[]), BaseUser()

        try:
            scopes, user = self.verify_header(conn)

        except Exception as exception:
            print(f"""Exception in QingcloudAuthBackend.authenticate: {traceback.format_exc()}""")
            raise AuthenticationError(exception) from None

        return AuthCredentials(scopes=scopes), user


# noinspection PyPep8Naming
def QingcloudAuthMiddleware(
        app: FastAPI,
        verify_header: Callable[[str], Tuple[List[str], BaseUser]],
        auth_error_handler: Callable[[Request, AuthenticationError], JSONResponse] = None,
        excluded_urls: List[str] = None
):
    """ Factory method, returning an AuthenticationMiddleware
    Intentionally not named with lower snake case convention as this is a factory method returning a class. Should feel like a class.

    Args:
        app (FastAPI): The FastAPI instance the middleware should be applied to. The `add_middleware` function of FastAPI adds the app as first argument by default.
        verify_header (Callable[[str], Tuple[List[str], BaseUser]]): A function handle that returns a list of scopes and a BaseUser
        auth_error_handler (Callable[[Request, Exception], JSONResponse]): Optional error handler for creating responses when an exception was raised in verify_authorization_header
        excluded_urls (List[str]): A list of URL paths (e.g. ['/login', '/contact']) the middleware should not check for user credentials ( == public routes)

    Examples:
        ```python
        def verify_authorization_header(auth_header: str) -> Tuple[List[str], FastAPIUser]:
            scopes = ["admin"]
            user = FastAPIUser(first_name="Code", last_name="Specialist", user_id=1)
            return scopes, user

        app = FastAPI()
        app.add_middleware(AuthMiddleware, verify_authorization_header=verify_authorization_header)
        ```
    """
    return AuthenticationMiddleware(app, backend=QingcloudAuthBackend(verify_header=verify_header,
                                                                      excluded_urls=excluded_urls),
                                    on_error=auth_error_handler)


def verify_auth(conn: HTTPConnection) -> Tuple[List[str], BaseUser]:
    """ Sample verify_authorization_header function that checks for a valid authorization header and returns a user object

    Args:
        conn (HTTPConnection): An HTTP connection by FastAPI/Starlette
    """
    userinfo = check_auth(conn)
    return [userinfo["role"]], QingcloudUser(**userinfo, user_from=UserFrom.CONSOLE)
