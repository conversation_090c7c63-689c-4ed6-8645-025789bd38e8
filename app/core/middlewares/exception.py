from typing import Dict

from fastapi import Request, Response
from fastapi.responses import J<PERSON><PERSON><PERSON>ponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from app.core.exceptions import AICPBaseException, InternalServerErrorException, internal_server_error
from app.core.loggers import logger
from app.core.response import AI<PERSON><PERSON><PERSON><PERSON><PERSON>po<PERSON>, BaseErrorResponse


class ProcessExceptionMiddleware(BaseHTTPMiddleware):
    """
    Middleware to process exceptions
    """

    def dispatch(self, request: Request, call_next):
        """
        Process request
        :param request:
        :param call_next:
        """
        try:
            response = call_next(request)
        except AICPBaseException as e:
            # 权限日志等级为warning
            if e.BASE_CODE == 1400:
                logger.warning(f"process request error - {e.code} - {e.message}")
                return BaseErrorResponse(**e.dict)
            logger.error(f"process request error - {e.code} - {e.message}")
            return BaseErrorResponse(**e.dict)
        except Exception as e:
            logger.error(f"process request error : {e}")
            return BaseErrorResponse(**InternalServerErrorException().dict)
        return response


def process_exception_middleware(request: Request, exc: AICPBaseException):
    """
    Process request
    :param request:
    :param call_next:
    """
    return AICPJSONResponse(exc.dict)


def process_base_exception_middleware(request: Request, exc: BaseException):
    """
    Process request
    :param request:
    :param call_next:
    """
    # 适配middleware层关于aicp的报错
    if not isinstance(exc, AICPBaseException):
        logger.exception(f"process request error : {exc}")
    return AICPJSONResponse(internal_server_error.dict)
