from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware

import app
from app import logger
from app.core.models import QingcloudUser
from app.core.utils import is_exclude_url

from .auth.qingcloud_auth import verify_auth
from ..config import user_id_context
from ..constant import UserFrom


class ProcessAuthnMiddleware(BaseHTTPMiddleware):
    """
    Middleware to process exceptions
    """

    async def dispatch(self, request: Request, call_next):
        """
        Process request
        :param request:
        :param call_next:
        """
        if request.headers.get("X-REMOTE-GROUP") == "system:authenticated" and request.headers.get(
                "X-REMOTE-USER") == "admin":
            logger.info("User is kse admin")
            # if user is kse admin, then set user as global_admin
            kse_user = {
                'user_id': 'user-kse-admin',
                'role': 'global_admin',
                'user_name': 'user-kse-admin',
                "privilege": 10,
                'email': 'user-kse-admin',
                'phone': '',
                'root_user_id': 'user-kse-admin',
                'access_key_id': app.settings.qingcloud_access_key_id,
                'secret_access_key': app.settings.qingcloud_secret_access_key,
                'user_from': UserFrom.KSE
            }
            request.scope["auth"], request.scope["user"] = ["global_admin"], QingcloudUser(**kse_user)
        elif is_exclude_url(request.url.path):
            logger.info("User is white list user")
            white_list_user = {
                'user_id': 'user-white-list',
                'role': 'global_admin',
                'user_name': 'user-white-list',
                "privilege": 10,
                'email': 'user-white-list',
                'phone': '',
                'root_user_id': 'user-white-list',
                'access_key_id': app.settings.qingcloud_access_key_id,
                'secret_access_key': app.settings.qingcloud_secret_access_key,
                'user_from': UserFrom.KSE
            }
            request.scope["auth"], request.scope["user"] = ["global_admin"], QingcloudUser(**white_list_user)
        else:
            request.scope["auth"], request.scope["user"] = verify_auth(request)
            logger.info("User is normal")

        user_id_context.set(request.scope["user"].user_id)
        response = await call_next(request)
        user_id_context.set(None)
        return response
