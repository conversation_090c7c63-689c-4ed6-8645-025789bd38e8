repository: hub.kubesphere.com.cn
image: aicp/aicp-api-server
tag: v2.4.0-beta-1

configMap:
  AI_CLOUD_PUSH_SERVER: 'http://push-server-service.aicp-system:32149/push/ms'
  DOCKER_REGISTRY: hd1-dockerhub.coreshub.cn
  LOCAL_STORAGE_ENABLE: 'true'
  MINIO_HOST: *************
  MINIO_POLICY_TMP_DIR: /tmp
  MINIO_PORT: '30900'
  MINIO_PROTOCOL: http
  MINIO_SC_ENDPOINT: "http://*************:30900"
  MINIO_SECURE: 'false'
  NOTEBOOK_HOST: 'https://ai.coreshub.cn/hd1/notebook/{server_type}/{namespace}/{name}/'
  QINGCLOUD_GPFS_DEBUG: 'true'
  QINGCLOUD_GPFS_ENABLED: 'false'
  QINGCLOUD_GPFS_SERVER: 'http://epfs-server-service.pitrix.svc:32151/'
  QINGCLOUD_GPSE_FILESYSTEM: fs1
  QINGCLOUD_GPSE_VOLUME_HANDLE: '0;2;13877686814292254296;0865A8C0:665A0C11;;{file_set};/share/{file_set}'
  QINGCLOUD_GPSE_ZONE: sh1
  TENSORBOARD_ENABLE: 'true'
  TENSORBOARD_URL: 'https://ai.coreshub.cn/hd1/tensorboard/{namespace}/{name}/'
  UFM_ADDRESS: ''
  UFM_ENABLE: 'false'
  UFM_PASSWORD: Zhu_88jie
  UFM_USERNAME: admin
  exclude_url: '["/healthz","^/kapis/.*$"]'
  qingcloud_access_key_id: need_to_change
  qingcloud_host: api.coreshub.cn
  qingcloud_port: '443'
  qingcloud_protocol: https
  qingcloud_secret_access_key: need_to_change
  qingcloud_zone: hd1
  default_console_id: coreshub
  default_regin_id: hd1
  
nodeSelector: 
  node-role.kubernetes.io/master: ""

kubernetesClusterDomain: cluster.local

serviceAccount:
  serviceAccount:
    annotations: {}


