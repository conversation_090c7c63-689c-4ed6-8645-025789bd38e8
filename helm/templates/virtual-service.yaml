apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  name: aicp-web-app
  labels:
  {{- include "helm.labels" . | nindent 4 }}
spec:
  gateways:
  - aicp-gateway
  hosts:
  - '*'
  http:
  - headers:
      request:
        add:
          x-forwarded-prefix: /aicp
    match:
    - uri:
        regex: ^\/(aicp|kapis).*
    name: aicp-web-app
    route:
    - destination:
        host: aicp-web-app
        subset: production