apiVersion: apps/v1
kind: Deployment
metadata:
  name: aicp-web-app
  labels:
    app: aicp-web-app
    version: production
  {{- include "helm.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aicp-web-app
      version: production
    {{- include "helm.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: aicp-web-app
        version: production
      {{- include "helm.selectorLabels" . | nindent 8 }}
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      nodeSelector:
        {{- toYaml .Values.nodeSelector | nindent 8 }}
      containers:
      - env:
        - name: TZ
          value: Asia/Shanghai
        envFrom:
        - configMapRef:
            name: aicp-web-app-parameters
        image: "{{ .Values.repository }}/{{ .Values.image }}:{{ .Values.tag | default .Chart.AppVersion }}"
        imagePullPolicy: Always
        name: aicp-web-app
        ports:
        - containerPort: 5000
        resources: {}
        volumeMounts:
        - mountPath: /code/migration/versions
          name: aicp-web-app-volume
      serviceAccountName: aicp-service-account
      volumes:
      - name: aicp-web-app-volume
        persistentVolumeClaim:
          claimName: aicp-alembic-version-pvc