apiVersion: batch/v1
kind: Job
metadata:
  name: "{{ .Release.Name }}-post-upgrade-job"
  namespace: "{{ .Release.Namespace }}"
  labels:
    app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
    app.kubernetes.io/instance: {{ .Release.Name | quote }}
    app.kubernetes.io/version: {{ .Chart.AppVersion }}
    helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
  annotations:
    # This is what defines this resource as a hook. Without this line, the
    # job is considered part of the release.
    "helm.sh/hook": post-upgrade
    "helm.sh/hook-weight": "-5"
    "helm.sh/hook-delete-policy": hook-succeeded
spec:
  template:
    metadata:
      name: "{{ .Release.Name }}"
      labels:
        app.kubernetes.io/managed-by: {{ .Release.Service | quote }}
        app.kubernetes.io/instance: {{ .Release.Name | quote }}
        helm.sh/chart: "{{ .Chart.Name }}-{{ .Chart.Version }}"
    spec:
      serviceAccountName: aicp-service-account
      restartPolicy: Never
      containers:
        - name: post-install-job
          image: "{{ .Values.repository }}/{{ .Values.image }}:{{ .Values.tag | default .Chart.AppVersion }}"
          command:
            - bash
            - /code/app/jobs/post-upgrade.sh
          envFrom:
            - configMapRef:
                name: aicp-web-app-parameters
          volumeMounts:
            - name: aicp-web-app-volume
              mountPath: /code/migration/versions
      volumes:
        - name: aicp-web-app-volume
          persistentVolumeClaim:
            claimName: aicp-alembic-version-pvc
