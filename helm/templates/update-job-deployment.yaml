apiVersion: apps/v1
kind: Deployment
metadata:
  name: aicp-update-job
  labels:
    app: aicp-update-job
    {{- include "helm.labels" . | nindent 4 }}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: aicp-update-job
      {{- include "helm.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      labels:
        app: aicp-update-job
        {{- include "helm.selectorLabels" . | nindent 8 }}
      annotations:
        sidecar.istio.io/inject: "true"
    spec:
      containers:
      - command:
        - python
        - /code/app/jobs/update_status.py
        env:
        - name: TZ
          value: Asia/Shanghai
        envFrom:
        - configMapRef:
            name: aicp-web-app-parameters
        image: "{{ .Values.repository }}/{{ .Values.image }}:{{ .Values.tag | default .Chart.AppVersion }}"
        imagePullPolicy: Always
        name: aicp-update-job
        resources: {}
        workingDir: /code/
      serviceAccountName: aicp-service-account