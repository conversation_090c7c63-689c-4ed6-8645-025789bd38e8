apiVersion: v1
kind: Secret
metadata:
  name: minio-admin
  namespace: {{ .Release.Namespace }}
stringData:
  accessKeyID: "aicp"
  secretAccessKey: "2a10auQpFvxSjg85JrTg4Iw8X"
  endpoint: "{{ .Values.configMap.MINIO_SC_ENDPOINT }}"

---
kind: StorageClass
apiVersion: storage.k8s.io/v1
metadata:
  name: csi-s3
provisioner: ru.yandex.s3.csi
parameters:
  # specify which mounter to use
  # can be set to rclone, s3fs, goofys or s3backer
  mounter: geesefs
  options: --memory-limit 1000 --dir-mode 0777 --file-mode 0666
  # to use an existing bucket, specify it here:
  # bucket: some-existing-bucket
  csi.storage.k8s.io/provisioner-secret-name: minio-admin
  csi.storage.k8s.io/provisioner-secret-namespace: aicp-system
  csi.storage.k8s.io/controller-publish-secret-name: minio-admin
  csi.storage.k8s.io/controller-publish-secret-namespace: aicp-system
  csi.storage.k8s.io/node-stage-secret-name: minio-admin
  csi.storage.k8s.io/node-stage-secret-namespace: aicp-system
  csi.storage.k8s.io/node-publish-secret-name: minio-admin
  csi.storage.k8s.io/node-publish-secret-namespace: aicp-system

---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: aicp-alembic-version-pvc
  labels:
  {{- include "helm.labels" . | nindent 4 }}
  namespace: {{ .Release.Namespace }}
spec:
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 1Gi
  storageClassName: csi-s3


