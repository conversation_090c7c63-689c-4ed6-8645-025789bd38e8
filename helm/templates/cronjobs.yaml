apiVersion: batch/v1
kind: CronJob
metadata:
  name: aicp-cron-job-by-minutes
  labels:
  {{- include "helm.labels" . | nindent 4 }}
spec:
  concurrencyPolicy: Forbid
  schedule: "*/1 * * * *"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 1
      completions: 1
      parallelism: 1
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          containers:
          - command:
            - python3
            - /code/app/jobs/cron_jobs/by_minutes.py
            env:
            - name: TZ
              value: Asia/Shanghai
            envFrom:
            - configMapRef:
                name: aicp-web-app-parameters
            image: "{{ .Values.repository }}/{{ .Values.image }}:{{ .Values.tag | default .Chart.AppVersion }}"
            imagePullPolicy: Always
            name: cron-job
            resources: {}
            workingDir: /code
          restartPolicy: Never
          serviceAccountName: aicp-service-account
      ttlSecondsAfterFinished: 300
---
apiVersion: batch/v1
kind: CronJob
metadata:
  name: aicp-cron-job-by-day
  labels:
  {{- include "helm.labels" . | nindent 4 }}
spec:
  concurrencyPolicy: Forbid
  schedule: "* 0 * * *"
  successfulJobsHistoryLimit: 1
  failedJobsHistoryLimit: 1
  jobTemplate:
    spec:
      backoffLimit: 1
      completions: 1
      parallelism: 1
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          containers:
          - command:
            - python3
            - /code/app/jobs/cron_jobs/cleanup.py
            env:
            - name: TZ
              value: Asia/Shanghai
            envFrom:
            - configMapRef:
                name: aicp-web-app-parameters
            image: "{{ .Values.repository }}/{{ .Values.image }}:{{ .Values.tag | default .Chart.AppVersion }}"
            name: cron-job
            resources: {}
            workingDir: /code
          restartPolicy: Never
          serviceAccountName: aicp-service-account
      ttlSecondsAfterFinished: 300