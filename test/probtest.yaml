apiVersion: monitoring.coreos.com/v1
kind: Probe
metadata:
  name: nb-duseqhtb47wg
  namespace: usr-cjn97fnr
spec:
  jobName: prometheus-blackbox-exporter-http-probe
  interval: 30s
  module: tcp_connect
  prober:
    url: prometheus-blackbox-exporter.kubesphere-monitoring-system.svc:9115
    scheme: http
    path: /probe
  targets:
    staticConfig:
      static:
        - nb-duseqhtb47wg.namespace1.svc:80
        - nb-duseqhtb47wg.namespace2.svc:90
        - nb-duseqhtb47wg.namespace3.svc:22
      labels:
        notebook: nb-duseqhtb47wg
      relabelingConfigs:
        - sourceLabels: [ __address__ ]
          regex: '([^.]+)\.([^.]+)\.svc:(.*)'
          targetLabel: port
          replacement: '$3'
