import asyncio
import socket
from collections import defaultdict
from contextlib import contextmanager
from time import perf_counter
from timeit import timeit
from typing import Any, Collection, Generator
from abc import ABC, abstractmethod
from time import ctime, time


class AsyncTCPScanner:
    """Perform asynchronous TCP-connect scans on collections of target
    hosts and ports."""

    def __init__(self,
                 targets: Collection[str],
                 ports: Collection[int],
                 timeout: float):
        """
        Args:
            targets (Collection[str]): A collection of strings
                containing a sequence of IP addresses and/or domain
                names.
            ports (Collection[int]): A collection of integers containing
                a sequence of valid port numbers as defined by
                IETF RFC 6335.
            timeout (float): Time to wait for a response from a target
                before closing a connection to it. Setting this to too
                short an interval may prevent the scanner from waiting
                the time necessary to receive a valid response from a
                valid server, generating a false-negative by identifying
                a result as a timeout too soon. Recommended setting to
                a minimum of 10 seconds.
        """
        self.targets = targets
        self.ports = ports
        self.timeout = timeout
        self.results = defaultdict(dict)
        self.total_time = float()
        self._loop = asyncio.get_event_loop()
        self._observers = list()

    @property
    def _scan_tasks(self):
        """Set up a scan coroutine for each pair of target address and
        port."""
        return [self._scan_target_port(target, port) for port in self.ports
                for target in self.targets]

    @contextmanager
    def _timer(self):
        """Measure the total time taken by the scan operation."""
        start_time: float = perf_counter()
        yield
        self.total_time = perf_counter() - start_time

    def register(self, observer):
        """Register a class that implements the interface of
        Output as an observer."""
        self._observers.append(observer)

    async def _notify_all(self):
        """Notify all registered observers that the scan results are
        ready to be pulled and processed."""
        [asyncio.create_task(observer.update()) for observer in self._observers]

    async def _scan_target_port(self, address: str, port: int) -> None:
        """Execute a TCP handshake on a target port and add the result
        to a JSON data structure of the form:
        {
            'example.com': {
                22: ('closed', 'ssh', 'Connection refused'),
                80: ('open', 'http', 'SYN/ACK')
            }
        }
        """

        try:
            await asyncio.wait_for(
                asyncio.open_connection(address, port),
                timeout=self.timeout
            )
            port_state, reason = 'open', 'SYN/ACK'
        except (ConnectionRefusedError, asyncio.TimeoutError, OSError) as e:
            reasons = {
                'ConnectionRefusedError': 'Connection refused',
                'TimeoutError': 'No response',
                'OSError': 'Network error'
            }
            port_state, reason = 'closed', reasons[e.__class__.__name__]
        try:
            service = socket.getservbyport(port)
        except OSError:
            service = 'unknown'
        self.results[address].update({port: (port_state, service, reason)})

    def execute(self):
        with self._timer():
            self._loop.run_until_complete(asyncio.wait(self._scan_tasks))
        self._loop.run_until_complete(self._notify_all())


class Output(ABC):
    """
    Interface for the implementation of all classes responsible for
    further processing and/or output of the information gathered by
    the AsyncTCPScanner class.
    """

    def __init__(self, subject):
        subject.register(self)

    @abstractmethod
    async def update(self, *args, **kwargs) -> None:
        pass


def _parse_ports(port_seq: str) -> Generator[int, Any, None]:
    """Yield an iterator with integers extracted from a string
    consisting of mixed port numbers and/or ranged intervals.
    Ex: From '20-25,53,80,111' to (20,21,22,23,24,25,53,80,111)
    """
    for port in port_seq.split(','):
        try:
            port = int(port)
            if not 0 < port < 65536:
                raise SystemExit(f'Error: Invalid port number {port}.')
            yield port
        except ValueError:
            start, end = (int(port) for port in port.split('-'))
            yield from range(start, end + 1)


class OutputToScreen(Output):
    def __init__(self, subject, show_open_only: bool = False):
        super().__init__(subject)
        self.scan = subject
        self.open_only = show_open_only

    async def update(self) -> None:
        all_targets: str = ' | '.join(self.scan.targets)
        num_ports: int = len(self.scan.ports) * len(self.scan.targets)
        output: str = '    {: ^8}{: ^12}{: ^12}{: ^12}'

        print(f'Starting Async Port Scanner at {ctime(time())}')
        print(f'Scan report for {all_targets}')

        for address in self.scan.results.keys():
            print(f'\n[>] Results for {address}:')
            print(output.format('PORT', 'STATE', 'SERVICE', 'REASON'))
            for port, port_info in sorted(self.scan.results[address].items()):
                if self.open_only is True and port_info[0] == 'closed':
                    continue
                print(output.format(port, *port_info))

        print(f"\nAsync TCP Connect scan of {num_ports} ports for "
              f"{all_targets} completed in {self.scan.total_time:.2f} seconds")

        await asyncio.sleep(0)


# scanner = AsyncTCPScanner(targets=tuple("nb-dt9sv6avcow0.usr-aamp56so.svc"),
#                           ports=tuple(_parse_ports("8888,8889,22")),
#                           timeout=10)
# to_screen = OutputToScreen(subject=scanner)
# scanner.execute()

def is_port_open(host:str, port: int) -> bool:
    import socket
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        return s.connect_ex((host, port)) == 0

from random import SystemRandom

def run(tasks, *, loop=None):
    if loop is None:
        loop = asyncio.get_event_loop()
    # waiting for all tasks
    return loop.run_until_complete(asyncio.wait(tasks))

async def scanner(ip, port, loop=None):
    fut = asyncio.open_connection(ip, port, loop=loop)
    try:
        reader, writer = await asyncio.wait_for(fut, timeout=0.5) # This is where it is blocking?
        print("{}:{} Connected".format(ip, port))
    except asyncio.TimeoutError:
        pass
    # handle connection refused and bunch of others
    except Exception as exc:
        print('Error {}:{} {}'.format(ip, port, exc))

def scan(ips, ports, randomize=False):
    loop = asyncio.get_event_loop()
    if randomize:
        rdev = SystemRandom()
        ips = rdev.shuffle(ips)
        ports = rdev.shuffle(ports)

    # let's pass list of task, not only one
    run([scanner(ip, port) for port in ports for ip in ips])

ips = ["nb-dt9sv6avcow0.usr-aamp56so.svc"]
ports = [22, 80, 81]
scan(ips, ports)
