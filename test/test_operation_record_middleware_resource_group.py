import pytest
from fastapi.testclient import Test<PERSON>lient
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.middlewares.operation_record import OperationRecordMiddleware

@pytest.fixture
def mock_app():
    app = FastAPI()
    return app

@pytest.fixture
def middleware(mock_app):
    return OperationRecordMiddleware(mock_app)

@pytest.fixture
def mock_call_next():
    async def _mock_call_next(request: Request):
        return Response(content="mock response", media_type="application/json")
    return AsyncMock(side_effect=_mock_call_next)

@pytest.mark.asyncio
async def test_dispatch_create_resource_group(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/resource_group"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"name": "test-rg"}'
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_delete_resource_group(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/resource_group/test-rg"
    mock_request.method = "DELETE"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_add_node_to_resource_group(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/resource_group/test-rg/node"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"node": "test-node"}'
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_remove_node_from_resource_group(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/resource_group/test-rg/node/test-node"
    mock_request.method = "DELETE"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_get_resource_group_nodes(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/resource_group/test-rg/nodes"
    mock_request.method = "GET"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
