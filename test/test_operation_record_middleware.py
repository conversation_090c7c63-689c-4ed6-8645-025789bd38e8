import pytest
from fastapi.testclient import Test<PERSON>lient
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.middlewares.operation_record import OperationRecordMiddleware

@pytest.fixture
def mock_app():
    app = FastAPI()
    return app

@pytest.fixture
def middleware(mock_app):
    return OperationRecordMiddleware(mock_app)

@pytest.fixture
def mock_call_next():
    async def _mock_call_next(request: Request):
        return Response(content="mock response", media_type="application/json")
    return AsyncMock(side_effect=_mock_call_next)

@pytest.mark.asyncio
async def test_dispatch_with_billing_endpoint(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/billing/test"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.query_params = {"user_id": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_with_admin_header(middleware, mock_call_next):
    # Mock admin request
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/test"
    mock_request.method = "POST"
    mock_request.headers = {
        "X-Remote-Group": "system:authenticated",
        "X-Remote-User": "admin"
    }
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
    assert mock_dispatch.call_args[0][0].headers["X-Remote-User"] == "admin"

@pytest.mark.asyncio        
async def test_dispatch_streaming_response(middleware, mock_call_next):
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/test"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b"test body"
    
    # Mock streaming response
    async def stream_gen():
        yield b"stream data"
        
    mock_response = AsyncMock()
    mock_response.body_iterator = stream_gen()
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
        
        mock_dispatch.assert_called_once()
        
@pytest.mark.asyncio
async def test_dispatch_process_response_body(middleware, mock_call_next):
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/test"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    # Mock JSON response
    mock_response = AsyncMock()
    mock_response.body.return_value = b'{"key":"value"}'
    
    with patch("app.core.middlewares.operation_record.logger.info") as mock_logger:
        await middleware.dispatch(mock_request, mock_call_next)
        
        assert mock_logger.call_count >= 1

@pytest.mark.asyncio
async def test_dispatch_invalid_json_response(middleware, mock_call_next):
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/test" 
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    # Mock invalid JSON response
    mock_response = AsyncMock()
    mock_response.body.return_value = b"invalid json"
    
    with patch("app.core.middlewares.operation_record.logger.info") as mock_logger:
        await middleware.dispatch(mock_request, mock_call_next)
        
        # Verify raw response is logged
        assert mock_logger.call_count >= 1

@pytest.mark.asyncio
async def test_dispatch_operation_record_error(middleware, mock_call_next):
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/test"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    # Mock error in process_operation
    with patch("app.core.middlewares.operation_record.logger.error") as mock_logger:
        await middleware.dispatch(mock_request, mock_call_next)
        
        # Verify error is logged
        mock_logger.assert_called_once()
