import pickle

from app.core.kube.api.kfam import KfamClient
from app.core.redis.redis_client import KfamCacheClient

if __name__ == "__main__":
    workgroup_info = KfamClient("usr-WJ2gdXUg").get_workgroup_info()
    # print(workgroup_info)
    # redis_client = KfamCacheClient()
    # redis_client.set_work_group_cache("usr-WJ2gdXUg-test", pickle.dumps(workgroup))
    # workgroup_info = redis_client.get_work_group_cache("usr-WJ2gdXUg-test")
    print(workgroup_info, type(workgroup_info))
    print(111111)
