import json
import unittest

import kr8s

from app import settings
from app.core.kube.api.kfam import KfamClient
from app.core.models import QingcloudUser
from app.core.volumes.aicp_minio import minio_admin_client


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    def test_create_storage(self):
        pass

    def test_exec(self):
        from app.core.kube.api.apis import v1_core
        from kubernetes.stream import stream
        resp = stream(
            v1_core.connect_get_namespaced_pod_exec,
            "tensorboard-111", "usr-inbwhptp",
            container="tensorboard", command=["sh"],
            stderr=True, stdin=False,
            stdout=True, tty=False,
            _preload_content=False
        )
        while resp.is_open():
            resp.update(timeout=1)
            if resp.peek_stdout():
                print(f"STDOUT: {resp.read_stdout()}")
            if resp.peek_stderr():
                print(f"STDERR: {resp.read_stderr()}")

            c = input("Enter command: ")
            print(f"Running command... {c}\n")
            if c == "exit":
                break
            resp.write_stdin(c + "\n")

        resp.close()

    def test_scale(self):
        from app.core.volumes.objects import StorageClass
        sc_list = kr8s.get("storageclass", "csi-s3")
        print(sc_list)

    def test_minio_admin(self):
        print(minio_admin_client.bucket_quota_get("usr-lxapexlz"))


if __name__ == '__main__':
    unittest.main()
