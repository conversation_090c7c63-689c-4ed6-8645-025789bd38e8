import requests
import subprocess

# Docker Hub API endpoint for aicphub
DOCKER_HUB_API_URL = "https://hub.docker.com/v2/repositories/aicphub/"
TARGET_REPOSITORY = "hub.kubesphere.com.cn/aicp"
PROXIES = {
    "http": "http://127.0.0.1:10808",
    "https": "http://127.0.0.1:10808"
}


def get_all_images_and_tags():
    images_tags = {}
    url = DOCKER_HUB_API_URL
    while url:
        response = requests.get(url, proxies=PROXIES, verify=False)
        data = response.json()
        for repo in data['results']:
            repo_name = repo['name']
            print(f"Getting tags for {repo_name}")
            tags_url = f"{DOCKER_HUB_API_URL}{repo_name}/tags/"
            tags_response = requests.get(tags_url, proxies=PROXIES, verify=False)
            tags_data = tags_response.json()
            tags = [tag['name'] for tag in tags_data['results']]
            images_tags[repo_name] = tags
        url = data['next']  # Handle pagination
    return images_tags


def sync_images_to_target_repo(images_tags):
    for image, tags in images_tags.items():
        for tag in tags:
            source = f"docker.io/aicphub/{image}:{tag}"
            target = f"{TARGET_REPOSITORY}/{image}:{tag}"
            print(f"Syncing {source} to {target}")
            # Sync using oras
            subprocess.run(["oras", "copy", source, target], check=True)


if __name__ == "__main__":
    images_tags = get_all_images_and_tags()
    sync_images_to_target_repo(images_tags)
