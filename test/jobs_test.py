import json
import unittest

import kr8s
from kr8s.objects import Pod, Event
from kubernetes.client import V1N<PERSON>, V1NodeList

from app import settings
from app.core.kube.kr8s.objects import AuthorizationPolicy


from app.core.kube.api import list_nodes, v1_core
from app.core.qingcloud.billing import BillingService
from app.core.qingcloud.interface import IaasClient, product_attr_query_request
from app.core.utils import asyncify_methods, gpu_manager


# from app.core.kube.objects import *  # noqa


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.k8s_client = kr8s

    def test_list_tf_jobs(self):
        tfjobs = self.k8s_client.get("tfjobs", namespace=kr8s.ALL)
        print(tfjobs)

    def test_list_mx_jobs(self):
        mxjobs = self.k8s_client.get("mxjobs", namespace=kr8s.ALL)
        print(mxjobs)

    def test_list_pytorch_jobs(self):
        pytorchjobs = self.k8s_client.get("pytorchjobs", namespace=kr8s.ALL)
        print(pytorchjobs)

    def test_list_xgboost_jobs(self):
        xgboostjobs = self.k8s_client.get("xgboostjobs", namespace=kr8s.ALL)
        print(xgboostjobs)

    def test_list_mpi_jobs(self):
        mpijobs = self.k8s_client.get("mpijobs", namespace=kr8s.ALL)
        print(mpijobs)

    def test_list_padddle_jobs(self):
        paddlejobs = self.k8s_client.get("paddlejobs", namespace=kr8s.ALL)
        print(paddlejobs)

    def test_list_jobs3(self):
        # print(kr8s.get("pods", namespace=kr8s.ALL))
        r = kr8s.get("tfjobs,mpijobs", namespace=kr8s.ALL)
        print(r)

    def test_get_secret(self):
        from kr8s.objects import Secret
        s: Secret = Secret.get("aicp.aicp-pg.credentials.postgresql.acid.zalan.do", namespace="aicp-system")
        import base64
        print(base64.b64decode(s.data["password"]))
        print(base64.b64decode(s.data["username"]))

    def test_get_job(self):
        r = TfJob.get("tfjob-simple", namespace="kubeflow")
        print(r.raw)

    def test_get_trains_pod(self):
        r = kr8s.get("pods", namespace="kubeflow",
                     label_selector={"training.kubeflow.org/job-name": "tfjob-simple"})
        print(json.dumps([i.raw for i in r]))

        # from kr8s.objects import Pod
        # r = Pod.get(namespace="kubeflow", label_selector={"training.kubeflow.org/job-name": "tfjob-simple"})
        # print([i.raw for i in r])

    def test_get_trains_pod_log(self):
        from kr8s.objects import Pod
        r = Pod.get(name="tfjob-simple-worker-0", namespace="kubeflow")
        print(r.raw)
        try:
            for line in r.logs(container="tensorflow", follow=True):
                print(line)
        except Exception as e:
            print(e)

    def test_get_trains_pod_event(self):
        field_selector = {"involvedObject.name": "tfjob-simple-1", "involvedObject.kind": "TFJob"}

        r = kr8s.get("events", namespace="kubeflow", field_selector=field_selector)
        print(json.dumps([i.raw for i in r]))

    def test_get_list(self):
        from app.core.kube.api import list_custom_rsrc
        from kr8s.objects import APIObject
        class ImageBuilder(APIObject):
            """MXJob is the definition of a MXJob.
            """

            version = "imagebuilder.ai.qingcloud.com/v1"
            endpoint = "imagebuilders"
            kind = "ImageBuilder"
            plural = "imagebuilders"
            singular = "imagebuilder"
            namespaced = True

        # ImageBuilder.get()
        # print(kr8s.get("imagebuilders", "example", namespace="default"))
        print(list_custom_rsrc("imagebuilder.ai.qingcloud.com", "v1", "imagebuilders", "default"))

    def test_pytorchJob(self):
        from app.apps.trains.kr8s_objects.pytorchjob import PyTorchJob
        tmp = {
            "apiVersion": "kubeflow.org/v1",
            "kind": "PyTorchJob",
            "metadata": {
                "name": "tn-dgwh9eus1k3k-1",
                "labels": {
                    "app": "tn-dgwh9eus1k3k",
                    "user": "usr-C3YU7Mr7",
                    "user_name": "",
                    "train_uuid": "tn-dgwh9eus1k3k"
                },
                "annotations": {
                    "sidecar.istio.io/inject": "false"
                }
            },
            "spec": {
                "elasticPolicy": {
                    "rdzv": "c10d",
                    "minReplicas": 2,
                    "maxReplicas": 2,
                    "maxRestarts": 1
                },
                # "runPolicy": {
                #     "ttlSecondsAfterFinished": 1800,
                #     "backoffLimit": 1,
                #     "activeDeadlineSeconds": 3600
                # },
                "pytorchReplicaSpecs": {
                    "Worker": {
                        "replicas": 2,
                        "template": {
                            "metadata": {
                                "annotations": {
                                    "sidecar.istio.io/inject": "false"
                                },
                                "labels": {
                                    "app": "tn-dgwh9eus1k3k",
                                    "user": "usr-C3YU7Mr7",
                                    "user_name": "",
                                    "train_uuid": "tn-dgwh9eus1k3k"
                                }
                            },
                            "spec": {
                                "volumes": [
                                    {
                                        "emptyDir": {
                                            "sizeLimit": "1Gi",
                                            "medium": "Memory"
                                        },
                                        "name": "dshm"
                                    },
                                    {
                                        "name": "default",
                                        "persistentVolumeClaim": {
                                            "claimName": "aicp-oss-pvc-usr-c3yu7mr7"
                                        }
                                    }
                                ],
                                "containers": [
                                    {
                                        "name": "pytorch",
                                        "imagePullPolicy": "Always",
                                        "image": "j1-dockerhub.qingcloud.com/public/tverous:pytorch-notebook",
                                        "command": [
                                            "/bin/sh",
                                            "-c",
                                            "--"
                                        ],
                                        "args": [
                                            "sleep 1000"
                                        ],
                                        "resources": {
                                            "requests": {
                                                "memory": "8Gi",
                                                "cpu": 4
                                            },
                                            "limits": {
                                                "memory": "8Gi",
                                                "cpu": 4
                                            }
                                        },
                                        "volumeMounts": [
                                            {
                                                "mountPath": "/dev/shm",
                                                "name": "dshm"
                                            },
                                            {
                                                "mountPath": "/logs/tensorboard",
                                                "name": "default",
                                                "subPath": "tn-dgwh9eus1k3k"
                                            },
                                            {
                                                "mountPath": "/root/code",
                                                "name": "default",
                                                "subPath": "458d4a6a-622e-4393-a3f6-a419e6578032"
                                            }
                                        ],
                                        "env": [
                                            {
                                                "name": "TENSORBOARD_LOG_PATH",
                                                "value": "/logs/tensorboard"
                                            }
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        }
        pt = PyTorchJob(tmp)
        pt.create()

    def test_shell_job(self):
        shell_text = "cd /opt && ls -al && pwd && sleep 300"
        from app.apps.trains.kr8s_objects.pytorchjob import PyTorchJob
        tmp = {
            "apiVersion": "kubeflow.org/v1",
            "kind": "PyTorchJob",
            "metadata": {
                "name": "tn-dgwh9eus1k3k-3",
                "namespace": "usr-rticrzez",
                "annotations": {
                    "sidecar.istio.io/inject": "false"
                }
            },
            "spec": {
                "pytorchReplicaSpecs": {
                    "Worker": {
                        "replicas": 1,
                        "template": {
                            "metadata": {
                                "annotations": {
                                    "sidecar.istio.io/inject": "false"
                                },
                            },
                            "spec": {
                                "containers": [
                                    {
                                        "name": "pytorch",
                                        "imagePullPolicy": "Always",
                                        "image": "j1-dockerhub.qingcloud.com/public/tverous:pytorch-notebook",
                                        "command": [
                                            "/bin/sh",
                                            "-c",
                                            shell_text
                                        ],
                                    }
                                ]
                            }
                        }
                    }
                }
            }
        }
        pt = PyTorchJob(tmp)
        pt.create()

    def test_list_node(self):
        nodes: V1NodeList = list_nodes(label_selector="aicp.group/aipods_type")
        node: V1Node

        r = {}

        # 检查存在异常污点的节点
        def check_taints(taints):
            if not taints:
                return True
            if len(taints) > 1:
                return False
            if taints[0]['key'] != "aicp.group/worker":
                return False

        print(list(filter(lambda x: check_taints(x.spec.taints), nodes.items)))

        for node in filter(lambda x: check_taints(x.spec.taints), nodes.items):
            if (aipods_type := node.metadata.labels.get("aicp.group/aipods_type")) not in r:
                r[aipods_type] = []
            r[aipods_type].append(node.status.allocatable)
        print(json.dumps(r))
        return r

    def test_product_attr_query_request(self):
        print(product_attr_query_request())

    def test_gpu_manager(self):
        m = gpu_manager.get_gpu_matcher("NVIDIA")
        print(m)

    def test_NoteBookCrOperator(self):
        from app.apps.notebooks.kr8s_objects.notebook import NoteBookCrOperator
        nbo = NoteBookCrOperator("nb-dr7sxhw83474", "usr-gejp8uea", "usr-GEjP8uea")
        nbo.change_image("j1-dockerhub.qingcloud.com/public/tverous:pytorch-notebook1111")
        print(nbo)

    def test_func(self):
        class ModelMixin():
            class Async:

                def __new__(cls, *args, **kwargs):
                    pass


                def get_cls(self):
                    return self

            class Sync(Async):
                pass

        class Model(ModelMixin):
            pass

        m = Model()
        print(m.Async.get_cls())
        print(m.Sync.get_cls())

    def test_authp(self):
        ap = AuthorizationPolicy.get("user-usr-lxapexlz-clusterrole-edit", namespace="usr-nuhirbcj")
        print(ap)

    async def test_async_api_connect(self):
        ic = IaasClient()
        req = {
            'access_keys': ["UVNVGDVTWLDPTPNLSZQZ"]
        }
        action = "DescribeAccessKeys"
        r = await ic.send_request_async(action, req)
        print(r)


    async def test_asynv_methods(self):
        import asyncio
        from functools import wraps

        class AsyncifyMethods:
            """
            一个类装饰器，将类中的同步方法包装为异步方法，使用 asyncio.run_in_executor 执行。
            """

            def __init__(self, cls):
                self.cls = cls

            @staticmethod
            def _create_async_method(method):
                """
                创建独立的异步方法包装
                """

                @wraps(method)
                async def async_wrapper(*args, **kwargs):
                    loop = asyncio.get_event_loop()
                    return await loop.run_in_executor(None, lambda: method(*args, **kwargs))

                return async_wrapper

            def __call__(self, *args, **kwargs):
                # 实例化原始类
                instance = self.cls(*args, **kwargs)

                # 遍历类的所有属性和方法
                for attr_name in dir(instance):
                    if attr_name.startswith("_"):  # 跳过私有或特殊方法
                        continue

                    attr = getattr(instance, attr_name)
                    if callable(attr) and not asyncio.iscoroutinefunction(attr):
                        # 创建独立作用域生成异步方法
                        async_method = self._create_async_method(attr)
                        setattr(instance, attr_name, async_method)

                return instance

        @AsyncifyMethods
        class MyClass:
            def sync_method_a(self, x):
                print(f"A  Running sync_method with {x} as argument")
                return x * 2

            def sync_method_b(self, x):
                print(f"B  Running sync_method with {x} as argument")
                return x * 2

        # 测试
        instance = MyClass()
        print(await instance.sync_method_a(10))
        print(await instance.sync_method_b(10))



if __name__ == '__main__':
    unittest.main()
