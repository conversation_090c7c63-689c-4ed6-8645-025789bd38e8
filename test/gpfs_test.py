import json
import unittest

import kr8s

from app.apps.notebooks.background_task import stop_notebooks_in_background
from app.core.kube.api.kfam import KfamClient
from app.core.models import QingcloudUser
from app.core.qingcloud.gpfs import QingcloudGpfsClient
from app.core.volumes.objects import StorageClass
from app.core.volumes.qingcloud_gpfs import QingcloudGpfsVolume


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    # def setUp(self):
    #     user = QingcloudUser(**{
    #         "access_key_id": "DWHBGUZTDGNCMTUGYCSS",
    #         "secret_access_key": "1TyJtIDgAggyB36d12KxNDqYuZ3YvmOO2AQysKYu",
    #         'rating': 0,
    #         'verification_code': '',
    #         'company_code': '',
    #         'vdc_id': None,
    #         'notify_email': '<EMAIL>',
    #         'verify_type': 'personal',
    #         'paid_mode': 'postpaid',
    #         'gravatar_email': '<EMAIL>',
    #         'zones': [],
    #         'currency': 'cny',
    #         'create_time': '2023-04-03T07:15:55Z',
    #         'is_set_passwd': 1,
    #         'personal_code': '',
    #         'company_phone': '',
    #         'bonus': '0',
    #         'user_id': 'usr-iNbWHPtp',
    #         'source': '',
    #         'search_key': '2f6a8cb2a1c28d92426d23dac670b753,b49c5ac948b66b4bc5e008ab42c320c0,2a2920e5a37efa24e451abd0002b8f8d,072728a918c044228ed2e5af06d00fe9,39c7ab44baf44c56acb2bcf76369655c,',
    #         'personal_name': '',
    #         'regions': ['testing'],
    #         'iam_domain': 'usr-iNbWHPtp',
    #         'is_data_encrypted': True,
    #         'industry_category': '',
    #         'company_name': '',
    #         'region_info': {'testing': {'zones': ['testing1b', 'testing1a'], 'default_zone': 'testing1a'}},
    #         'privilege': 10,
    #         'role': 'user', 'mkt_source': '', 'user_name': 'danchey',
    #         'email': '<EMAIL>', 'channel': '', 'login_account_users': [], 'status': 'active',
    #         'email_veri_code': '', 'passwd_status_time': '2023-04-03T07:15:55Z', 'sale_type': 'public',
    #         'is_email_confirmed': 1, 'passwd': '*', 'is_phone_verified': 0, 'change_passwd_first_login': 0,
    #         'phone': '', 'birthday': None, 'preference': 1, 'address': '', 'remarks': '', 'total_sub_user_count': 0,
    #         'console_id': 'testingcloud', 'lang': 'zh-cn', 'user_type': 0, 'gender': 'male', 'nologin': 0,
    #         'support_engineer': '', 'crm_owner': '', 'root_user_id': 'usr-jaswGjDn',
    #         'status_time': '2023-04-03T07:15:55Z', 'login_type': [], 'balance': '-1645.4479', 'zone_info': {
    #             'testing1b': {'privilege': 10, 'user_id': 'usr-jaswGjDn', 'role': 'user', 'zone_id': 'testing1b'},
    #             'testing1a': {'privilege': 10, 'user_id': 'usr-jaswGjDn', 'role': 'user', 'zone_id': 'testing1a'}},
    #         'verify_status': 'new'})
    #     self.gpfs_client = QingcloudGpfsClient(user)
    #     self.volume_client = QingcloudGpfsVolume(user, "yangzhou2")

    def test_get_filesystem(self):
        r = self.gpfs_client.get_filesets()
        print(r)

    def test_get_sc(self):
        r = StorageClass.get("gpfs-sc-yangzhou2")
        # r = kr8s.get("storageclasses")
        print(r.raw)

    def test_has_sc(self):
        print(self.volume_client.has_storage_class())

    def test_create_sc(self):
        print(self.volume_client.create_storage_class())


    def test_stop_notebooks(self):
        stop_notebooks_in_background("nb-e8gsm7ge2wow")

if __name__ == '__main__':
    unittest.main()
