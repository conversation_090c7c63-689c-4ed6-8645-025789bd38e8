import asyncio

class LockManager:
    def __init__(self):
        # 存储每个ID对应的锁
        self.locks = {}

    def get_lock(self, id):
        # 获取ID对应的锁，如果不存在则创建一个新的锁
        if id not in self.locks:
            self.locks[id] = asyncio.Lock()
        return self.locks[id]

async def handle_task(lock_manager, id, task_name, processing_time):
    # 获取特定ID的锁
    lock = lock_manager.get_lock(id)
    async with lock:
        print(f"Task {task_name} with ID {id} is starting.")
        await asyncio.sleep(processing_time)
        print(f"Task {task_name} with ID {id} is finished.")

async def main():
    lock_manager = LockManager()

    tasks = [
        handle_task(lock_manager, 1, "Task 1A", 2),
        handle_task(lock_manager, 1, "Task 1B", 1),
        handle_task(lock_manager, 2, "Task 2A", 3),
        handle_task(lock_manager, 2, "Task 2B", 1),
        handle_task(lock_manager, 3, "Task 3A", 1),
    ]

    await asyncio.gather(*tasks)

asyncio.run(main())
