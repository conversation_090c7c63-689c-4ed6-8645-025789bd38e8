import unittest

from httpx import HTTPStatusError

from app import logger
from app.apps.notebooks.background_task import stop_notebooks_in_background
from app.core.models import VolumeSpecBase
from app.core.volumes.local_storage import LocalStorageVolume


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.k8s_client = None

    def test_create_notebooks(self):
        params = {
            "name": "danchey-jupyter-abcd",
            "user_id": "<EMAIL>",
            "namespace": "kongyao",
            "image": "kubeflownotebookswg/jupyter-scipy:v1.8.0",
            "allowCustomImage": True,
            "imagePullPolicy": "IfNotPresent",
            "customImage": "",
            "customImageCheck": False,
            "server_type": "jupyter",
            "cpu": "0.5",
            "cpuLimit": "0.6",
            "memory": "1Gi",
            "memoryLimit": "1.2Gi",
            "gpus": {"num": "none"},
            "affinityConfig": "",
            "tolerationGroup": "",
            "shm": True,
            "configurations": [],
            "env":[],
            "workspace": {
                "mount": "/home/<USER>",
                "newPvc": {
                    "metadata": {
                        "name": "danchey-jupyter-workspace"},
                    "spec": {
                        "accessModes": ["ReadWriteOnce"],
                        "resources": {
                            "requests": {"storage": "5Gi"}}}}},
            "datavols": []
        }

        d = NoteBook.get_resource_definition(params)
        notebooks = NoteBook(d)
        try:
            notebooks.create()
            print(notebooks.raw)
        except HTTPStatusError as e:
            print(e.response.status_code)
            print(e)


    def test_compress_zv(self):
        vs = VolumeSpecBase(file_set="nb-e72r1omq40zk", volume_type="LOCAL", pvc_name="nb-e72r1omq40zk",
                            mount_path="a", )
        lsv = LocalStorageVolume(None, "usr-wj2gdxug", vs)
        lsv.compression()




