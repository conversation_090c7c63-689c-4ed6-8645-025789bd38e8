import os

image_map = {
    "hub.kubesphere.com.cn/aicp/k8scsi/csi-provisioner:v2.1.0": "dockerhub.aicp.local/aicp-common/aicp/k8scsi/csi-provisioner:v2.1.0",
    "hub.kubesphere.com.cn/aicp/csi-s3:0.41.1": "dockerhub.aicp.local/aicp-common/aicp/csi-s3:0.41.1",
    "hub.kubesphere.com.cn/aicp/k8scsi/csi-node-driver-registrar:v1.2.0": "dockerhub.aicp.local/aicp-common/aicp/k8scsi/csi-node-driver-registrar:v1.2.0",
}


def run_cmd(cmd):
    print(cmd)
    os.system(cmd)


for ir, it in image_map.items():
    run_cmd(f"docker pull {ir}")
    run_cmd(f"docker tag {ir} {it}")
    run_cmd(f"docker push {it}")

BASE_PATH = os.path.dirname(os.path.abspath(__file__))
get_file = lambda x: os.path.join(BASE_PATH, x)

# delete old provisioner
run_cmd(f"kubectl delete -f {get_file('old-provisioner.yaml')}")

run_cmd(f"kubectl apply -f {get_file('provisioner.yaml')}")
run_cmd(f"kubectl apply -f {get_file('driver.yaml')}")
run_cmd(f"kubectl apply -f {get_file('csi-s3.yaml')}")
