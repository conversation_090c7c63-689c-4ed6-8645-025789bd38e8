import pytest
from fastapi.testclient import Test<PERSON>lient
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.middlewares.operation_record import OperationRecordMiddleware

@pytest.fixture
def mock_app():
    app = FastAPI()
    return app

@pytest.fixture
def middleware(mock_app):
    return OperationRecordMiddleware(mock_app)

@pytest.fixture
def mock_call_next():
    async def _mock_call_next(request: Request):
        return Response(content="mock response", media_type="application/json")
    return AsyncMock(side_effect=_mock_call_next)

@pytest.mark.asyncio
async def test_dispatch_create_notebook(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/notebooks/namespaces/test-ns/notebooks"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"name": "test-notebook"}'
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_delete_notebook(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/notebooks/namespaces/test-ns/notebooks/test-notebook"
    mock_request.method = "DELETE"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_start_notebook(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/notebooks/namespaces/test-ns/notebooks/test-notebook/start"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"image": "test-image"}'
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_stop_notebook(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/notebooks/namespaces/test-ns/notebooks/test-notebook/stop"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_notebook_streaming_response(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/notebooks/namespaces/test-ns/notebooks/test-notebook/logs"
    mock_request.method = "GET"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    # Mock streaming response
    async def stream_gen():
        yield b"stream data"
        
    mock_response = AsyncMock()
    mock_response.body_iterator = stream_gen()
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
        
        # Verify dispatch was called
        mock_dispatch.assert_called_once()
