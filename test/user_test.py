import base64
import json
import time
import unittest

import kr8s
from kr8s.objects import Secret

from app.core.kube.api.kfam import KfamClient
from app.core.models import QingcloudUser
from app.core.qingcloud.billing import QAIBillingService
from app.core.qingcloud.interface import describe_user, describe_users, product_center_query_request
from app.core.qingcloud.resource import ProductCenterResource


class MyTestCase(unittest.IsolatedAsyncioTestCase):

    def setUp(self):
        self.k8s_client = None
        self.kfam_client = KfamClient("<EMAIL>")
        # self.user = QingcloudUser(**{
        #     "access_key_id": "DWHBGUZTDGNCMTUGYCSS",
        #     "secret_access_key": "1TyJtIDgAggyB36d12KxNDqYuZ3YvmOO2AQysKYu",
        #     'rating': 0,
        #     'verification_code': '',
        #     'company_code': '',
        #     'vdc_id': None,
        #     'notify_email': '<EMAIL>',
        #     'verify_type': 'personal',
        #     'paid_mode': 'postpaid',
        #     'gravatar_email': '<EMAIL>',
        #     'zones': [],
        #     'currency': 'cny',
        #     'create_time': '2023-04-03T07:15:55Z',
        #     'is_set_passwd': 1,
        #     'personal_code': '',
        #     'company_phone': '',
        #     'bonus': '0',
        #     'user_id': 'usr-iNbWHPtp',
        #     'source': '',
        #     'search_key': '2f6a8cb2a1c28d92426d23dac670b753,b49c5ac948b66b4bc5e008ab42c320c0,2a2920e5a37efa24e451abd0002b8f8d,072728a918c044228ed2e5af06d00fe9,39c7ab44baf44c56acb2bcf76369655c,',
        #     'personal_name': '',
        #     'regions': ['testing'],
        #     'iam_domain': 'usr-iNbWHPtp',
        #     'is_data_encrypted': True,
        #     'industry_category': '',
        #     'company_name': '',
        #     'region_info': {'testing': {'zones': ['testing1b', 'testing1a'], 'default_zone': 'testing1a'}},
        #     'privilege': 10,
        #     'role': 'user', 'mkt_source': '', 'user_name': 'danchey',
        #     'email': '<EMAIL>', 'channel': '', 'login_account_users': [], 'status': 'active',
        #     'email_veri_code': '', 'passwd_status_time': '2023-04-03T07:15:55Z', 'sale_type': 'public',
        #     'is_email_confirmed': 1, 'passwd': '*', 'is_phone_verified': 0, 'change_passwd_first_login': 0,
        #     'phone': '', 'birthday': None, 'preference': 1, 'address': '', 'remarks': '', 'total_sub_user_count': 0,
        #     'console_id': 'testingcloud', 'lang': 'zh-cn', 'user_type': 0, 'gender': 'male', 'nologin': 0,
        #     'support_engineer': '', 'crm_owner': '', 'root_user_id': 'usr-jaswGjDn',
        #     'status_time': '2023-04-03T07:15:55Z', 'login_type': [], 'balance': '-1645.4479', 'zone_info': {
        #         'testing1b': {'privilege': 10, 'user_id': 'usr-jaswGjDn', 'role': 'user', 'zone_id': 'testing1b'},
        #         'testing1a': {'privilege': 10, 'user_id': 'usr-jaswGjDn', 'role': 'user', 'zone_id': 'testing1a'}},
        #     'verify_status': 'new'})

    def test_user_exists(self):
        try:
            workinfo = self.kfam_client.get_v1_role_cluster_admin()
            print(workinfo)
        except Exception as e:
            print(e)

    def test_read_bindings(self):
        bindings = self.kfam_client.read_bindings()
        print(bindings)

    def test_product_center_query_request(self):
        print(json.dumps(product_center_query_request(search_word="sku_J7po7k7j8wvW")))

    def test_ProductCenterResource(self):
        pcr = ProductCenterResource("sku_J7po7k7j8wvW")
        print(json.dumps(pcr.get_billing_price_info()))
        print(json.dumps(pcr.get_k8s_resources_definition()))

    def test_billing(self):
        pcr = ProductCenterResource("sku_vGX4NGO7vOKk", replicas=2)
        print(pcr)
        print(pcr.get_billing_price_info())
        print(pcr.get_k8s_resources_definition())
        billing = QAIBillingService()
        r_id = "xxxxxx1"
        print(billing.get_price(self.user.user_id, pcr.get_billing_price_info(), "qai"))
        # print(billing.lease(r_id, self.user.user_id, pcr.get_billing_price_info()))

    def test_secret(self):
        sc:Secret = Secret.get("aicp-es-es-elastic-user", namespace="aicp-storage")
        print(base64.b64decode(sc.data.get('elastic')).decode())
        # print(base64.B53sc.data.get('elastic'))


if __name__ == '__main__':
    unittest.main()
