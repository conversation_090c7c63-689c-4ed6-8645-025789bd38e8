import datetime
import json
import unittest
import urllib3
import requests
from typing import List
from sqlalchemy import create_engine, asc, update, <PERSON>ala<PERSON><PERSON><PERSON>ult
from sqlalchemy.orm import sessionmaker, selectinload
from sqlmodel import Session, select
from app import logger, settings

from app.models.gpu import IbDevInfo
from app.models.notebooks import NotebookStatus, Notebook
from app.models.operation_record import OperationRecord, NotebookMessageStopAction
from app.models.resource_group import ResourceNode
from app.apps.trains import Train
from app.models.user import UserInfo
from app.core.models import STATUS_PHASE
from app.core.ufm.dependencies import get_ufm_crud
from app.core.ufm.models import UFMPkeys
from app.core.utils import generate_random_string
from multiprocessing import Lock
from requests.auth import HTTPBasicAuth

engine = create_engine(
    settings.DB_CONNECTION_STR,
    pool_size=settings.DB_POOL_SIZE,
    max_overflow=settings.DB_MAX_OVERFLOW,
    pool_recycle=settings.DB_POOL_RECYCLE,
    pool_pre_ping=settings.DB_POOL_PRE_PING,
    echo=app.settings.ECHO,
    future=True
)


def get_db_session() -> Session:
    with Session(engine) as session:
        yield session
        session.commit()

Session = sessionmaker(bind=engine, autocommit=False)

lock = Lock()

##################################### UFM ######################################

# UFM add empty pkey
def add_pkey_request(pkey: str):
    data = {
        "pkey": pkey,
        "index0": True,
        "ip_over_ib": True,
        "mtu_limit": 2,
        "service_level": 0,
        "rate_limit": 300,
    }
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    json_data = json.dumps(data)
    res = session.post(settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/add", headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(settings.UFM_USERNAME, settings.UFM_PASSWORD) ,data=json_data)
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("ufm server create pkey failed")

# ufm server delete pkey
def delete_ufm_pkey(pkey: str):
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    res = session.delete(settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/" + hex(pkey), headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(settings.UFM_USERNAME, settings.UFM_PASSWORD))
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("ufm unbind pkey failed")

# UFM GUIDs bind PKey
def bind_ufm_pkey_guids(guids: List[str], pkey: str):
    # Remove the prefix '0x'
    new_guids = [item[2:] for item in guids]
    data = {
        "guids": new_guids,
        "pkey": pkey,
        "index0": True,
        "ip_over_ib": True,
        "membership": "full",
        "mtu_limit": 2,
        "service_level": 0,
        "rate_limit": 300,
    }
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    json_data = json.dumps(data)
    res = session.put(settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/", headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(settings.UFM_USERNAME, settings.UFM_PASSWORD) ,data=json_data)
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("ufm bind pkey failed")

# ufm server unbind pkey guid
def unbind_ufm_pkey_guid(guids: List[str], pkey: str):
    # Remove the prefix '0x'
    new_guids = [item[2:] for item in guids]
    guid_str = ','.join(new_guids)
    # create session
    session = requests.Session()
    # not use verify
    session.verify = False
    # ignore warning
    urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
    res = session.delete(settings.UFM_ADDRESS + "/ufmRest/resources/pkeys/" + pkey + "/guids/" + guid_str, headers={'Content-Type': 'Application/json'}, auth=HTTPBasicAuth(settings.UFM_USERNAME, settings.UFM_PASSWORD))
    if res.status_code == 400 and "is already exist!" in str(res.content):
        raise Exception(f"{pkey} is already exist!")
    # not 2xx
    if int(res.status_code / 100) != 2:
        raise Exception("umf unbind pkey failed")



################################## 业务操作 #################################


# user bind pkey
def add_ufm_pkey(conn, user_id: str):
    # get user pkey info
    user = conn.query(UFMPkeys).filter_by(user_id=user_id).all()
    conn.commit()
    if user:
        logger.error(f"User already exists pkey")
        raise Exception(f"User already exists pkey")
    with lock:
        pkeys_list = conn.query(UFMPkeys.pkey).order_by(asc(UFMPkeys.pkey)).all()
        conn.commit()
        pkeys_first = UFMPkeys()
        pkeys_first.user_id = user_id
        pkeys_first.pkey_id = "ufm-" + generate_random_string()
        pkeys_first.status = "success"
        pkey_temp = 0
        # list is not None
        if pkeys_list:
            # pkey more than 65534
            if len(pkeys_list) >= 65534:
                logger.error(f"Insufficient number of pkey")
                raise Exception(f"Insufficient number of pkey")
            # find pkey
            for i in range(len(pkeys_list)):
                if i + 1 != int(pkeys_list[i].pkey, 16):
                    pkey_temp = i + 1
                    break
                if i == len(pkeys_list) - 1:
                    pkey_temp = len(pkeys_list) + 1
        # list is None, pkey is 1
        else:
            pkey_temp = 1
        pkeys_first.pkey = hex(pkey_temp)
        # ufm server
        add_pkey_request(pkeys_first.pkey)
        # database bind user and pkey
        conn.add(pkeys_first)
        conn.commit()

# guids bind pkey
def bind_guids_pkey(conn, guids: List[str], pkey: str):
    # start transaction
    with conn.begin():
        # get exist guids
        database_guids = conn.query(IbDevInfo.guid).filter_by(pkey=pkey).all()
        exist_guids = []
        for database_guid in database_guids:
            exist_guids.append(database_guid.guid)
        guids.extend(exist_guids)
        # ufm server
        bind_ufm_pkey_guids(guids, pkey)
        for guid in guids:
            # update
            stmt = update(IbDevInfo).where(IbDevInfo.guid == guid).values(pkey=pkey)
            conn.execute(stmt)
        conn.commit()

# guids unbind pkey
def unbind_guids_pkey(conn, guids: List[str], pkey: str):
    unbind_ufm_pkey_guid(guids, pkey)
    # start transaction
    with conn.begin():
        # get all dev info
        dev_list = conn.query(IbDevInfo).filter(IbDevInfo.pkey == pkey).all()
        if len(dev_list) > len(guids):
                for guid in guids:
                    # update
                    stmt = update(IbDevInfo).where(IbDevInfo.guid == guid).values(pkey='')
                    conn.execute(stmt)
        else:
            for guid in guids:
                # update
                stmt = update(IbDevInfo).where(IbDevInfo.guid == guid).values(pkey='')
                conn.execute(stmt)
            database_guids = conn.query(IbDevInfo.guid).filter_by(pkey=pkey).all()
            if len(database_guids) == 0:
                add_pkey_request(pkey)



def get_guids(conn, hostnames):
    # guids = conn.query(IbDevInfo.guid).filter(IbDevInfo.hostname.in_(hostnames)).all()
    # res_guids = []
    # for guid in guids:
    #     res_guids.append(guid[0])
    # print(res_guids)
    hostnames = conn.query(ResourceNode.hostname).filter(ResourceNode.rg_id.in_(["rg-OqhM10XQ"])).all()
    hostnamess = []
    for rn in hostnames:
        hostnamess.append(rn[0])
    print(hostnamess)

class UFMTest(unittest.TestCase):

    def test_exist_user_init(self):
        user_id = "usr-C3YU7Mr7"
        namespace = user_id.lower()
        conn = Session()
        user = conn.query(UserInfo).filter_by(user_id=user_id).first()
        if user:
            logger.error(f"User already exists.")
            return
        user_init = UserInfo()
        user_init.user_id = user_id
        user_init.namespace = namespace
        user_init.containers_number = settings.POD_NUMBER
        user_init.jobs_number = settings.JOB_NUMBER
        user_init.rg_nodes = settings.RG_NODES
        # profile initialization
        user_init.status = "active"
        conn.add(user_init)
        conn.commit()

    def test_not_exist_user_init(self):
        user_id = "usr-C3YU7Mr7"
        namespace = user_id.lower()
        conn = Session()
        user = conn.query(UserInfo).filter_by(user_id=user_id).first()
        if user:
            logger.error(f"User already exists.")
            return
        user_init = UserInfo()
        user_init.user_id = user_id
        user_init.namespace = namespace
        user_init.containers_number = settings.POD_NUMBER
        user_init.jobs_number = settings.JOB_NUMBER
        user_init.rg_nodes = settings.RG_NODES
        # profile not initialization
        user_init.status = "warning"
        conn.add(user_init)
        conn.commit()

    def test_update_user_info(self):
        user_id = "usr-C3YU7Mr7"
        namespace = user_id.lower()
        conn = Session()
        user_info = conn.query(UserInfo).filter_by(user_id=user_id).first()
        user_info.status = "active"
        conn.commit()

    def test_user_quota(self):
        # user_db = get_user_crud(session=get_db_session)
        # user_db.quota_verification("usr-C3YU7Mr7", "nb", 2)
        conn = Session()
        user_id = "usr-C3YU7Mr7"
        type = "tn"
        num = 2
        user: UserInfo = conn.query(UserInfo).filter_by(user_id=user_id).first()
        if type == "nb":
            stmt = select(Notebook).where((Notebook.namespace == user_id.lower()) & (Notebook.status == NotebookStatus.Running))
            notebooks: ScalarResult[Notebook] = conn.scalars(stmt)
            nbs = notebooks.fetchall()
            if len(nbs) + num > user.containers_number:
                return False
        if type == "tn":
            stmt = select(Train).where((Train.namespace == user_id.lower()) & ((Train.status == "Succeeded") | (Train.status == "Creating")))
            trains: ScalarResult[Train] = conn.scalars(stmt)
            trs = trains.fetchall()
            if len(trs) + num > user.jobs_number:
                return False
        if type == "rg":
            stmt = select(ResourceNode).where((ResourceNode.user_id == user_id) & (ResourceNode.status == "active"))
            resource_nodes: ScalarResult[ResourceNode] = conn.scalars(stmt)
            rns = resource_nodes.fetchall()
            if len(rns) + num > user.rg_nodes:
                return False
        return True

    def test_get_notebook(self):
        session = Session()
        # 已关机
        stmt_op = select(OperationRecord).where(
            (OperationRecord.resource == "notebook_id") & (OperationRecord.action == NotebookMessageStopAction.NowStop))
        sr_op: ScalarResult[OperationRecord] = session.scalars(stmt_op)
        operation_record = sr_op.first()
        if operation_record is not None:
            logger.info("已关机短信已发送")
            return
        notebook_stmt = select(Notebook).where(
            Notebook.status == STATUS_PHASE.Running, Notebook.stop_time.is_not(None),
            Notebook.stop_time <= datetime.datetime.now()
        ).options(selectinload(Notebook.replica_specs))
        notebooks_sr: ScalarResult[Notebook] = session.scalars(notebook_stmt)
        notebooks = notebooks_sr.all()
        print(notebooks)
        # record = OperationRecord(
        #     user_id='user_id', resource="notebook.uuid", child_resource="notebook.uuid",
        #     status="SUCCEED", action=NotebookMessageStopAction.NowStop
        # )
        record = OperationRecord(
            user_id='system', resource="notebook.uuid", child_resource="notebook.uuid",
            status="notebook.status", reason="notebook.reason", action="notebook.status"
        )
        print(record)
        session.add(record)
        session.commit()
        session.close()

    def test_add_pkeys1(self):
        conn = Session()
        add_ufm_pkey(conn, "2f31333")

    def test_bind_guids_pkey(self):
        conn = Session()
        # "0xa088c2030018d940", "0xa088c203009618f4", "0xa088c2030018d460"
        bind_guids_pkey(conn, ["0xa088c203009618f4", "0xa088c2030018d940"], "0x1")

    def test_unbind_guids_pkey(self):
        conn = Session()
        unbind_guids_pkey(conn, ["0xa088c203009618f4", "0xa088c2030018d940"], "0x1")

    def test_add_pkeys(self):
        ufm_crud = get_ufm_crud(session=get_db_session)
        ufm_crud.add_ufm_pkeys("usr-iNbWHPtp")

    def test_add_pkey_requets(self):
        add_pkey_request(1)

    def test_bind_ufm_pkey(self):
        bind_ufm_pkey_guids(["0xa088c2030018d460"], "0x1")

    def test_unbind_ufm_pkey(self):
        unbind_ufm_pkey_guid(["a088c2030018d940", "a088c203009618f4"], 7)

    def test_guids(self):
        conn = Session()
        get_guids(conn, ["hs04"])

    def test_delete_pkey(self):
        delete_ufm_pkey(7)

    def test_async_req(self):
        user_id = "usr-yG39pcgm"

        data = {
            "user_id": user_id,
            "hostnames": "hs01"
        }
        # params = {"user_id": user_id}
        # rep = describe_access_key_by_user_id(user_id)
        # access_key_id = rep.get("access_key_set")[0]["access_key_id"]
        # secret_access_key = rep.get("access_key_set")[0]["secret_access_key"]
        # signature = get_signature(method="POST", url="/aicp/ufm/create_ufm_info", ak=access_key_id,
        #                           sk=secret_access_key,
        #                           params=params)
        # r = requests.post(settings.AI_CLOUD_PUSH_SERVER + "?" + signature, json=data)
        # print(r)

        res = requests.post(url="http://127.0.01:5000/aicp/ufm/create_ufm_info", json=data)
        print(res)


    # def test_add_pkeys(self):
    #     ufm_crud = get_ufm_crud(session=get_db_session)
    #     ufm_crud.add_ufm_pkeys("usr-iNbWHPtp")
    #     # UFMInfoCrud.add_ufm_pkeys(self, "usr-iNbWHPtp")
    #     # OperationRecordCrud.get_operation_record(self, "123")
    #
    # def test_get_operator(self):
    #     op_crud = get_op_crud(session=Depends(get_db_session))
    #     op_crud.get_operation_record("123123")
