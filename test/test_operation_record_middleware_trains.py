import pytest
from fastapi.testclient import TestClient
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.middlewares.operation_record import OperationRecordMiddleware

@pytest.fixture
def mock_app():
    app = FastAPI()
    return app

@pytest.fixture
def middleware(mock_app):
    return OperationRecordMiddleware(mock_app)

@pytest.fixture
def mock_call_next():
    async def _mock_call_next(request: Request):
        return Response(content="mock response", media_type="application/json")
    return AsyncMock(side_effect=_mock_call_next)

@pytest.mark.asyncio
async def test_dispatch_create_train(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/namespaces/test-ns/trains"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"name": "test-train"}'
    
    # Mock response
    mock_response = AsyncMock()
    mock_response.headers = {"X-Request-Id": "test-trace-id"}
    mock_response.body.return_value = b'{"id": "test-train-id"}'
    mock_call_next.return_value = mock_response

    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch, \
         patch("app.core.middlewares.operation_record.producer", new=AsyncMock()) as mock_producer:
        
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
        
        # Verify request parsing
        mock_request.url.path = "/trains/namespaces/test-ns/trains"
        assert mock_request.method == "POST"
        assert mock_request.headers["aicp-userid"] == "test-user"
        

@pytest.mark.asyncio
async def test_dispatch_delete_train(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/namespaces/test-ns/trains/test-train"
    mock_request.method = "DELETE"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    # Mock response
    mock_response = AsyncMock()
    mock_response.headers = {"X-Request-Id": "test-trace-id"}
    mock_response.body.return_value = b'{"status": "deleted"}'
    mock_call_next.return_value = mock_response

    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch, \
         patch("app.core.middlewares.operation_record.producer", new=AsyncMock()) as mock_producer:
        
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
        
        # Verify request parsing
        assert mock_request.url.path == "/trains/namespaces/test-ns/trains/test-train"
        assert mock_request.method == "DELETE"
        assert mock_request.headers["aicp-userid"] == "test-user"


@pytest.mark.asyncio
async def test_dispatch_start_train(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/namespaces/test-ns/trains/test-train/start"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"image": "test-image"}'
    
    # Mock response
    mock_response = AsyncMock()
    mock_response.headers = {"X-Request-Id": "test-trace-id"}
    mock_response.body.return_value = b'{"status": "started"}'
    mock_call_next.return_value = mock_response

    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch, \
         patch("app.core.middlewares.operation_record.producer", new=AsyncMock()) as mock_producer:
        
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
        
        # Verify request parsing
        assert mock_request.url.path == "/trains/namespaces/test-ns/trains/test-train/start"
        assert mock_request.method == "POST"
        assert mock_request.headers["aicp-userid"] == "test-user"
        

@pytest.mark.asyncio
async def test_dispatch_stop_train(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/namespaces/test-ns/trains/test-train/stop"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.path_params = {"uuid": "test-train"}
    mock_request.body.return_value = b'{"uuid": "test-train"}'
    
    # Mock response
    mock_response = AsyncMock()
    mock_response.headers = {"X-Request-Id": "test-trace-id"}
    mock_response.body.return_value = b'{"status": "stopped"}'
    mock_call_next.return_value = mock_response

    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch, \
         patch("app.core.middlewares.operation_record.producer", new=AsyncMock()) as mock_producer:
        
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
        
        # Verify request parsing
        assert mock_request.url.path == "/trains/namespaces/test-ns/trains/test-train/stop"
        assert mock_request.method == "POST"
        assert mock_request.headers["aicp-userid"] == "test-user"


@pytest.mark.asyncio
async def test_dispatch_train_logs(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/trains/namespaces/test-ns/trains/test-train/logs"
    mock_request.method = "GET"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    # Mock streaming response
    async def stream_gen():
        yield b"stream data"
        
    mock_response = AsyncMock()
    mock_response.body_iterator = stream_gen()
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
        
        # Verify dispatch was called
        mock_dispatch.assert_called_once()
