import pytest
from fastapi.testclient import Test<PERSON>lient
from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from unittest.mock import AsyncMock, MagicMock, patch
from app.core.middlewares.operation_record import OperationRecordMiddleware

@pytest.fixture
def mock_app():
    app = FastAPI()
    return app

@pytest.fixture
def middleware(mock_app):
    return OperationRecordMiddleware(mock_app)

@pytest.fixture
def mock_call_next():
    async def _mock_call_next(request: Request):
        return Response(content="mock response", media_type="application/json")
    return AsyncMock(side_effect=_mock_call_next)

@pytest.mark.asyncio
async def test_dispatch_create_public_key(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/public_key"
    mock_request.method = "POST"
    mock_request.headers = {"aicp-userid": "test-user"}
    mock_request.body.return_value = b'{"key": "test-key"}'
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_delete_public_key(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/public_key/test-key"
    mock_request.method = "DELETE"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_get_public_key(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/public_key/test-key"
    mock_request.method = "GET"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()

@pytest.mark.asyncio
async def test_dispatch_list_public_keys(middleware, mock_call_next):
    # Mock dependencies
    mock_request = AsyncMock()
    mock_request.url.path = "/public_key"
    mock_request.method = "GET"
    mock_request.headers = {"aicp-userid": "test-user"}
    
    with patch.object(middleware, "dispatch", new=AsyncMock()) as mock_dispatch:
        await middleware.dispatch(mock_request, mock_call_next)
    
        # Verify dispatch was called
        mock_dispatch.assert_awaited_once()
