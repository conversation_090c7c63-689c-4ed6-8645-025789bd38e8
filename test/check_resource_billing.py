import argparse
import json
import sys

import kr8s

sys.path.append("/code")

from app.core.qingcloud.billing import QAIBillingService
from app.models.notebooks import Notebook
from app.models.trains import Train
from app.apps.notebooks.kr8s_objects.notebook import NoteBook as NBCR

def get_resource_by_id(resource_id):
    if resource_id.startswith("nb-"):
        return Notebook.one_by_id(resource_id)
    elif resource_id.startswith("tn-"):
        return Train.one_by_id(resource_id)
    else:
        print("Not support resource type, Only support notebook and train")
        exit(1)

result = []

nbs = NBCR.list(namespace=kr8s.ALL)
for nb in nbs:
    notebook = get_resource_by_id(nb.metadata.name)
    # print(f"notebook id : {notebook.uuid}, status : {notebook.status}")
    lease_info = QAIBillingService().get_lease_info(notebook.uuid, notebook.user_id)
    if notebook.status == "Running" and lease_info["contract"].get("price_info").get("replicas")!=1:
        result.append({
            "notebook": str(notebook),
            "status": notebook.status,
            "lease_info": lease_info
        })
    if notebook.status in ["Suspended", "Suspending"] and lease_info["status"] == "active" and lease_info["contract"].get("price_info").get("replicas")!=0:
        result.append({
            "notebook_id": str(notebook),
            "status": notebook.status,
            "lease_info": lease_info
        })

print(json.dumps(result))