apiVersion: kubeflow.org/v1
kind: TFJob
metadata:
  generateName: tfjob
  namespace: your-user-namespace
spec:
  tfReplicaSpecs:
    Worker:
      replicas: 2
      restartPolicy: OnFailure
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          restartPolicy: OnFailure
          nodeName: gpu-a100-node001
          containers:
            - name: tensorflow
              image: registry.cn-beijing.aliyuncs.com/danchey/tf-mnist-with-summaries:latest
              command:
                - python
                - /var/tf_mnist/mnist_with_summaries.py
              resources:
                requests:
                  nvidia.com/gpu: 3
                limits:
                  nvidia.com/gpu: 3