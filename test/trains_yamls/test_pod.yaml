apiVersion: v1
kind: Pod
metadata:
  name: iboip-test-pod-1
  annotations:
    k8s.v1.cni.cncf.io/networks: example-ipoibnetwork
spec:
  nodeName: gpu-a100-node001
  restartPolicy: OnFailure
  containers:
    - image: nvidia/cuda:12.3.1-devel-ubi8
      name: mofed-test-ctr
      securityContext:
        capabilities:
          add: [ "IPC_LOCK" ]
      resources:
        requests:
          nvidia.com/gpu: 1
          rdma/rdma_shared_device_a: 1
        limits:
          nvidia.com/gpu: 1
          rdma/rdma_shared_device_a: 1
      command:
        - sh
        - -c
        - sleep inf
---
apiVersion: v1
kind: Pod
metadata:
  name: iboip-test-pod-2
  annotations:
    k8s.v1.cni.cncf.io/networks: example-ipoibnetwork
spec:
  nodeName: gpu-a100-node002
  restartPolicy: OnFailure
  containers:
    - image: nvidia/cuda:12.3.1-devel-ubi8
      name: mofed-test-ctr
      securityContext:
        capabilities:
          add: [ "IPC_LOCK" ]
      resources:
        requests:
          nvidia.com/gpu: 1
          rdma/rdma_shared_device_a: 1
        limits:
          nvidia.com/gpu: 1
          rdma/rdma_shared_device_a: 1
      command:
        - sh
        - -c
        - sleep inf