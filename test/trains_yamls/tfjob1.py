# -*- coding: utf-8 -*-
from time import sleep

import tensorflow as tf
import numpy as np

# create data
x_data = np.random.rand(100).astype(np.float32)
y_data = x_data*0.1+0.3
# create tensorflow structure start
Weights = tf.Variable(tf.random_uniform([1],-1.0,1.0)) # 参数变量，随机生成，一维变量，范围【-1，1】
biases = tf.Variable(tf.zeros([1])) # 初始值为0，一维

y = Weights*x_data+biases

loss = tf.reduce_mean(tf.square(y-y_data))  # loss 误差
optimizer = tf.train.GradientDescentOptimizer(0.5) # 优化
train = optimizer.minimize(loss) # 训练

init = tf.initialize_all_variables()

sess = tf.Session()
sess.run(init)  # 重要  激活
for step in range(201): # 训练200步
    sess.run(train)
    if step % 20 == 0:
        print(step,sess.run(Weights),sess.run(biases))


sleep(1000)

