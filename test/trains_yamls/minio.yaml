apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: minio
  namespace: aicp-storage
spec:
  serviceName: minio
  replicas: 1
  selector:
    matchLabels:
      app: minio
  template:
    metadata:
      labels:
        app: minio
    spec:
      hostAliases:
        - ip: ************
          hostnames:
            - aicp-oss.testing.com

      nodeName: master
      nodeSelector:
        - key: node-role.kubernetes.io/master
          operator: exists
      containers:
        - name: minio
          image: quay.io/minio/minio:RELEASE.2023-11-01T01-57-10Z-cpuv1
          imagePullPolicy: IfNotPresent
          env:
            - name: MINIO_SERVER_URL
              value: http://aicp-oss.testing.com
            - name: MINIO_BROWSER_REDIRECT_URL
              value: http://aicp-oss.testing.com/minio/ui
          args:
            - server
            - /data
            - --console-address
            - ":30901"
            - --address
            - ":30900"
          ports:
            - name: data
              containerPort: 30900
              protocol: "TCP"
            - name: console
              containerPort: 30901
              protocol: "TCP"
          volumeMounts:
            - name: data
              mountPath: /data
      volumes:
        - name: data
          hostPath:
            path: /minio/data
---
apiVersion: v1
kind: Service
metadata:
  name: minio
  namespace: aicp-storage
  labels:
    app: minio
spec:
  type: NodePort
  ports:
    - name: data
      port: 30900
      targetPort: 30900
      protocol: TCP
      nodePort: 30900
    - name: console
      port: 30901
      targetPort: 30901
      protocol: TCP
      nodePort: 30901
  selector:
    app: minio

---
apiVersion: tensorboard.kubeflow.org/v1alpha1
kind: Tensorboard
metadata:
    name: tensorboard-sample1
    namespace: usr-inbwhptp
spec:
    logspath: pvc://aicp-oss-pvc-usr-inbwhptp/069f61b3-7c13-4c3c-ab48-2147f5c102e9
