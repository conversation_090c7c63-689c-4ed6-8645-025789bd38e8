apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: gpfs-sc-yangzhou2
  labels:
    user: yangzhou2
provisioner: spectrumscale.csi.ibm.com
parameters:
  volBackendFs: "gpfs_share"
  uid: "1000"
  gid: "1000"
  filesetType: "dependent"
  parentFileset: "yangzhou2"
reclaimPolicy: Retain

---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: yangzhou2-pv
spec:
  storageClassName: gpfs-sc-yangzhou2
  capacity:
    storage: 10Gi
  accessModes:
    - ReadWriteMany
  csi:
    driver: spectrumscale.csi.ibm.com
    volumeHandle: 0;2;3320938546841704143;1F3D1FAC:6566D9C5;;yangzhou2;/gpfs_share/yangzhou2


---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: yangzhou2-pvc
spec:
  storageClassName: gpfs-sc-yangzhou2
  volumeName: yangzhou2-pv
  accessModes:
    - ReadWriteMany
  resources:
    requests:
      storage: 10Gi

---
apiVersion: v1
kind: Pod
metadata:
  labels:
    run: nginx
  name: nginx
spec:
  nodeName: master
  volumes:
    - name: gpfs
      persistentVolumeClaim:
        claimName: yangzhou2-pvc
  containers:
    - image: dockerhub.qingcloud.com/aicp_system/auth-server:latest
      command:
        - sleep
        - "3600"
      imagePullPolicy: IfNotPresent
      name: nginx
      resources: { }
      volumeMounts:
        - mountPath: /share
          subPath: logs
          name: gpfs
  restartPolicy: Always

---
