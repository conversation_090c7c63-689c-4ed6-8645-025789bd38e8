apiVersion: kubeflow.org/v1
kind: Notebook
metadata:
  annotations:
    notebooks.kubeflow.org/creator: usr-Du7Zvgq1
    notebooks.kubeflow.org/server-type: jupyter
  labels:
    app: nb-dg0vrbzifabk
  name: nb-dg0vrbzifabk
  namespace: usr-du7zvgq1
spec:
  template:
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: aicp.group.sku.sku_Ak1VmlJnGAr2
                    operator: Exists
      initContainers:
        - command:
            - cp
            - -a
            - /notebook-inint-container-files/*
            - /aicp-init
          image: dockerhub.qingcloud.com/aicp_system/notebook-init-container:dev
          imagePullPolicy: Always
          name: nb-dg0vrbzifabk-init
          resources:
            limits:
              cpu: 100m
              memory: 256Mi
            requests:
              cpu: 100m
              memory: 256Mi
          volumeMounts:
            - mountPath: /aicp-init
              name: notebook-init-copy
      containers:
        - command:
            - /aicp-init/init
          env:
            - name: TENSORBOARD_LOG_PATH
              value: /logs/tensorboard
          image: tverous/pytorch-notebook
          imagePullPolicy: Always
          name: nb-dg0vrbzifabk
          resources:
            limits:
              cpu: 2
              memory: 4Gi
            requests:
              cpu: 2
              memory: 4Gi
          volumeMounts:
            - mountPath: /logs/tensorboard
              name: default
              subPath: nb-dg0vrbzifabk
            - mountPath: /aicp-init
              name: notebook-init-copy
            - mountPath: /dev/shm
              name: dshm
          workingDir: /root
      securityContext:
        runAsGroup: 0
        runAsUser: 0
      volumes:
        - name: default
          persistentVolumeClaim:
            claimName: aicp-oss-pvc-usr-du7zvgq1
        - name: notebook-init-copy
          emptyDir:
            sizeLimit: 100Mi
        - emptyDir:
            medium: Memory
            sizeLimit: 1Gi
          name: dshm