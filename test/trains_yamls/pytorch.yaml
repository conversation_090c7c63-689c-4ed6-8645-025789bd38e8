apiVersion: "kubeflow.org/v1"
kind: PyTorchJob
metadata:
  name: pytjob
  namespace: usr-rticrzez
spec:
  pytorchReplicaSpecs:
    Worker:
      replicas: 1
      template:
        metadata:
          annotations:
            sidecar.istio.io/inject: "false"
        spec:
          activeDeadlineSeconds: 60
          nodeName: gpu-a100-node002
          containers:
            - name: pytorch
              securityContext:
                capabilities:
                    add:
                        - IPC_LOCK
                        - SYS_RESOURCE
              image: j1-dockerhub.qingcloud.com/pytorch/pytorch:11.8.0-devel-ubuntu22.04-v2
              command:
                - sh
                - -c
                -