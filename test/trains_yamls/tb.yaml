kind: Pod
apiVersion: v1
metadata:
  name: tensorboard-111
  namespace: usr-inbwhptp
  labels:
    app: tensorboard-sample1
    pod-template-hash: 9d54c7b97
spec:
  volumes:
    - name: tbpd
      persistentVolumeClaim:
        claimName: aicp-oss-pvc-usr-inbwhptp
  containers:
    - name: tensorboard
      image: 'tensorflow/tensorflow:2.5.1'
      command:
        - sleep
        - "1000"
      workingDir: /
      ports:
        - containerPort: 6006
          protocol: TCP
      resources: { }
      volumeMounts:
        - name: tbpd
          readOnly: true
          mountPath: /tensorboard_logs/
      terminationMessagePath: /dev/termination-log
      terminationMessagePolicy: File
      imagePullPolicy: IfNotPresent
  nodeName: master