# Makefile for building and pushing multi-architecture Docker images

# Default values
REPO ?= docker.io/aicphub/aicp-python
TAG ?= 3.9.18-mc
ARCHS ?= linux/amd64,linux/arm64

# Docker buildx builder name
BUILDER_NAME ?= multiarch-builder

# Default target
.PHONY: default
default: build

# Create a new builder instance if it doesn't exist
.PHONY: create-builder
create-builder:
	@if ! docker buildx inspect $(BUILDER_NAME) > /dev/null 2>&1; then \
		docker buildx create --name $(BUILDER_NAME) --use; \
		docker buildx inspect $(BUILDER_NAME) --bootstrap; \
	fi

# Build the Docker image for multiple architectures
.PHONY: build
build: create-builder
	docker buildx build --platform $(ARCHS) -t $(REPO):$(TAG) .

# Build and push the Docker image for multiple architectures
.PHONY: build-push
build-push: build
	docker buildx build --platform $(ARCHS) -t $(REPO):$(TAG) --push .

# Clean up the builder instance
.PHONY: clean
clean:
	docker buildx rm $(BUILDER_NAME)