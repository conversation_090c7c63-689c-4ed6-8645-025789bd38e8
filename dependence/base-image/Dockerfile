FROM python:3.9.18-slim-bullseye

MAINTAINER "AICP Group"

ARG TARGETARCH

RUN case "${TARGETARCH}" in \
    amd64) ARCH="x86_64" ;; \
    arm64) ARCH="aarch64" ;; \
    ppc64le) ARCH="ppc64le" ;; \
    *) echo "Unsupported architecture: ${TARGETARCH}"; exit 1 ;; \
    esac \
    && apt update -y && apt install curl -y \
    && curl  https://dl.min.io/client/mc/release/linux-${TARGETARCH}/mc -k -o /usr/bin/mc \
    && chmod +x /usr/bin/mc