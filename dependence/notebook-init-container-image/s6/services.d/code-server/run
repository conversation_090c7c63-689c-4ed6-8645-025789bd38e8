#!/command/with-contenv bash

# if command code-server not found, install it
if ! command -v code-server &> /dev/null; then
  # if release is ubuntu , use apt install code-server, else if release is centos, use yum install code-server
  if [ -f /etc/lsb-release ]; then
    echo "install code-server for ubuntu"
    dpkg -i "$INIT_PATH"/install/code-server_4.22.1_amd64.deb
  elif [ -f /etc/redhat-release ]; then
    echo "install code-server for centos"
    rpm -i "$INIT_PATH"/install/code-server_4.22.1_amd64.rpm
  else
    echo "not install code-server for other"
  fi
fi

cd "${HOME}"
exec code-server \
  --bind-addr 0.0.0.0:8889 \
  --disable-telemetry \
  --disable-update-check \
  --disable-workspace-trust \
  --disable-getting-started-override \
  --auth none \
  "${HOME}"