FROM ubuntu:22.04

RUN apt-get update && apt-get install -y curl wget \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

WORKDIR notebook-init-container-files

RUN mkdir ./install \
    && wget "https://github.com/coder/code-server/releases/download/v4.22.1/code-server_4.22.1_amd64.deb" \
        -O ./install/code-server_4.22.1_amd64.deb \
    && wget "https://github.com/coder/code-server/releases/download/v4.22.1/code-server-4.22.1-amd64.rpm" \
        -O ./install/code-server_4.22.1_amd64.rpm

ADD ./libs/s6-overlay-noarch.tar.xz ./
ADD ./libs/s6-overlay-x86_64.tar.xz ./
COPY ./script/ ./script/
COPY ./s6/. ./etc/
COPY ./tmp_home/ ./tmp_home/
RUN rm -f ./init && chmod 755 ./script/aicp-init

ENTRYPOINT ["./script/aicp-init"]